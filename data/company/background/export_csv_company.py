import ast
import csv
import json
import traceback
from io import String<PERSON>
from typing import Optional, Dict, Any
from datetime import timed<PERSON><PERSON>
from pydantic import BaseModel, Field

from django.core.mail import EmailMessage
from django.db.models import Q
from django.template.loader import render_to_string
from django.utils import timezone

from data.constants.date_constant import *
from data.constants.properties_constant import *
from data.models import *
from django.db import models
from utils.contact import get_company_downloadable_value
from utils.date import format_date_from_workspace
from utils.filter import build_view_filter
from utils.utility import is_valid_uuid, remove_unsupported_characters
from utils.logger import logger
from utils.bgjobs.hatchet_client import hatchet
from hatchet_sdk import Context
from utils.bgjobs.handler import set_bg_job_running, set_bg_job_completed, set_bg_job_failed


class ExportCSVCompanyPayload(BaseModel):
    user_id: str
    workspace_id: str
    view_id: Optional[str] = None
    history_id: Optional[str] = None
    columns: Optional[str] = None
    filter_dictionary: Optional[str] = None
    encoded_format: Optional[str] = Field(default="utf-8")
    record_ids: Optional[str] = None
    language: Optional[str] = Field(default="ja")
    target: Optional[str] = Field(default=TYPE_OBJECT_COMPANY)
    background_job_id: Optional[str] = Field(default="")
    
    # Backward compatibility (Deprecated soon)
    function: Optional[str] = Field(default="")
    workspace: Optional[str] = Field(default="")
    job_id: Optional[str] = Field(default="")
    payload: Optional[dict] = Field(default_factory=dict)

@hatchet.task(name="ExportCSVCompany", input_validator=ExportCSVCompanyPayload, execution_timeout=timedelta(hours=12), schedule_timeout=timedelta(hours=1))
def export_csv_company(input: ExportCSVCompanyPayload, ctx: Context):
    user_id = input.user_id
    workspace_id = input.workspace_id or input.workspace
    view_id = input.view_id
    history_id = input.history_id
    columns = input.columns
    encoded_format = input.encoded_format
    lang = input.language
    target = input.target
    
    if input.background_job_id or input.job_id:
        set_bg_job_running(input.background_job_id or input.job_id)

    filter_dictionary = input.filter_dictionary
    try:
        if filter_dictionary:
            filter_dictionary = json.loads(filter_dictionary)
        else:
            filter_dictionary = {}
    except Exception as e:
        logger.warning(
            f"[DEBUG] - export_csv_company - Error in parsing filter_dictionary: {e}")
        filter_dictionary = {}

    try:
        history = TransferHistory.objects.get(id=history_id)
    except TransferHistory.DoesNotExist:
        logger.warning("[DEBUG] - export_csv_company - TransferHistory does not exist")
        if input.background_job_id or input.job_id:
            set_bg_job_failed(input.background_job_id or input.job_id)
        return

    try:
        workspace = Workspace.objects.get(id=workspace_id)
    except Workspace.DoesNotExist:
        logger.warning("[DEBUG] - export_csv_company - Workspace does not exist")
        history.status = 'canceled'
        history.save()
        if input.background_job_id or input.job_id:
            set_bg_job_failed(input.background_job_id or input.job_id)
        return

    try:
        user = User.objects.get(id=int(user_id))
    except User.DoesNotExist:
        logger.warning("[DEBUG] - export_csv_company - User does not exist")
        history.status = 'canceled'
        history.save()
        if input.background_job_id or input.job_id:
            set_bg_job_failed(input.background_job_id or input.job_id)
        return

    try:
        if not columns:
            if lang == 'ja':
                msg = "列を選択してください"
            else:
                msg = "Please select a column"

            Notification.objects.create(
                workspace=workspace, user=user, type="error", message=msg)
            history.status = 'canceled'
            history.save()
            return

        csv_buffer = StringIO()
        writer = csv.writer(csv_buffer, quoting=csv.QUOTE_MINIMAL)

        columns = columns.split(",")
        columns.insert(0, 'company_id')

        custom_fields = []
        raw_columns = columns.copy()
        localize_column = []
        for column in columns:
            if column in COMPANY_COLUMNS_DISPLAY:
                column = COMPANY_COLUMNS_DISPLAY[column][lang]
                col = column
            elif "| child" in column:
                if lang == "ja":
                    col = column.replace("child", "子供")
            elif is_valid_uuid(column):
                try:
                    custom_field = CompanyNameCustomField.objects.get(
                        id=column)
                    col = custom_field.name
                    custom_fields.append(custom_field.name)
                except:
                    col = column
            if " | child" in col:
                try:
                    custom_field = CompanyNameCustomField.objects.get(
                        id=col.split(' | ')[0])
                    col = f"{custom_field.name} | {'子オブジェクト' if lang == 'ja' else 'Child Objects'}"
                except Exception as e:
                    logger.warning(
                        f'[DEBUG] - export_csv_company - Error getting custom field: {e}')

            localize_column.append(col)

        # Filter out unsupported characters for Shift-JIS encoding only
        if lang == 'ja' and encoded_format == 'shift-jis':
            localize_column = [remove_unsupported_characters(
                col) for col in localize_column]

        writer.writerow(localize_column)

        config_view = None
        if view_id:
            view = View.objects.filter(
                id=view_id, workspace=workspace).first()
            if not view:
                logger.warning("[DEBUG] - export_csv_company - View does not exist")
                history.status = 'canceled'
                history.save()
                if input.background_job_id or input.job_id:
                    set_bg_job_failed(input.background_job_id or input.job_id)
                return

            view_filter = ViewFilter.objects.filter(view=view).first()
            config_view = view_filter.view_type
        else:
            view, _ = View.objects.get_or_create(
                workspace=workspace, target=target, title__isnull=True)
            view_filter = ViewFilter.objects.filter(view=view).first()
            config_view = view_filter.view_type

        companies_columns = ast.literal_eval(view_filter.column)
        companies_columns.insert(0, 'checkbox')
        companies_columns.insert(1, 'company_id')
        companies_columns = [data.lower() for data in companies_columns]

        if not config_view:
            config_view = 'list'

        filter_conditions = Q(workspace=workspace, status='active')

        if filter_dictionary:
            view_filter.filter_value = filter_dictionary

        filter_conditions = build_view_filter(
            filter_conditions, view_filter, TYPE_OBJECT_COMPANY)

        if input.record_ids:
            ids = json.loads(input.record_ids)
            filter_conditions &= Q(id__in=ids)

        companies = Company.objects.filter(filter_conditions).order_by(
            'company_id', '-created_at').distinct()
        custom_field_ids = CompanyNameCustomField.objects.filter(
            workspace=workspace).values_list('id', flat=True)
        custom_field_ids = [str(v) for v in custom_field_ids]

        query_columns = ['id', 'workspace_id']
        for col in columns:
            if col in [field.name for field in Company._meta.fields]:
                query_columns.append(col)

        companies = companies.values(*query_columns)

        merged_companies_list = companies
        history.total_number = len(merged_companies_list)
        for i, company in enumerate(merged_companies_list):
            try:
                row = []
                for column in columns:
                    data = get_company_downloadable_value(
                        company, column, custom_field_ids, workspace.timezone, lang)
                    row.append(data)

                # Clean newlines and other problematic characters from CSV data
                row = [str(cell).replace('\n', ' ').replace(
                    '\r', ' ').strip() if cell else cell for cell in row]

                if lang == 'ja' and encoded_format == 'shift-jis':
                    row = [remove_unsupported_characters(
                        str(cell)) if cell else cell for cell in row]

                writer.writerow(row)

                history.success_number += 1
            except:
                logger.info(
                    f"[DEBUG] - export_csv_company - Error processing company data: {traceback.format_exc()}")
                history.failed_number += 1

        csv_data = csv_buffer.getvalue()

        # Encode the CSV data based on the specified format
        if encoded_format == 'shift-jis':
            try:
                csv_data = csv_data.encode('shift-jis')
            except UnicodeEncodeError as e:
                logger.warning(
                    f"[DEBUG] - export_csv_company - Shift-JIS encoding error: {e}")
                # Fallback: encode with error handling to replace problematic characters
                csv_data = csv_data.encode('shift-jis', errors='replace')
        else:
            # For UTF-8 and other encodings, encode directly
            csv_data = csv_data.encode(encoded_format)

        if lang == 'ja':
            curr_datetime = format_date_from_workspace(
                timezone.now(), workspace)
            filename = f'Sanka_会社_{curr_datetime}.csv'
            mail_subject = 'Sanka - エクスポートCSV - 会社'
            message = render_to_string('data/email/export-orders-ja.html')
        else:
            curr_datetime = format_date_from_workspace(
                timezone.now(), workspace)
            filename = f'Sanka_Export_Companies_{curr_datetime}.csv'
            mail_subject = 'Sanka - Export CSV - Companies'
            message = render_to_string('data/email/export-orders.html')

        from_email = 'Sanka <<EMAIL>>'
        to_email = [user.email]

        email_message = EmailMessage(
            mail_subject,
            message,
            from_email,
            to_email
        )

        mimetype = 'text/csv'
        email_message.attach(filename, csv_data, mimetype)
        email_message.send(fail_silently=False)

        history.name = f'Export Companies: {filename}'
        history.progress = round(
            history.success_number / history.total_number * 100)
        history.status = 'completed'
        history.save()
        
        if input.background_job_id or input.job_id:
            set_bg_job_completed(input.background_job_id or input.job_id)

    except Exception as e:
        if lang == 'ja':
            Notification.objects.create(
                workspace=workspace, user=user, message=f"会社のエクスポートに失敗しました。サポートにお問い合わせください。 |{e}", type="error")
        else:
            Notification.objects.create(
                workspace=workspace, user=user, message=f"Export Companies Failed. Please contact support. | {e}", type="error")

        logger.error(f"Export Failed : {str(traceback.format_exc())}")

        history.name = 'Export Companies'
        history.status = 'failed'
        history.progress = 0
        history.save()
        
        if input.background_job_id or input.job_id:
            set_bg_job_failed(input.background_job_id or input.job_id)
