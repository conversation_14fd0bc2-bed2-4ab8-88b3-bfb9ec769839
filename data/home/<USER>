import urllib.parse
from datetime import datetime
import stripe
from django.contrib.auth import login
from django.core.paginator import EmptyP<PERSON>, Paginator
from django.db.models import Max, OuterRef, Q, Subquery
from django.http import HttpResponse
from django.shortcuts import redirect, render
from django.utils import timezone
from django.utils.timezone import make_aware
from django_hosts.resolvers import reverse
from data.constants.properties_constant import *
from data.models import (
    Notification, Integration, Channel, Verification, Page2, Workspace,
    User, Solution, Widget, Report, ReportPanel, MessageThread, OBJECT_SLUG_TO_OBJECT_TYPE,
    Workflow, Module, View, ViewFilter, BasePricing, MONTHLY, HOLIDAY_COUNTRY_CODE, OBJECT_GROUP_TYPE,
    DEFAULT_PERMISSION, Message, Invitation,
)
from utils.decorator import login_or_hubspot_required
from utils.filter import build_view_filter
from utils.logger import logger
from utils.properties.properties import (get_page_object,
                                         get_properties_with_details)
from utils.stripe.stripe import *
from utils.utility import (check_if_int_castable, check_staff, get_workspace,
                           modular_view_filter)
from utils.workspace import create_workspace, get_permission

LANGUAGE_QUERY_PARAMETER = 'language'
POSTS_PER_PAGE = 12
POSTS_PER_PAGE_BEGIN = POSTS_PER_PAGE - 1

# Search performance optimization constants
SEARCH_RESULTS_LIMIT_PER_TYPE = 10  # Maximum results per object type in global search

@login_or_hubspot_required
def main(request):

    lang = request.LANGUAGE_CODE
    if lang == 'ja':
        page_title = 'ホーム'
    else:
        page_title = 'Home'
    menu_key = 'home'

    page_type = request.GET.get("page_type")

    if page_type == None or page_type == 'dashboard':
        page_type = 'dashboard'

    if lang == 'ja':
        articles = Page2.objects.filter(
            ja=True, status='published').order_by('-update_date')[:12]
    else:
        articles = Page2.objects.filter(
            en=True, status='published').order_by('-update_date')[:12]

    workspace = get_workspace(request.user)
    if not workspace:
        return redirect(reverse('start', host='app'))

    if workspace.subscription_status == 'preactive':
        monthly_base_pricings = BasePricing.objects.filter(
            is_base_plan=False, payment_frequency=MONTHLY)
        base_plan_pricings = BasePricing.objects.filter(is_base_plan=True)

        solution_options = Solution.objects.filter(
            module=True, module_values__isnull=False).order_by('order')
        context = {
            'page_title': page_title,
            'page_cat': "no_sidebar",
            'credit_card_attached': request.GET.get("credit-card-attached", None),
            'monthly_base_pricings': monthly_base_pricings,
            'base_plan_pricings': base_plan_pricings,
            'solution_options': solution_options,
            'COUNTRY_CODE': HOLIDAY_COUNTRY_CODE,
        }
        return render(request, 'data/home/<USER>', context)

    # Checking Subscription
    if workspace.subscription == 'active':
        try:
            subscription = stripe.Subscription.retrieve(
                workspace.stripe_subscription)
            status = subscription.status
            if status == 'unpaid':
                today = timezone.now().timestamp()
                subscription_end = make_aware(datetime.utcfromtimestamp(
                    int(subscription.current_period_end))).timestamp()

                if today > subscription_end:
                    stripe.Subscription.delete(workspace.stripe_subscription)
                    workspace.subscription = 'expired'
                    workspace.save()
                    return redirect(reverse('start', host='app'))
        except:
            pass

    if workspace.subscription == 'canceled':
        try:
            subscription = stripe.Subscription.retrieve(
                workspace.stripe_subscription)
            status = subscription.status

            if status == 'trialing':
                today = timezone.now().timestamp()
                subscription_end = make_aware(datetime.utcfromtimestamp(
                    int(subscription.trial_end))).timestamp()
            else:
                today = timezone.now().timestamp()
                subscription_end = make_aware(datetime.utcfromtimestamp(
                    int(subscription.current_period_end))).timestamp()

            if today > subscription_end:
                workspace.subscription = 'expired'
                workspace.save()
                return redirect(reverse('start', host='app'))
        except:
            pass

    q = request.GET.get("q")

    # if user is admin, show all workflow
    if check_staff(request.user, workspace):
        templates = Workflow.objects.filter(
            created_by_sanka=True).exclude()[:12]
    else:
        templates = Workflow.objects.filter(
            created_by_sanka=True, status='published').exclude(status='draft')[:12]

    lang = request.LANGUAGE_CODE
    # for timezone etc.
    verification = Verification.objects.get(user=request.user)
    email_verified = verification.verified

    integrations = Integration.objects.filter()

    workflows = None

    featured_articles = Page2.objects.filter(featured=True, status='published')
    if lang == 'ja':
        featured_articles = featured_articles.filter(ja=True)
    else:
        featured_articles = featured_articles.filter(en=True)

    featured_articles = featured_articles.order_by('order')[:4]

    widgets = Widget.objects.filter(workspace=workspace).order_by('order')

    default_widgets = ['onboarding']

    for widget in widgets:
        for i, df_widget in enumerate(default_widgets):
            if widget.special_type == df_widget:
                del default_widgets[i]
                break
    print(f' === home.py - 1749: Default Widgets: {default_widgets}')
    order = 0
    for df_widget in default_widgets:
        widget, _ = Widget.objects.get_or_create(
            workspace=workspace, order=order, special_type=df_widget)
        order += 1

    widgets = Widget.objects.filter(
        workspace=workspace, is_deleted=False).order_by('order')
    start_solution, _ = Solution.objects.get_or_create(slug='start')

    if lang == 'ja':
        start_helps = Page2.objects.filter(
            ja=True, solution=start_solution).order_by('order')[:4]
    else:
        start_helps = Page2.objects.filter(
            en=True, solution=start_solution).order_by('order')[:4]

    # onboarding_start = request.GET.get('onboard_start', None)

    context = {
        'page_type': page_type,
        'menu_key': menu_key,
        'page_title': page_title,
        'email_verified': email_verified,
        'templates': templates,
        'articles': articles,
        'q': q,
        'integrations': integrations,
        'user_timezone': workspace.timezone,
        'from': 'home',  # for no redirect on workflow and task flow using htmx
        'workflows': workflows,
        'featured_articles': featured_articles,
        'widgets': widgets,
        'start_helps': start_helps,
    }

    return render(request, 'data/home/<USER>', context)

@login_or_hubspot_required
def notifications(request, page=None):
    workspace = get_workspace(request.user)
    try:
        if not page:
            page = 1
        notifications = Notification.objects.filter(
            workspace=workspace, user=request.user, mark_as_read=False).order_by('-created_at')

        has_next_page = False
        if notifications:
            paginator = Paginator(notifications, 10)
            try:
                page_content = paginator.page(page)
                notifications = page_content.object_list
                has_next_page = page_content.has_next()
                page += 1
            except EmptyPage:
                notifications = []
                page = None
        else:
            page = None
    except:
        notifications = []
        page = 1
        has_next_page = False

    context = {
        'notifications': notifications,
        'next_page': page,
        'has_next_page': has_next_page
    }
    return render(request, 'data/notifications/notifications.html', context)


@login_or_hubspot_required
def notifications_number(request):
    notifications_number = len(Notification.objects.filter(workspace=get_workspace(
        request.user), user=request.user, mark_as_read=False).values_list('id'))
    context = {'notifications_number': notifications_number}
    return render(request, 'data/notifications/notifications-number.html', context)


@login_or_hubspot_required
def delete_notification(request, id):
    n = Notification.objects.get(id=id)
    n.delete()
    response = HttpResponse(status=200)
    response['HX-Trigger-After-Swap'] = "reloadNotifNumber"
    return response


@login_or_hubspot_required
def bulk_delete_notifications(request):
    Notification.objects.filter(
        user=request.user, mark_as_read=False).update(mark_as_read=True)
    response = HttpResponse(status=200)
    response['HX-Trigger-After-Swap'] = "reloadNotifNumber"
    return response

@login_or_hubspot_required
def start(request):
    workspace = get_workspace(request.user)
    if workspace:
        if workspace.subscription in ['free']:
            return redirect(reverse('main', host='app'))

    if workspace:
        workspaces = Workspace.objects.filter(
            user=request.user).exclude(id=workspace.id)
    else:
        workspaces = None

    invited_workspaces = Invitation.objects.filter(
        email=request.user.email, status='invited')

    lang = request.LANGUAGE_CODE
    if lang == 'ja':
        page_title = 'スタート'
    else:
        page_title = 'Start'
    page_group_type = 'start'
    page_cat = 'no_sidebar'
    context = {
        'page_title': page_title,
        'page_group_type': page_group_type,
        '': True,
        'page_cat': page_cat,
        'workspaces': workspaces,
        'invited_workspaces': invited_workspaces
    }
    return render(request, 'start.html', context)


@login_or_hubspot_required
def start_workspace(request):

    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    if request.method == 'POST':
        workspace_title = request.POST.get('workspace_title', '')
        if not workspace_title:
            if lang == 'ja':
                workspace_title = 'デフォルトワークスペース'
            else:
                workspace_title = 'Sanka Default Workspace'

        if lang == 'ja':
            workspace_currency = 'jpy'
        else:
            workspace_currency = 'usd'

        workspace_description = ''

        # Create Workspace Module
        create_workspace(request,
                         workspace_title,
                         workspace_description,
                         workspace_currency
                         )

        if request.LANGUAGE_CODE == 'ja':
            Notification.objects.create(workspace=get_workspace(
                request.user), user=request.user, message="新しいワークスペースが作られました。", type="success")
        else:
            Notification.objects.create(workspace=get_workspace(
                request.user), user=request.user, message="A new Workspace was created.", type="success")

    return redirect(reverse('main', host='app'))

@login_or_hubspot_required
def search(request):
    workspace = get_workspace(request.user)
    if not workspace:
        return redirect(reverse('start', host='app'))
    lang = request.LANGUAGE_CODE
    if lang == 'ja':
        page_title = '検索'
    else:
        page_title = 'Search'

    menu_key = 'search'
    # module = Module.objects.get(slug=module_slug, workspace=workspace)
    modules = Module.objects.filter(workspace=workspace)
    object_slugs = [x for x in OBJECT_SLUG_TO_OBJECT_TYPE]
    map_module_by_object_values = {}
    for module in modules:
        map_module_by_object_values[module.object_values] = module.slug

    map_module_slug_by_object_value = {}
    for obj in object_slugs:
        val_map = None
        for key, value in map_module_by_object_values.items():
            if OBJECT_SLUG_TO_OBJECT_TYPE.get(obj) in key.split(','):
                val_map = value
                break

        map_module_slug_by_object_value[obj] = val_map

    helps = Page2.objects.filter(category='help').order_by('-created_at')
    reports = Report.objects.filter(
        is_template=False, workspace=workspace, channel=None, is_deleted=False).order_by('-created_at')
    panels = ReportPanel.objects.filter(
        is_template=False, is_deleted=False, workspace=workspace).order_by('-created_at')

    objects_map = {}
    all_objects = TYPE_OBJECTS
    for obj in all_objects:
        page_obj = get_page_object(obj, lang)
        base_model = page_obj['base_model']

        # Skip objects with no base_model (not implemented in get_page_object)
        if base_model is None:
            continue

        # Handle models that don't have direct workspace field
        try:
            # Try direct workspace filter first
            obj_model = base_model.objects.filter(workspace=workspace)
        except Exception:
            # Handle special cases for models without direct workspace field
            if obj == 'subscription_platform':
                # ShopTurboSubscriptionPlatforms is related through source_subscription
                obj_model = base_model.objects.filter(source_subscription__workspace=workspace)
            else:
                # For other models without workspace field, return empty queryset
                obj_model = base_model.objects.none()

        objects_map[obj] = {'target': obj, 'objects': obj_model}

    permission_object = [permission for permission in OBJECT_GROUP_TYPE]
    permissions = {}
    for obj in permission_object:
        permission = get_permission(object_type=obj, user=request.user)
        if not permission:
            permission = DEFAULT_PERMISSION

        permissions[obj] = permission

    q = request.GET.get('q')
    id = request.GET.get('id')
    page_object = request.GET.get('target')
    if q:
        q = urllib.parse.unquote_plus(q)
        q = q.strip()  # Delete Space at the front and at the end.
        q_number = None
        if check_if_int_castable(q):
            q_number = int(q)

        logger.info(q)

        helps = helps.filter(Q(title__icontains=q) | Q(body__icontains=q) | Q(
            title_ja__icontains=q) | Q(body_ja__icontains=q)).distinct()[:SEARCH_RESULTS_LIMIT_PER_TYPE]
        # Enhanced search for reports - search across multiple fields
        reports = reports.filter(
            Q(name__icontains=q) |
            Q(type__icontains=q) |
            Q(metrices__icontains=q)
        ).distinct()[:SEARCH_RESULTS_LIMIT_PER_TYPE]

        # Enhanced search for panels - search across multiple fields
        panels = panels.filter(
            Q(name__icontains=q) |
            Q(data_source_type__icontains=q) |
            Q(panel_type__icontains=q) |
            Q(metrices__icontains=q)
        ).distinct()[:SEARCH_RESULTS_LIMIT_PER_TYPE]

        # Annotate the last_message body
        latest_messages = Message.objects.filter(
            thread=OuterRef('pk')
        ).order_by('-sent_at')

        message_threads = MessageThread.objects.filter(workspace=workspace).annotate(
            last_message_date=Max("message__sent_at"),
            last_message_body=Subquery(latest_messages.values('body')[:1])
        ).filter(
            Q(last_message_date__isnull=False) &
            Q(
                Q(**{"last_message_body__icontains": q})
            )
        ).order_by('-last_message_date')[:SEARCH_RESULTS_LIMIT_PER_TYPE]
        # Process search queries in parallel batches for better performance
        from concurrent.futures import ThreadPoolExecutor, as_completed
        import threading
        
        def search_object_type(key, target_obj):
            target = target_obj['target']
            objects = target_obj['objects']

            # Early check - if no objects in queryset, skip processing
            if not objects.exists():
                return key, []

            page_obj = get_page_object(target, lang)
            id_field = page_obj['id_field']

            # Limit searchable properties for better performance
            if target == 'contacts':
                s_properties = get_properties_with_details(target, workspace, lang, searchable_only=True, excludes=[
                                                           'image_url'], includes=[{'id': 'name'}])
            elif target == 'estimates':
                s_properties = get_properties_with_details(target, workspace, lang, excludes=[
                                                           'invoice'], searchable_only=True)
            elif target == TYPE_OBJECT_CASE:
                s_properties = get_properties_with_details(target, workspace, lang, excludes=[
                                                           'invoice', 'estimate', 'tasks'], searchable_only=True)
            elif target == TYPE_OBJECT_CAMPAIGN:
                s_properties = get_properties_with_details(target, workspace, lang, excludes=[
                                                           'channel'], searchable_only=True)
            else:
                s_properties = get_properties_with_details(
                    target, workspace, lang, searchable_only=True)
            
            # Limit to first 3 most relevant fields for performance
            s_field_names = [p['id'] for p in s_properties[:3]]
            if id_field:
                s_field_names.insert(0, id_field)

            conds = Q()
            for s_name in s_field_names:
                try:
                    dummy_view_filter = ViewFilter(
                        view=View(workspace=workspace),
                        filter_value={
                            s_name: {
                                'key': 'contains',
                                'value': q
                            }
                        }
                    )
                    conds |= build_view_filter(Q(), dummy_view_filter, target)
                except Exception as e:
                    print(f'... ERROR === home.py search for {target}: {e}')

            try:
                # Early limit for performance - only fetch what we need
                result = objects.filter(conds).distinct()[:SEARCH_RESULTS_LIMIT_PER_TYPE]
                return key, list(result)  # Convert to list to avoid lazy evaluation issues
            except Exception as e:
                print(f'... ERROR === key: {key} conds: {conds} ==> {e}')
                return key, []

        # Execute searches with a reasonable thread pool size
        max_workers = min(len(objects_map), 5)  # Limit concurrent DB connections
        search_results = {}
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_key = {
                executor.submit(search_object_type, key, target_obj): key 
                for key, target_obj in objects_map.items()
            }
            
            for future in as_completed(future_to_key):
                key, results = future.result()
                objects_map[key]['objects'] = results

    else:
        # redirect to home
        return redirect(reverse('main', host='app'))

    # Check if any results were found across all object types
    has_any_results = (
        bool(helps) or bool(reports) or bool(panels) or bool(message_threads) or
        any(bool(value['objects']) for value in objects_map.values())
    )

    context = {
        'page_title': page_title,
        'q': q,
        'menu_key': menu_key,
        'helps': helps,
        'reports': reports,
        'panels': panels,
        'message_threads': message_threads,
        'permissions': permissions,
        'map_module_slug_by_object_value': map_module_slug_by_object_value,
        'target': page_object,
        'id': id,
        'has_any_results': has_any_results,
        **{key: value['objects'] for key, value in objects_map.items()}
    }

    return render(request, 'data/home/<USER>', context)
