{% extends 'base.html' %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}
{% block content %}


<div class="w-100" id="reports-table">
    <div class="mb-15 gx-5">
        <div class="d-flex align-items-end mb-2 align-items-center" style="background-color: #FAFAFA !important;">
            {% include 'data/common/module-header-tabs.html' %}
            {% include 'data/reports/view-menu-new-design.html' %}
        </div>
        {% if permission == 'hide' %}
            {% include 'data/static/no-access-page.html' %}
        {% else %}
        <div class="px-lg-10 px-5">
            {% include 'data/common/permission-action-warning-message.html' %}
            {% include 'data/reports/view-menu.html' %}

            {% if object_type == 'dashboards' %}
                {% include 'data/reports/report-table-container.html' %}
            {% elif object_type == 'panels' %}
                {% include 'data/reports/panel-table-container.html' %}
            {% endif %}

        </div>
        {% endif %}
    </div>
</div>

<style>
    .reports_loads{
        display:none;
    }
    .htmx-request .reports_loads{
        display:inline;
    }
    .htmx-request.reports_loads{
        display:inline;
    } 
</style>

<script>
    function toggleText() {
        var x = document.getElementById("select-additional-options-toggle");
        var toggle_data = x.getAttribute('toggle-data')
        if (toggle_data !== "true") {
            
            {% if LANGUAGE_CODE == 'ja'%}
            x.innerHTML = "選択を解除";
            {% else %}
            x.innerHTML = "Clear All";
            {% endif %}

          $(".flag_all").each(function(index, element) {
                element.value = true
            });

            x.setAttribute('toggle-data',"true")

        } else {

            x.setAttribute('toggle-data',"false")

            addcontactelem = document.getElementById("update-report");
            addcontactelem.classList.add("disabled");
 
            $('input[type=checkbox]').prop('checked', false);

            //Hide
            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.add("d-none")

            x.innerHTML = "{% if LANGUAGE_CODE == 'ja'%}すべて選択 ({{paginator.count}}) このセクションの連絡先{%else%}Select All ({{paginator.count}}) contacts in this sections{%endif%}";
            
            $(".flag_all").each(function(index, element) {
                element.value = false
            });

        }
      }

    const checking_checkbox = (elem,event) => {
        if (elem.checked) {
            document.getElementById('panel-bulk-action-container').classList.remove('d-none')
            document.getElementById('panel-view-container').classList.add('tw-hidden')
        } else {
            taskSelections = document.getElementsByClassName('panel-selection')
            for (let i = 0; i < taskSelections.length; i++) {
                const element = taskSelections[i];
                if (element.checked) {
                    return
                }
            }
            document.getElementById('panel-bulk-action-container').classList.add('d-none')
            document.getElementById('panel-view-container').classList.remove('tw-hidden')
        }
    }

    function check_permission_action(event, permission_type, ...args){
        let source = args.length > 0 ? args[0] : null;
        
        const checkInputs = document.querySelectorAll('.check_input:checked');

        let members = "{{group_members}}"
        members = members.split(',')
        const user_id = '{{request.user.id}}'
        const permission = '{{permission}}';
        const permission_list = permission.split('|');
        let scope = ''
        permission_list.forEach(p => {
            if (p.includes(permission_type)) {
                p_split = p.split('_');
                scope = p_split[0]
            }
        })

        let msg = '';
        let denied = false;
        for (let i = 0; i < checkInputs.length; i++) {
            var owner_id = checkInputs[i].dataset.owner;
            if (owner_id){
                if (scope == 'user'){
                    if (owner_id.toString() !== user_id.toString()) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分のアイテムのみ編集または削除することができます。";
                        {% else %}
                        msg = "Action denied. You are only allowed to edit or delete your own items.";
                        {% endif %}                    
                        checkInputs[i].click()
                        denied = true;
                    }
                } else if (scope == 'team'){
                    if (!members.includes(owner_id.toString())) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分または自分のチームに割り当てられたアイテムのみ編集または削除できます。";
                        {% else %}
                        msg = "Action denied. You can only edit or delete items assigned to you or your team.";
                        {% endif %}
                        checkInputs[i].click()
                        denied = true;
                    }
                } 
            }
        }
        if (denied) {
            event.preventDefault();
            event.stopImmediatePropagation();
            document.getElementById('permissionActionWarning').innerHTML = msg;
            setTimeout(() => {
                document.getElementById('permissionActionWarning').innerHTML = '';
            }, 4000);
            msg = ''
        } else if (source){
            const modalEl = document.getElementById(source);
            const modal = bootstrap.Modal.getOrCreateInstance(modalEl);
            modal.show();
        }
        
    }
</script>


{% endblock content %}