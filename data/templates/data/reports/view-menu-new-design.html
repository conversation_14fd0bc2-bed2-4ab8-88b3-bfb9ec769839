{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}

<div class="w-50 d-flex justify-content-end tw-mr-5" id="view-container">
    {% if permission|check_permission:'edit' %}
        <div class="{% include "data/utility/table-button.html" %} btn-group tw-h-[32px]">
            <button 
                {% if object_type == 'panels' %}
                class="tw-w-[100px] align-items-center d-flex btn btn-primary btn-md py-1 create-panel-button" type="button"
                hx-get="{% host_url 'panel_form' host 'app'  %}?module={{menu_key}}" 
                hx-trigger="click" 
                hx-target="#panel-wizard" 
                hx-swap="innerHTML"
                style="border-radius: 0.475rem 0.475rem 0.475rem 0.475rem;"
                {% else %}
                class="tw-w-[100px] align-items-center d-flex btn btn-primary btn-md py-1 create-report-button" type="button"
                hx-get="{% host_url 'report_form' host 'app'  %}" 
                hx-vals='{"type": "create", "object_type": "dashboards"}' 
                hx-trigger="click" 
                hx-target="#report-wizard" 
                hx-swap="innerHTML"
                style="border-radius: 0.475rem 0.475rem 0.475rem 0.475rem;"
                {% endif %}>
                <span class="svg-icon svg-icon-4">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"/>
                        <rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"/>
                        <rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"/>
                    </svg>
                </span>
        
                <span class="fs-7 ps-1 fw-bolder w-85px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    新規
                    {% else %}
                    New
                    {% endif %}
                </span>
            </button> 
        </div>
    {% endif %}
</div>