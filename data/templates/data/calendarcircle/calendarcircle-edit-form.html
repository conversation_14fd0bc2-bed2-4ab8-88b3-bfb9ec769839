{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}    


<div id="calendar_form" class="calendar-form">
    <div class="card-header" id="kt_help_header">
        <h5 class="{% include "data/utility/card-header.html" %}">
            {% if not event %} 
                {% if LANGUAGE_CODE == 'ja'%}
                スケジューラーを作成
                {% else %}
                Create Scheduler
                {% endif %}

            {% else %}
            
                {% if LANGUAGE_CODE == 'ja'%}
                スケジューラーを編集
                {% else %}
                Edit Scheduler
                {% endif %}
            {%endif%}
        </h5>
        <div class="card-toolbar">
            {% if event %}
            <script>
                KTMenu.createInstances()
            </script>
            <button class="d-flex btn bg-white opacity-75-hover text-start w-100 py-3 ps-2 me-n5" type="button" data-kt-menu-trigger="click" data-kt-menu-placement="left">
                <span class="svg-icon svg-icon-2x"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/General/Other1.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <rect x="0" y="0" width="24" height="24"/>
                        <circle fill="#000000" cx="12" cy="5" r="2"/>
                        <circle fill="#000000" cx="12" cy="12" r="2"/>
                        <circle fill="#000000" cx="12" cy="19" r="2"/>
                    </g>
                </svg><!--end::Svg Icon--></span>
            </button>

            <div class="docs-aside-select-menu menu menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-active-bg fw-semibold py-3 w-175px" data-kt-menu="true">
                <div class="menu-item cursor-pointer px-3" data-bs-toggle="tooltip" data-bs-placement="right" data-kt-initialized="1">
                    <a onclick="copy(this)" id="kt_clipboard" class="btn btn-copy btn-light-primary d-flex justify-content-center" 
                        data-clipboard-text="{{type_http}}{% host_url 'calendar_attend' slug=event.slug url=event.urls %}">
                        {% translate_lang "Copy URL" LANGUAGE_CODE %}
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <div class="card-body">
        <form id="calendar_drawer" method='POST' {% if not event %} action="{% host_url 'new_event_calendar' host 'app' %}" {% else %} action="{% host_url 'edit_event_calendar' id=event.id host 'app' %}" {% endif %} enctype="multipart/form-data">
            {% csrf_token %}
            <input type="hidden" name="create_event" {% if not event %} value="yes" {%else%}value="manage"{% endif %} ></input>
            
            <div class="mb-10 ">  
                <div class="mb-5">  
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span class="required">
                            {% translate_lang "Name" LANGUAGE_CODE %}
                        </span>
                    </label>
                    <input autocomplete="off" required value="{{event.title}}" 
                    placeholder="{% translate_lang "Name" LANGUAGE_CODE %}" 
                    class="form-control w-100" type="text" name="title">
                </div>

                <div class="mb-5 url_check">  
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span class="required">
                            {% if LANGUAGE_CODE == 'ja'%}
                            スケジューラーURL 
                            {% else %}
                            Scheduler URL
                            {% endif %}
                        </span>
                    </label>
                    <input autocomplete="off" required {% if event.url %} value="{{event.url}}" {%elif url %}value="{{url}}"{% endif %} 
                    
                    hx-get="{% host_url 'calendarcircle_check_slug' host 'app' %}"
                    hx-trigger="change"
                    hx-select=".url_check"
                    hx-target=".url_check"
                    hx-swap='outerHTML'
                    hx-include="[name='event_id']"
                    hx-indicator=".loading-url-spinner"
                    id="url-slug"
                    placeholder="URL" 
                    class="form-control w-100 
                        {% if validURL == "valid" %}
                            is-valid
                        {% elif validURL == "invalid" %}
                            is-invalid
                        {% endif %}" 
                    oninput="validateInput(event)" maxlength="50"
                    type="text" name="url">
                    
                    <script>
                        function validateInput(event) {
                            let inputElement = event.target;
                            let inputValue = inputElement.value;
                      
                            // Remove non-alphabet characters using a regular expression
                            let cleanedValue = inputValue.replace(/[^a-zA-Z0-9\s]/g, '');
                      
                            // Update the input value with only alphabet characters
                            inputElement.value = cleanedValue;
                          }
                        var submit_calendar =  document.querySelector('#submit_calendar');
                        var url_slug =  document.querySelector('#url-slug');
                            document.body.addEventListener("validURL", function(evt){
                                console.log("valid");
                                url_slug.classList.add('is-valid');
                                url_slug.classList.remove('is-invalid');
                                submit_calendar.removeAttribute('disabled');
                            })
                            document.body.addEventListener("invalidURL", function(evt){
                                console.log("invalid");
                                var submit_calendar =  document.querySelector('#submit_calendar');
                                url_slug.classList.add('is-invalid');
                                url_slug.classList.remove('is-valid');
                                submit_calendar.setAttribute('disabled', '');
                                
                            })
                    </script>
                    <div class="invalid-feedback">URL "{{url}}" {% translate_lang "is already Taken" LANGUAGE_CODE %}</div>

                    <div class="d-flex justify-content-center">
                        <div class="loading-url-spinner mt-10 mb-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>     
                    </div> 
                </div>
                
                <div id="channel_section" class="w-100 mb-5">
                    {% if available_channels %}
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}
                                同期するカレンダーを選択 
                                {% else %}
                                Select Calendar to Sync
                                {% endif %}
                            </span>
                        </label>
                        <ul class="ps-0">
                    
                            <input type="hidden" name="event_id" value="{% if event %}{{event.id}}{% else %}create_new{% endif %}">
                            <select class="form-select h-40px select2-this" data-control="select2" data-placeholder="Select Channel" id="main-channel-picker"
                                hx-get="{% host_url 'new_event_calendar' host 'app' %}"
                                hx-trigger="htmx-change"
                                hx-include="[name='event_id']"
                                hx-swap="outerHTML"
                                hx-select=".calendar_id"
                                hx-target=".calendar_id"
                                hx-indicator=".loading-spinner"
                                hx-after-request="handleAfterRequest"
                                data-hide-search="true" data-allow-clear="true" name="main_channel">

                                <option value=""></option>

                                {% for channel in available_channels %}
                                    <option {% if channel == event.channel %}selected{% endif %} value="{{ channel.id }}" data-platform={{channel}}>
                                        {{ channel.name }} 
                                    </option>
                                {% endfor %}

                            </select>
                        </ul>

                        <div class="d-flex justify-content-center">
                            <div class="loading-spinner mt-10 mb-5">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>     
                        </div>     

                        <script>
                            function handleAfterRequest(event) {
                                var calendar_id =  document.querySelector('.calendar_id');
                            }
                            $('.select2-this').select2();
                            $('#main-channel-picker').on('select2:select', function (e) {
                                var selectElement = $(this).closest('select').get(0);
                                var calendar_id =  document.querySelector('.calendar_id');
                                selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                                calendar_id.classList.add('d-none');
                            });
                            $('#main-channel-picker').on('select2:unselect', function (e) {
                                // This code will be executed when the user clears the selection
                                var calendar_id =  document.querySelector('.calendar_id');
                                calendar_id.classList.add('d-none');
                            });
                        </script>
                        
                        <div class="w-100 calendar_id">
                            {% if available_calendars %}
                            <label class="{% include 'data/utility/form-label.html' %}">
                                <span class="required">
                                    {% translate_lang "Calendar to check conflicts" LANGUAGE_CODE %}
                                </span>
                            </label>

                            <select multiple class="calendar_check mb-5 form-select rounded-start-0 form-select-solid bg-white selects2-this" data-control="select2" data-hide-search="true" name="calendar_id">
                                {% for calendar in available_calendars %}    
                                    {% if "group.v." not in calendar.id %}
                                    <option {% if not event %} {% if calendar.primary == True %} selected {%endif%} {%else%} {% if calendar.id in event.calendar_id %} selected {%endif%}{%endif%} value="{{ calendar.id }}" data-platform={{calendar.summary}}>
                                        {{ calendar.summary }} 
                                    </option>
                                    {%endif%}
                                {% endfor %}
                            </select>

                            <label class="{% include 'data/utility/form-label.html' %}">
                                <span class="required">
                                    {% translate_lang "Calendar to add appointments" LANGUAGE_CODE %}
                                </span>
                            </label>
                            <select class="calendar_set form-select h-40px selects2-this" data-control="select2" data-hide-search="true" name="calendar_id_add">
                                {% for calendar in available_calendars %}
                                    {% if calendar.accessRole == "owner" or calendar.accessRole == "writer" %}
                                    <option {%if calendar.id == event.calendar_id_add %}selected{%endif%} value="{{calendar.id}}" data-platform={{calendar.id}}>
                                        {{ calendar.summary }} 
                                    </option>
                                    {%endif%}
                                {% endfor %}
                            </select>
                            {%endif%}

                            <script>
                                $('.selects2-this').select2({minimumResultsForSearch: Infinity});
                            </script>
                        </div>
                    {% else %}
                        <ul class="p-10">
                            {% if LANGUAGE_CODE == 'ja'%}
                            連携されたGoogleカレンダーアカウントはありません<a href="{% host_url 'integrations' host 'app' %}">インテグレーションページ</a>からアカウントを追加してください。
                            {% else %}
                            You don't have any connected Google Calendar account. Connect one on <a href="{% host_url 'integrations' host 'app' %}">Integration page</a>.
                            {% endif %}
                        </ul>
                    {% endif %}
                </div>

                
                <div class="mb-5 d-none">  
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span>
                            {% translate_lang "Appointment Slot Description" LANGUAGE_CODE %}
                        </span>
                    </label>
                    <textarea autocomplete="off" 
                    placeholder="{% translate_lang "Appointment Slot Description" LANGUAGE_CODE %}" 
                    class="form-control w-100 emojize-this" type="text" name="description">{% if event.description %}{{event.description}}{%endif%}</textarea>
                </div>

                
                <div class="mb-5">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span class="required">
                            {% if LANGUAGE_CODE == 'ja'%}
                            予約する時間 
                            {% else %}
                            Schedule Duration
                            {% endif %}
                        </span>
                    </label>
                
                    <select class="form-control duration_select h-40px selects2-this" name="duration" data-placeholder="Select Status" onChange="valCheck(this)">  
                        
                        <option value="15 min" {% if event.duration == '15' %}selected{% endif %}>
                            15 {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                        <option value="30 min" {% if event.duration == '30' %}selected{% endif %}>
                            30 {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                        <option value="45 min" {% if event.duration == '45' %}selected{% endif %}>
                            45 {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                        <option value="60 min" {% if not event  %}selected{% endif %} {% if event.duration == '60' %}selected{% endif %}>
                            60 {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                            
                        <option value="75 min" {% if event.duration == '75' %}selected{% endif %}>
                            75 {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                        <option value="90 min" {% if event.duration == '90' %}selected{% endif %}>
                            90 {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                        <option value="105 min" {% if event.duration == '105' %}selected{% endif %}>
                            105 {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                        <option value="120 min" {% if event.duration == '120' %}selected{% endif %}>
                            120 {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                        <option value="0" 
                        {% if event  %}
                            {% if event.duration != '15' %}
                                {% if event.duration != '30' %}
                                    {% if event.duration != '45' %}
                                        {% if event.duration != '60' %}
                                            {% if event.duration != '75' %}
                                                {% if event.duration != '90' %}
                                                    {% if event.duration != '105' %}
                                                        {% if event.duration != '120' %}
                                                            selected
                                                        {% endif %}
                                                    {% endif %}
                                                {% endif %}
                                            {% endif %}
                                        {% endif %}
                                    {% endif %}
                                {% endif %}
                            {% endif %}
                        {% endif %}>
                            Custom</option>
                    </select>
                </div>
                
                <div class="input-group mb-5 custom_duration d-none">
                    <input autocomplete="off" class="form-control" type="number" name="custom_duration" class="me-2" value={{event.duration}} />
                    <select class="form-control"data-placeholder="Select Location" name="custom_duration_time">  
                        <option value="min"  {% if event.duration_time == 'min' %}selected{% endif %}>
                            {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                        <option value="hrs" {% if event.duration_time == 'hrs' %}selected{% endif %}>
                            {% translate_lang "hours" LANGUAGE_CODE %}</option>
                    </select>
                </div>

                <div class="mb-5">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span>
                            {% translate_lang "Location" LANGUAGE_CODE %}
                        </span>
                    </label>
                
                    <select class="form-control location_select h-40px select2-this" name="location" data-placeholder="Select Location" onChange="valCheck_loc(this)">  
                        <option  {% if not event  %}selected{% endif %} value="Google Meet" {% if event.location == 'Google Meet' %}selected{% endif %} >
                            Google Meet</option>  
                        <option value="Zoom" {% if event.location == 'Zoom' %}selected{% endif %}>
                            Zoom</option>  
                        <option value="physical"
                        {% if event  %}
                            {% if event.location != 'Google Meet' %}
                                {% if event.location != 'Zoom' %}
                                    selected
                                {% endif %}
                            {% endif %}
                        {% endif %}>
                            {% translate_lang "Physical Location" LANGUAGE_CODE %}
                        </option>  
                    </select>
                </div>
                
                <div class="input-group mb-5 custom_location d-none">
                    <input autocomplete="off" class="form-control" type="text" name="custom_location" class="me-2" {%if event.location != "Google Meet"%}{%if event.location != "Zoom"%}value="{{event.location}}"{%endif%}{%endif%} 
                    placeholder="{% translate_lang "Type Your Location" LANGUAGE_CODE %}" 
                    />
                </div>
            </div>

            <div class="mb-5">    
                <div class="mb-5">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span>
                            {% if LANGUAGE_CODE == 'ja'%}
                            予約可能な期間
                            {% else %}
                            Schedulable Period
                            {% endif %}
                        </span>
                    </label>
                    
                    <select class="form-control h-40px select2-this" name="date_event" data-placeholder="Select Date">  
                        <option value="forever" {% if event.date == 'forever' %}selected{% endif %}>
                            {% translate_lang "Indefinitely" LANGUAGE_CODE %}
                        </option>  
                    </select>

                    <div class="d-none">
                        <div class="d-flex mb-5 align-items-center">
                            <div>
                                {% comment %} <input autocomplete="off" class="form-check-input booking_time_type" type="radio" name="date_radio" checked onchange="updateCheckboxValue()" class="me-2" value="booking_range" id="date_booking_range_id" /> {% endcomment %}
                                <input autocomplete="off" class="form-check-input booking_time_type" type="radio" name="date_radio" checked onchange="updateCheckboxValue()" class="me-2" value="booking_range" id="date_booking_range_id" />
                            </div>
                            
                            <div class="input-group ms-2 mb-0 booking_select">
                                <input autocomplete="off" class="form-control" type="number" name="booking_select" class="me-2" value="" id="date_booking_range_id" />
                                <select class="form-control h-40px selects2-this" name="custom_date_event" data-placeholder="Select Location">  
                                    <option value="weekdays" {% if event.location == 'weekdays' %}selected{% endif %}>
                                        {% translate_lang "Weekdays" LANGUAGE_CODE %}
                                    </option>
                                    <option value="all_days" {% if event.location == 'all_days' %}selected{% endif %}>
                                        {% translate_lang "Calendar Days" LANGUAGE_CODE %}
                                    </option>  
                                </select>
                            </div>
                        </div>


                        <div class="d-flex mb-5 align-items-center">
                            <div>
                                <input autocomplete="off" class="form-check-input date_range_type" type="radio" name="date_radio" class="me-2" value="date_range" id="date_range_id" />
                                    
                                <label class="form-check-label" for="date_range_id">
                                    <span>
                                        {% translate_lang "Within a date range" LANGUAGE_CODE %}
                                    </span>
                                </label>
                            </div>
                            <div class="input-group-lg ms-2 mb-0 date_range_select d-none">
                                {% comment %} <span class="text-center">Within Date Range</span> {% endcomment %}
                                <input autocomplete="off" name="date"
                                    class="form-control dateinput form-control" 
                                    placeholder="{% translate_lang "Select Appointment Slot Date" LANGUAGE_CODE %}" 

                                    id="id_due_date"
                                    {% if event %}
                                    value="{{event.date_start}} 〜 {{event.date_end}}"
                                    {%endif%}>
                            </div>
                        </div>

                        <div class="d-flex mb-5 align-items-center">
                            <div>
                                <input autocomplete="off" class="form-check-input forever_type" type="radio" name="date_radio" class="me-2" value="date_forever" id="date_forever_id" />
                            
                                <label class="form-check-label" for="date_forever_id">
                                    <span>
                                        {% translate_lang "Indefinitely" LANGUAGE_CODE %}
                                    </span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>  
    
                <div class="accordion-item mb-5 border-0">
                    <h2 class="accordion-header mt-0 " id="{{event.id}}" style='margin-top:0px'>
                        <button class="border rounded-3 accordion-button fs-7 fw-semibold collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#body_{{event.id}}" aria-expanded="false" aria-controls="body_{{event.id}}">
                            <div>
                                {% translate_lang "Available Hours" LANGUAGE_CODE %}
                                </span>
                            </div>
                        </button>
                    </h2>
                    <div id="body_{{event.id}}" class="accordion-collapse collapse mb-5" aria-labelledby="{{event.id}}">
                        <div class="card accordion-body">
                            <!--Begin of Manage Drawer Interface -->
                            {% for day,day_ja,dayweek,schedule in event_data %}
                                <div class="form-group row mb-5 {{day}}">
                                    <div class="row h-40px col-4">
                                        <input type="hidden" name="selected_day_{{forloop.counter}}" value="0">
                                        <input id="day_{{forloop.counter}}" onchange="updateCheckboxValue_{{forloop.counter}}()" value="1" 
                                            class="ms-2 me-2 mt-2 col-lg-3 col-sm-12 d-flex align-items-center form-check-input cursor-pointer event-day-selection" 
                                            type="checkbox" name="selected_day_{{forloop.counter}}" 
                                            {% if not event %} checked {%endif%} {% if dayweek == '1' %} checked {%endif%}>
                                        
                                        <label class="col d-flex fs-6 fw-bold mt-2" for="day_{{forloop.counter}}">
                                            <span>
                                                {% if LANGUAGE_CODE == 'ja' %} {{day_ja}} {% else %} {{day}} {% endif %}
                                            </span>
                                        </label>
                                    </div>
                                    
                                    <div class="row col align-items-center {% if dayweek == '0' %}d-none{% endif %} hour_item me-2" id="time_{{forloop.counter}}">
                                        {% if dayweek == '1' %}
                                            {% for start,end in schedule %}
                                                <div class="row align-items-center" id="time_hour_{{forloop.counter}}">
                                                    <div class="col-5 mb-5" id="time_start_{{day}}">
                                                        <input class="form-control timepicker timepicker-24 time_start_{{day}}" data-index="{{forloop.counter}}" name="time_start_{{day}}" id="select_start_{{forloop.counter}}" value="{{start}}" type="time" onChange="validateTimeRangeStart_{{day}}(this)"/>
                                                    </div>
                                                        
                                                    <div class="col-sm-1 mb-5">
                                                        -
                                                    </div>  
            
                                                    <div class="col-5 mb-5" id="time_end_{{day}}">
                                                        <input class="form-control timepicker timepicker-24 time_end_{{day}}" data-index="{{forloop.counter}}" name="time_end_{{day}}" id="select_end_{{forloop.counter}}" value="{{end}}"  type="time" onChange="validateTimeRangeEnd_{{day}}(this)"/>
                                                    </div>  

                                                    {% if forloop.counter != 1 %}
                                                    <div class="col-sm-1 mb-5 px-2">
                                                        <button class="btn btn-sm btn-icon btn-danger btn-circle" onclick='delete_item_{{forloop.counter}}(this);' type="button">X</button>
                                                    </div> 
                                                    {%else%}
                                                    <div class="col-sm-1 px-3 mb-5" id="add_item_{{forloop.parentloop.counter}}">
                                                        <a href='javascript:;' onclick='add_item_{{forloop.parentloop.counter}}(this);'>
                                                            <span class="svg-icon svg-icon-primary svg-icon-2x"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg-->
                                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"></rect>
                                                                    <rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"></rect>
                                                                    <rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"></rect>
                                                            </svg>
                                                        </a>
                                                    </div>
                                                    {%endif%}
                                                    <div class="col invalid-notif-{{day}} text-danger d-none" data-index="{{forloop.counter}}">
                                                        <span>
                                                            {% translate_lang "End time cannot be earlier than start time" LANGUAGE_CODE %}
                                                        </span>
                                                    </div>
                                                </div>
                                            {%endfor%}
                                        {% else %}
                                            <div class="row align-items-center" id="time_hour_1">
                                                <div class="col-5 mb-5" id="time_start_{{day}}">
                                                    <input class="form-control timepicker timepicker-24 time_start_{{day}}" data-index="1" name="time_start_{{day}}" id="select_start_{{forloop.counter}}" value="09:00" type="time" onChange="validateTimeRangeStart_{{day}}(this)"/>
                                                </div>
                                                    
                                                <div class="col-sm-1 mb-5">
                                                    -
                                                </div>  
        
                                                <div class="col-5 mb-5" id="time_end_{{day}}">
                                                    <input class="form-control timepicker timepicker-24 time_end_{{day}}" data-index="1" name="time_end_{{day}}" id="select_end_{{forloop.counter}}" value="17:00" type="time" onChange="validateTimeRangeEnd_{{day}}(this)"/>
                                                </div>  

                                                <div class="col-sm-1 px-3 mb-5" id="add_item_{{forloop.counter}}">
                                                    <a href='javascript:;' onclick='add_item_{{forloop.counter}}(this);'>
                                                        <span class="svg-icon svg-icon-primary svg-icon-2x"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg-->
                                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"></rect>
                                                                <rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"></rect>
                                                                <rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"></rect>
                                                        </svg>
                                                    </a>
                                                </div>
                                                <div class="col invalid-notif-{{day}} text-danger d-none" data-index="1">
                                                    <span>
                                                        {% translate_lang "End time cannot be earlier than start time" LANGUAGE_CODE %}
                                                    </span>
                                                </div>
                                            </div>
                                        {%endif%}
                                    </div> 

                                    <div class="row col align-items-center{% if not dayweek %} d-none {% endif %} {% if dayweek == '1' %} d-none {% endif %}" id="time_unv_{{forloop.counter}}">
                                        <span class="ms-1">
                                            {% translate_lang "Unavailable" LANGUAGE_CODE %}
                                    </div> 
                                    
                                    <script>
                                        {% if dayweek == '1' %}  var cek_{{forloop.counter}} = '1'; {% elif not event %} var cek_{{forloop.counter}} = '1'; {% else %} var cek_{{forloop.counter}} = '0'; {% endif %}
                                        function add_item_{{forloop.counter}}(elm){
                                            const time_div = document.getElementById('time_{{forloop.counter}}');
                                            
                                            const div_total = time_div.childElementCount + 1;
                                            var contacts_custom_field_Container = document.getElementById('time_{{forloop.counter}}');
                                            var custom_field_ElmId = String(Date.now())
                                            if ('{{LANGUAGE_CODE}}' == 'ja'){
                                                placeholder_str = "商品"
                                            }
                                            var custom_field_Elm_Str = 
                                            `<div class="row align-items-center mt-2" id="time_hour_${div_total}">
                                                <div class="col-5 mb-5" id="time_start_{{day}}">
                                                    <input class="form-control timepicker timepicker-24 time_start_{{day}}" name="time_start_{{day}}" data-index="${div_total}" id="select_start_${div_total}" value="09:00" type="time" onchange="validateTimeRangeStart_{{day}}(this)"/>
                                                </div>
                                                
                                                <div class="col-sm-1 mb-5">
                                                    -
                                                </div>  
    
                                                <div class="col-5 mb-5" id="time_end_${div_total}">
                                                    <input class="form-control timepicker timepicker-24 time_end_{{day}}" name="time_end_{{day}}" data-index="${div_total}" id="select_end_${div_total}"  value="17:00" type="time" onchange="validateTimeRangeEnd_{{day}}(this)"/>
                                                </div> 
                                                
                                                <div class="col-sm-1 mb-5 px-2">
                                                    <button class="btn btn-sm btn-icon btn-danger btn-circle" onclick='delete_item_${div_total}(this);' type="button">X</button>
                                                </div> 
                                                
                                                <div class="col invalid-notif-{{day}} text-danger d-none" data-index="${div_total}">
                                                    <span>
                                                        {% translate_lang "End time cannot be earlier than start time" LANGUAGE_CODE %}
                                                    </span>
                                                </div>
                                            </div> 
                                            `;
                                            contacts_custom_field_Container.insertAdjacentHTML('beforeend', custom_field_Elm_Str)
                                            elm.parentElement.parentElement.appendChild(contacts_custom_field_Container)
                                    
                                        }
                                    
                                    
                                        function delete_item_{{forloop.counter}}(elm){
                                            elm.parentElement.parentElement.remove()
                                        }
                                        
                                        function updateCheckboxValue_{{forloop.counter}}() {
                                            
                                            var time = document.querySelector('#time_{{forloop.counter}}');
                                            var add_item = document.querySelector('#add_item_{{forloop.counter}}');
                                            var time_event_unavailable = document.querySelector('#time_unv_{{forloop.counter}}');
                                            
                                            if (cek_{{forloop.counter}} == '1'){
                                                time_event_unavailable.classList.remove('d-none');
                                                time.classList.add('d-none');
                                                add_item.classList.add('d-none');
                                                cek_{{forloop.counter}} = '0';
                                            }
                                            else{
                                                add_item.classList.remove('d-none')
                                                time.classList.remove('d-none')
                                                time_event_unavailable.classList.add('d-none')
                                                cek_{{forloop.counter}} = '1';
                                            }

                                            // Get the checkbox elementd-none
                                            var checkbox = document.querySelector('#date_booking_range_id');
                                            var hiddenInput = document.querySelector('input[name="checkbox_value"]');

                                    
                                            // Update the value based on the checked status
                                            checkbox.value = checkbox.checked ? 1 : 0;
                                    
                                            // Log the updated value (you can remove this line in your actual implementation)
                                            console.log('Checkbox value:', checkbox.value);
                                        }
                                    </script>
                                </div>
                            {%endfor%}
                            <!--End of Manage Drawer Interface -->
                        </div>
                    </div>
                </div>
                
                <div class="mb-5">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span class="required">
                            {% translate_lang "Book Time Increment" LANGUAGE_CODE %}
                        </span>
                    </label>
                    <select class="form-control time_increment_select h-40px selects2-this" name="time_increment" data-placeholder="" onChange="valCheck_inc(this)">  
                            
                        <option value="5 min" {% if event.time_increment == '15' %}selected{% endif %}>
                            5 {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                        <option value="10 min" {% if event.time_increment == '10' %}selected{% endif %}>
                            10 {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                        <option value="15 min" {% if event.time_increment == '15' %}selected{% endif %}>
                            15 {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                        <option value="20 min" {% if event.time_increment == '20' %}selected{% endif %}>
                            20 {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                            
                        <option value="25 min" {% if event.time_increment == '25' %}selected{% endif %}>
                            25 {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                        <option value="30 min" {% if not event  %}selected{% endif %} {% if event.time_increment == '30' %}selected{% endif %}>
                            30 {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                        <option value="45 min" {% if event.time_increment == '35' %}selected{% endif %}>
                            35 {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                        <option value="30 min" {% if event.time_increment == '40' %}selected{% endif %}>
                            40 {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                        <option value="45 min" {% if event.time_increment == '45' %}selected{% endif %}>
                            45 {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                        <option value="50 min" {% if event.time_increment == '50' %}selected{% endif %}>
                            50 {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                        <option value="55 min" {% if event.time_increment == '55' %}selected{% endif %}>
                            55 {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                        <option value="60 min" {% if event.time_increment == '60' %}selected{% endif %}>
                            60 {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                        <option value="0" 
                        {% if event  %}
                            {% if event.time_increment %}
                                {% if event.time_increment != '5' %}
                                    {% if event.time_increment != '10' %}
                                        {% if event.time_increment != '15' %}
                                            {% if event.time_increment != '20' %}
                                                {% if event.time_increment != '25' %}
                                                    {% if event.time_increment != '30' %}
                                                        {% if event.time_increment != '35' %}
                                                            {% if event.time_increment != '40' %}
                                                                {% if event.time_increment != '45' %}
                                                                    {% if event.time_increment != '50' %}
                                                                        {% if event.time_increment != '55' %}
                                                                            {% if event.time_increment != '60' %}
                                                                                selected
                                                                            {% endif %}
                                                                        {% endif %}
                                                                    {% endif %}
                                                                {% endif %}
                                                            {% endif %}
                                                        {% endif %}
                                                    {% endif %}
                                                {% endif %}
                                            {% endif %}
                                        {% endif %}
                                    {% endif %}
                                {% endif %}
                            {% endif %}
                        {% endif %}>
                            Custom</option>
                    </select>
                </div>
                
                <div class="input-group mb-5 custom_time_increment d-none">
                    <input autocomplete="off" class="form-control" type="number" name="custom_time_increment" class="me-2" value={{event.time_increment}} />
                    <select class="form-control h-40px select2-this"data-placeholder="Select Location" name="custom_time_increment_time">  
                        <option value="min"  {% if event.time_increment_time == 'min' %}selected{% endif %}>
                            {% translate_lang "minutes" LANGUAGE_CODE %}</option>  
                        <option value="hrs" {% if event.time_increment_time == 'hrs' %}selected{% endif %}>
                            {% translate_lang "hours" LANGUAGE_CODE %}</option>
                    </select>
                </div>

                <!--begin::Localization dropdown-->
                <div class="mb-5">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span class="required">
                            {% if LANGUAGE_CODE == 'ja'%}
                             タイムゾーンを選択 
                            {% else %}
                            Select Timezone
                            {% endif %}

                        </span>
                    </label>
                    <select name="timezone_select" id="timezone_selector" class="form-control h-40px select2-this" data-control="select2" data-placeholder="Select an option">
                        {% if event.timezone %} <option value="{{event.timezone}}">{{event.timezone_place}}</option> {% endif %}
                        <option value="-39600;(GMT-11:00) International Date Line West" >(GMT-11:00) International Date Line West</option>
                        <option value="-39600;(GMT-11:00) Midway Island" >(GMT-11:00) Midway Island</option>
                        <option value="-39600;(GMT-11:00) Samoa" >(GMT-11:00) Samoa</option>
                        <option value="-36000;(GMT-10:00) Hawaii" >(GMT-10:00) Hawaii</option>
                        <option value="-28800;(GMT-08:00) Alaska" >(GMT-08:00) Alaska</option>
                        <option value="-25200;(GMT-07:00) Pacific Time (US &amp; Canada)" >(GMT-07:00) Pacific Time (US &amp; Canada)</option>
                        <option value="-25200;(GMT-07:00) Tijuana" >(GMT-07:00) Tijuana</option>
                        <option value="-25200;(GMT-07:00) Arizona">(GMT-07:00) Arizona</option>
                        <option value="-21600;(GMT-06:00) Mountain Time (US &amp; Canada)">(GMT-06:00) Mountain Time (US &amp; Canada)</option>
                        <option value="-21600;(GMT-06:00) Chihuahua">(GMT-06:00) Chihuahua</option>
                        <option value="-21600;(GMT-06:00) Mazatlan">(GMT-06:00) Mazatlan</option>
                        <option value="-21600;(GMT-06:00) Saskatchewan">(GMT-06:00) Saskatchewan</option>
                        <option value="-21600;(GMT-06:00) Central America">(GMT-06:00) Central America</option>
                        <option value="-18000;(GMT-05:00) Central Time (US &amp; Canada)">(GMT-05:00) Central Time (US &amp; Canada)</option>
                        <option value="-18000;(GMT-05:00) Guadalajara">(GMT-05:00) Guadalajara</option>
                        <option value="-18000;(GMT-05:00) Mexico City">(GMT-05:00) Mexico City</option>
                        <option value="-18000;(GMT-05:00) Monterrey">(GMT-05:00) Monterrey</option>
                        <option value="-18000;(GMT-05:00) Bogota">(GMT-05:00) Bogota</option>
                        <option value="-18000;(GMT-05:00) Lima">(GMT-05:00) Lima</option>
                        <option value="-18000;(GMT-05:00) Quito">(GMT-05:00) Quito</option>
                        <option value="-14400;(GMT-04:00) Eastern Time (US &amp; Canada)">(GMT-04:00) Eastern Time (US &amp; Canada)</option>
                        <option value="-14400;(GMT-04:00) Indiana (East)">(GMT-04:00) Indiana (East)</option>
                        <option value="-14400;(GMT-04:00) Caracas">(GMT-04:00) Caracas</option>
                        <option value="-14400;(GMT-04:00) La Paz">(GMT-04:00) La Paz</option>
                        <option value="-14400;(GMT-04:00) Georgetown">(GMT-04:00) Georgetown</option>
                        <option value="-10800;(GMT-03:00) Atlantic Time (Canada)">(GMT-03:00) Atlantic Time (Canada)</option>
                        <option value="-10800;(GMT-03:00) Santiago">(GMT-03:00) Santiago</option>
                        <option value="-10800;(GMT-03:00) Brasilia">(GMT-03:00) Brasilia</option>
                        <option value="-10800;(GMT-03:00) Buenos Aires">(GMT-03:00) Buenos Aires</option>
                        <option value="-9000;(GMT-02:30) Newfoundland">(GMT-02:30) Newfoundland</option>
                        <option value="-7200;(GMT-02:00) Greenland">(GMT-02:00) Greenland</option>
                        <option value="-7200;(GMT-02:00) Mid-Atlantic">(GMT-02:00) Mid-Atlantic</option>
                        <option value="-3600;(GMT-01:00) Cape Verde Is.">(GMT-01:00) Cape Verde Is.</option>
                        <option value="0;(GMT) Azores">(GMT) Azores</option>
                        <option value="0;(GMT) Monrovia">(GMT) Monrovia</option>
                        <option value="0;(GMT) UTC">(GMT) UTC</option>
                        <option value="3600;(GMT+01:00) Dublin">(GMT+01:00) Dublin</option>
                        <option value="3600;(GMT+01:00) Edinburgh">(GMT+01:00) Edinburgh</option>
                        <option value="3600;(GMT+01:00) Lisbon">(GMT+01:00) Lisbon</option>
                        <option value="3600;(GMT+01:00) London">(GMT+01:00) London</option>
                        <option value="3600;(GMT+01:00) Casablanca">(GMT+01:00) Casablanca</option>
                        <option value="3600;(GMT+01:00) West Central Africa">(GMT+01:00) West Central Africa</option>
                        <option value="7200;(GMT+02:00) Belgrade">(GMT+02:00) Belgrade</option>
                        <option value="7200;(GMT+02:00) Bratislava">(GMT+02:00) Bratislava</option>
                        <option value="7200;(GMT+02:00) Budapest">(GMT+02:00) Budapest</option>
                        <option value="7200;(GMT+02:00) Ljubljana">(GMT+02:00) Ljubljana</option>
                        <option value="7200;(GMT+02:00) Prague">(GMT+02:00) Prague</option>
                        <option value="7200;(GMT+02:00) Sarajevo">(GMT+02:00) Sarajevo</option>
                        <option value="7200;(GMT+02:00) Skopje">(GMT+02:00) Skopje</option>
                        <option value="7200;(GMT+02:00) Warsaw">(GMT+02:00) Warsaw</option>
                        <option value="7200;(GMT+02:00) Zagreb">(GMT+02:00) Zagreb</option>
                        <option value="7200;(GMT+02:00) Brussels">(GMT+02:00) Brussels</option>
                        <option value="7200;(GMT+02:00) Copenhagen">(GMT+02:00) Copenhagen</option>
                        <option value="7200;(GMT+02:00) Madrid">(GMT+02:00) Madrid</option>
                        <option value="7200;(GMT+02:00) Paris">(GMT+02:00) Paris</option>
                        <option value="7200;(GMT+02:00) Amsterdam">(GMT+02:00) Amsterdam</option>
                        <option value="7200;(GMT+02:00) Berlin">(GMT+02:00) Berlin</option>
                        <option value="7200;(GMT+02:00) Bern">(GMT+02:00) Bern</option>
                        <option value="7200;(GMT+02:00) Rome">(GMT+02:00) Rome</option>
                        <option value="7200;(GMT+02:00) Stockholm">(GMT+02:00) Stockholm</option>
                        <option value="7200;(GMT+02:00) Vienna">(GMT+02:00) Vienna</option>
                        <option value="7200;(GMT+02:00) Cairo">(GMT+02:00) Cairo</option>
                        <option value="7200;(GMT+02:00) Harare">(GMT+02:00) Harare</option>
                        <option value="7200;(GMT+02:00) Pretoria">(GMT+02:00) Pretoria</option>
                        <option value="10800;(GMT+03:00) Bucharest">(GMT+03:00) Bucharest</option>
                        <option value="10800;(GMT+03:00) Helsinki">(GMT+03:00) Helsinki</option>
                        <option value="10800;(GMT+03:00) Kiev">(GMT+03:00) Kiev</option>
                        <option value="10800;(GMT+03:00) Kyiv">(GMT+03:00) Kyiv</option>
                        <option value="10800;(GMT+03:00) Riga">(GMT+03:00) Riga</option>
                        <option value="10800;(GMT+03:00) Sofia">(GMT+03:00) Sofia</option>
                        <option value="10800;(GMT+03:00) Tallinn">(GMT+03:00) Tallinn</option>
                        <option value="10800;(GMT+03:00) Vilnius">(GMT+03:00) Vilnius</option>
                        <option value="10800;(GMT+03:00) Athens">(GMT+03:00) Athens</option>
                        <option value="10800;(GMT+03:00) Istanbul">(GMT+03:00) Istanbul</option>
                        <option value="10800;(GMT+03:00) Minsk">(GMT+03:00) Minsk</option>
                        <option value="10800;(GMT+03:00) Jerusalem">(GMT+03:00) Jerusalem</option>
                        <option value="10800;(GMT+03:00) Moscow">(GMT+03:00) Moscow</option>
                        <option value="10800;(GMT+03:00) St. Petersburg">(GMT+03:00) St. Petersburg</option>
                        <option value="10800;(GMT+03:00) Volgograd">(GMT+03:00) Volgograd</option>
                        <option value="10800;(GMT+03:00) Kuwait">(GMT+03:00) Kuwait</option>
                        <option value="10800;(GMT+03:00) Riyadh">(GMT+03:00) Riyadh</option>
                        <option value="10800;(GMT+03:00) Nairobi">(GMT+03:00) Nairobi</option>
                        <option value="10800;(GMT+03:00) Baghdad">(GMT+03:00) Baghdad</option>
                        <option value="14400;(GMT+04:00) Abu Dhabi">(GMT+04:00) Abu Dhabi</option>
                        <option value="14400;(GMT+04:00) Muscat">(GMT+04:00) Muscat</option>
                        <option value="14400;(GMT+04:00) Baku">(GMT+04:00) Baku</option>
                        <option value="14400;(GMT+04:00) Tbilisi">(GMT+04:00) Tbilisi</option>
                        <option value="14400;(GMT+04:00) Yerevan">(GMT+04:00) Yerevan</option>
                        <option value="16200;(GMT+04:30) Tehran">(GMT+04:30) Tehran</option>
                        <option value="16200;(GMT+04:30) Kabul">(GMT+04:30) Kabul</option>
                        <option value="18000;(GMT+05:00) Ekaterinburg">(GMT+05:00) Ekaterinburg</option>
                        <option value="18000;(GMT+05:00) Islamabad">(GMT+05:00) Islamabad</option>
                        <option value="18000;(GMT+05:00) Karachi">(GMT+05:00) Karachi</option>
                        <option value="18000;(GMT+05:00) Tashkent">(GMT+05:00) Tashkent</option>
                        <option value="19800;(GMT+05:30) Chennai">(GMT+05:30) Chennai</option>
                        <option value="19800;(GMT+05:30) Kolkata">(GMT+05:30) Kolkata</option>
                        <option value="19800;(GMT+05:30) Mumbai">(GMT+05:30) Mumbai</option>
                        <option value="19800;(GMT+05:30) New Delhi">(GMT+05:30) New Delhi</option>
                        <option value="19800;(GMT+05:30) Sri Jayawardenepura">(GMT+05:30) Sri Jayawardenepura</option>
                        <option value="20700;(GMT+05:45) Kathmandu">(GMT+05:45) Kathmandu</option>
                        <option value="21600;(GMT+06:00) Astana">(GMT+06:00) Astana</option>
                        <option value="21600;(GMT+06:00) Dhaka">(GMT+06:00) Dhaka</option>
                        <option value="21600;(GMT+06:00) Almaty">(GMT+06:00) Almaty</option>
                        <option value="21600;(GMT+06:00) Urumqi">(GMT+06:00) Urumqi</option>
                        <option value="23400;(GMT+06:30) Rangoon">(GMT+06:30) Rangoon</option>
                        <option value="25200;(GMT+07:00) Novosibirsk">(GMT+07:00) Novosibirsk</option>
                        <option value="25200;(GMT+07:00) Bangkok">(GMT+07:00) Bangkok</option>
                        <option value="25200;(GMT+07:00) Hanoi">(GMT+07:00) Hanoi</option>
                        <option value="25200;(GMT+07:00) Jakarta">(GMT+07:00) Jakarta</option>
                        <option value="25200;(GMT+07:00) Krasnoyarsk">(GMT+07:00) Krasnoyarsk</option>
                        <option value="28800;(GMT+08:00) Beijing">(GMT+08:00) Beijing</option>
                        <option value="28800;(GMT+08:00) Chongqing">(GMT+08:00) Chongqing</option>
                        <option value="28800;(GMT+08:00) Hong Kong">(GMT+08:00) Hong Kong</option>
                        <option value="28800;(GMT+08:00) Kuala Lumpur">(GMT+08:00) Kuala Lumpur</option>
                        <option value="28800;(GMT+08:00) Singapore">(GMT+08:00) Singapore</option>
                        <option value="28800;(GMT+08:00) Taipei">(GMT+08:00) Taipei</option>
                        <option value="28800;(GMT+08:00) Perth">(GMT+08:00) Perth</option>
                        <option value="28800;(GMT+08:00) Irkutsk">(GMT+08:00) Irkutsk</option>
                        <option value="28800;(GMT+08:00) Ulaan Bataar">(GMT+08:00) Ulaan Bataar</option>
                        <option value="32400;(GMT+09:00) Seoul">(GMT+09:00) Seoul</option>
                        <option value="32400;(GMT+09:00) Osaka">(GMT+09:00) Osaka</option>
                        <option value="32400;(GMT+09:00) Sapporo">(GMT+09:00) Sapporo</option>
                        <option value="32400;(GMT+09:00) Tokyo" {% if not event %}selected {% endif %} >(GMT+09:00) Tokyo</option>
                        <option value="32400;(GMT+09:00) Yakutsk">(GMT+09:00) Yakutsk</option>
                        <option value="34200;(GMT+09:30) Darwin">(GMT+09:30) Darwin</option>
                        <option value="34200;(GMT+09:30) Adelaide">(GMT+09:30) Adelaide</option>
                        <option value="36000;(GMT+10:00) Canberra">(GMT+10:00) Canberra</option>
                        <option value="36000;(GMT+10:00) Melbourne">(GMT+10:00) Melbourne</option>
                        <option value="36000;(GMT+10:00) Sydney">(GMT+10:00) Sydney</option>
                        <option value="36000;(GMT+10:00) Brisbane">(GMT+10:00) Brisbane</option>
                        <option value="36000;(GMT+10:00) Hobart">(GMT+10:00) Hobart</option>
                        <option value="36000;(GMT+10:00) Vladivostok">(GMT+10:00) Vladivostok</option>
                        <option value="36000;(GMT+10:00) Guam">(GMT+10:00) Guam</option>
                        <option value="36000;(GMT+10:00) Port Moresby">(GMT+10:00) Port Moresby</option>
                        <option value="36000;(GMT+10:00) Solomon Is.">(GMT+10:00) Solomon Is.</option>
                        <option value="39600;(GMT+11:00) Magadan">(GMT+11:00) Magadan</option>
                        <option value="39600;(GMT+11:00) New Caledonia">(GMT+11:00) New Caledonia</option>
                        <option value="43200;(GMT+12:00) Fiji">(GMT+12:00) Fiji</option>
                        <option value="43200;(GMT+12:00) Kamchatka">(GMT+12:00) Kamchatka</option>
                        <option value="43200;(GMT+12:00) Marshall Is.">(GMT+12:00) Marshall Is.</option>
                        <option value="43200;(GMT+12:00) Auckland">(GMT+12:00) Auckland</option>
                        <option value="43200;(GMT+12:00) Wellington">(GMT+12:00) Wellington</option>
                        <option value="46800;(GMT+13:00) Nuku'alofa">(GMT+13:00) Nuku'alofa</option>
                    </select>
                    <div class="mt-5 mb-5">
                        <input autocomplete="off" class="form-check-input timezone_lock" type="checkbox" name="timezone_lock" class="me-2" value="1" id="timezone_lock_id" {% if event.timezone_is_enable == "1" %}checked{%endif%} />
                            
                        <label class="form-check-label" for="timezone_lock_id">
                            <span>
                                {% translate_lang "Lock the timezone (best for in-person events)" LANGUAGE_CODE %}
                            </span>
                        </label>
                    </div>
                                    
                </div>
                <!--end::Localization dropdown-->
                
                <div class="mb-10">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span class="required">
                            {% translate_lang "Status" LANGUAGE_CODE %}
                        </span>
                    </label>
                
                    <select class="form-control h-40px selects2-this" name="status" data-placeholder="Select Status">  
                        <option value="Active" {% if event.status == 'Active' %}selected{% endif %}>
                            {% translate_lang "Active" LANGUAGE_CODE %} </option>
                        <option value="Draft" {% if event.status == 'Draft' %}selected{% endif %}>
                            {% translate_lang "Draft" LANGUAGE_CODE %}</option>
                    </select>
                </div>
            </div>
        </form>

        <div class="mb-5 submit_section">
            <button form = "calendar_drawer" type="submit" class="btn btn-dark" id="submit_calendar">
                {%if event%}
                    {% translate_lang "Save Updates" LANGUAGE_CODE %}
                {% else %} 
                    {% translate_lang "Create New" LANGUAGE_CODE %}
                {% endif %}
            </button>
            {% if event %} 
            <a type="submit" class="btn btn-danger" href="{% host_url 'calendar_delete' id=event.id host 'app' %}"  >
                {% translate_lang "Delete" LANGUAGE_CODE %}
            </a>
            {% endif %}
        </div>
    </div>
</div>


{% block js %}
<script>
    
    $("#id_due_date").daterangepicker({
        autoUpdateInput: false,
        cancelLabel: 'Clear',
        orientation: "top right",
        locale: {
            {% if LANGUAGE_CODE == 'ja' %}
            cancelLabel: 'クリア',
            format: 'YYYY年MM月DD日', // Japanese date format
            separator: ' 〜 ',
            applyLabel: '選択',
            cancelLabel: 'キャンセル',
            fromLabel: 'From',
            toLabel: 'To',
            customRangeLabel: 'カスタム範囲',
            daysOfWeek: ['日', '月', '火', '水', '木', '金', '土'],
            monthNames: [
                '1月', '2月', '3月', '4月', '5月', '6月',
                '7月', '8月', '9月', '10月', '11月', '12月'
            ],
            {% else %}
            format: "Y-M-DD",
            {% endif %}
        }
    });
    
    $('input[name="date"]').on('apply.daterangepicker', function(ev, picker) {
        {% if LANGUAGE_CODE == 'ja' %}
        $(this).val(picker.startDate.format('YYYY年MM月DD日') + ' 〜 ' + picker.endDate.format('YYYY年MM月DD日'));
        {% else %}
        $(this).val(picker.startDate.format('YYYY-MM-DD') + ' 〜 ' + picker.endDate.format('YYYY-MM-DD'));
        {% endif %}
    });
  
    $('input[name="date"]').on('cancel.daterangepicker', function(ev, picker) {
        $(this).val('');
    });
        $('.emojize-this').emojioneArea({
            placeholder: {% if LANGUAGE_CODE == 'ja' %} "ここに説明を書いてください..." {% else %} "Write your Description here..." {% endif %}
        });


        var custom_duration = document.querySelector('.custom_duration');
        var duration_select = document.querySelector('.duration_select').value;
            if (duration_select == "0"){
                custom_duration.classList.remove('d-none')
            }
            else{
                custom_duration.classList.add('d-none')
            }
        function valCheck(){
            var duration_select = document.querySelector('.duration_select').value;
            if (duration_select == "0"){
                custom_duration.classList.remove('d-none')
            }
            else{
                custom_duration.classList.add('d-none')
            }
        }

        var custom_time_increment = document.querySelector('.custom_time_increment');
        var time_increment_select = document.querySelector('.time_increment_select').value;
            if (time_increment_select == "0"){
                custom_time_increment.classList.remove('d-none')
            }
            else{
                custom_time_increment.classList.add('d-none')
            }
        function valCheck_inc(){
            var time_increment_select = document.querySelector('.time_increment_select').value;
            if (time_increment_select == "0"){
                custom_time_increment.classList.remove('d-none')
            }
            else{
                custom_time_increment.classList.add('d-none')
            }
        }

        var custom_location = document.querySelector('.custom_location');
        var location_select = document.querySelector('.location_select').value;
            if (location_select == "physical"){
                custom_location.classList.remove('d-none')
            }
            else{
                custom_location.classList.add('d-none')
            }
        function valCheck_loc(){
            var location_select = document.querySelector('.location_select').value;
            if (location_select == "physical"){
                custom_location.classList.remove('d-none')
            }
            else{
                custom_location.classList.add('d-none')
            }
        }

        var date_range = document.querySelector('.date_range_type');
        var booking_type = document.querySelector('.booking_time_type');
        var forever = document.querySelector('.forever_type');

        var date_range_select = document.querySelector('.date_range_select');
        var booking_select = document.querySelector('.booking_select');

        date_range.addEventListener('click', () => {
            date_range_select.classList.remove('d-none')
            
            booking_select.classList.add('d-none');
        });
        
        booking_type.addEventListener('click', () => {
            booking_select.classList.remove('d-none')
            
            date_range_select.classList.add('d-none');
        });

        forever.addEventListener('click', () => {
            booking_select.classList.add('d-none')
            date_range_select.classList.add('d-none')
        });
        
        {% for day,day_ja,dayweek,schedule in event_data %}
            function validateTimeRangeStart_{{day}}(object) {
                var submit_calendar =  document.querySelector('#submit_calendar');
                submit_calendar.removeAttribute('disabled');
                const startTime_obj = object;
                const index = object.getAttribute('data-index');
                const notif = document.querySelector(`.invalid-notif-{{day}}[data-index="${index}"]`);
                notif.classList.add('d-none');
                const endTime_obj = document.querySelector(`.time_end_{{day}}[data-index="${index}"]`);
                endTime_obj.classList.remove('is-invalid');
                const startTime = new Date(`2000-01-01 ${startTime_obj.value}`);
                const endTime = new Date(`2000-01-01 ${endTime_obj.value}`);
                console.log(startTime_obj)
                console.log(endTime_obj)
                if (endTime < startTime) {
                    console.log(startTime)
                    console.log(endTime)
                    notif.classList.remove('d-none');
                    endTime_obj.classList.add('is-invalid');
                    var submit_calendar =  document.querySelector('#submit_calendar');
                    submit_calendar.setAttribute('disabled', '');
                    return;
                }
            }
            function validateTimeRangeEnd_{{day}}(object) {
                var submit_calendar =  document.querySelector('#submit_calendar');
                submit_calendar.removeAttribute('disabled');
                const endTime_obj = object;
                endTime_obj.classList.remove('is-invalid');
                const index = object.getAttribute('data-index');
                const notif = document.querySelector(`.invalid-notif-{{day}}[data-index="${index}"]`);
                notif.classList.add('d-none');
                const startTime_obj = document.querySelector(`.time_start_{{day}}[data-index="${index}"]`);
                
                const startTime = new Date(`2000-01-01 ${startTime_obj.value}`);
                const endTime = new Date(`2000-01-01 ${endTime_obj.value}`);
                console.log(startTime)
                console.log(endTime)
                if (endTime < startTime) {
                    console.log(startTime)
                    console.log(endTime)
                    endTime_obj.classList.add('is-invalid');
                    notif.classList.remove('d-none');
                    var submit_calendar =  document.querySelector('#submit_calendar');
                    submit_calendar.setAttribute('disabled', '');
                    return;
                }
            }
            function checkAll_{{day}}(){
                const time_start_{{day}} = document.getElementsByClassName('time_start_{{day}}');
                const time_end_{{day}} = document.getElementsByClassName('time_end_{{day}}');
                
                {% comment %} console.log(numberInputs.length) {% endcomment %}
                for (let i = 0; i < time_start_{{day}}.length; i++) {
                    const startTime = new Date(`2000-01-01 ${time_start_{{day}}[i].value}`);
                    const endTime = new Date(`2000-01-01 ${time_end_{{day}}[i].value}`);

                    if (!(endTime < otherStartTime || startTime > otherEndTime)) {
                        validationMessage.textContent = 'Time range overlaps with another input.';
                        return;
                    }
                }
            }
        {%endfor%}

        document.getElementById('inputAmount').addEventListener('input', function() {
            // Get the current value of the input
            let inputValue = this.value;

            // If the input is 0 or empty, set it to 1
            if (inputValue === '0' || inputValue === '') {
                this.value = '1';
            }
        });
</script>     
{% endblock %}  
  
{% block css %}

<style>
    .loading-spinner{
        display:none;
    }
    .loading-url-spinner{
        display:none;
    }
    .htmx-request .loading-url-spinner{
        display:inline-block;
    }
    .htmx-request.loading-url-spinner{
        display:inline-block;
    }
    .htmx-request .loading-spinner{
        display:inline-block;
    }
    .htmx-request.loading-spinner{
        display:inline-block;
    }
</style>
{% endblock %}