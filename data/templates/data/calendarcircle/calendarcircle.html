{% extends 'base.html' %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% block content %}

<div class="d-flex align-items-end mb-2 align-items-center" style="background-color: #FAFAFA !important;">
    {% include 'data/common/module-header-tabs.html' %}
</div>
{% if permission == 'hide' %}
    {% include 'data/static/no-access-page.html' %}
{% else %}

<div class="d-flex flex-column flex-lg-row justify-content-between align-items-center px-10">
    <div class="my-5">
        <div class="d-none d-flex align-items-end">
            <div class="me-2">
                <a href="{% host_url 'calendar' host 'app' %}" {% if page_group_type == 'calendar' %} class="fs-2 fw-bolder text-dark border-bottom border-bottom-2 border-primary" {%else%} class="fs-2 fw-bold text-gray-500" {%endif%}>
                    {% if LANGUAGE_CODE == 'ja'%}
                    カレンダー 
                    {% else %}
                    Calendar
                    {% endif %}
                </a>
            </div>
            
            <div class="me-2">
                <a href="{% host_url 'calendarcircle' host 'app' %}" {% if page_group_type == "scheduler" %} class="fs-2 fw-bolder text-dark border-bottom border-bottom-2 border-primary" {%else%} class="fs-2 fw-bold text-gray-500" {%endif%}>
                    {% if LANGUAGE_CODE == 'ja'%}
                    スケジューラー
                    {% else %}
                    Scheduler
                    {% endif %}
                </a>
            </div>  
        </div>
    </div>
    <div class="border rounded">
        <button type="button"
            id="calendar_wizard_button" 
            hx-vals='{"drawer_type":"{{page_group_type}}"}'
            hx-swap='outerHTML'
            hx-get="{% host_url 'calendarDrawer_new' host 'app'  %}" 
            hx-indicator=".loading-drawer-spinner,.calendar-form"
            hx-target="#calendar_form"
            class="w-100px align-items-center d-flex btn btn-dark btn-md py-1 rounded-1" 
        >
        <span class="svg-icon svg-icon-2">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"></rect>
                <rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"></rect>
                <rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"></rect>
            </svg>
        </span>
        <span class="fs-7">
            {% if LANGUAGE_CODE == 'ja'%}
            新規
            {% else %}
            New
            {% endif %}
        </span>
        </button>

    </div>


    {% comment %} {% if page_group_type == 'calendar_meeting' %}
    <div class="mt-5 me-10">
        <form id="invoice_id_list_download" method="POST" action="{% host_url 'invoice_download' host 'app' %}"  enctype="multipart/form-data">{% csrf_token %}</form>
        <a  href="{% host_url 'calendar_meeting' host 'app' %}?type=calendar_view"
            class="me-1 btn btn-sm bg-gray-100 w-100 me-1 h-40px min-w-150px" 
            >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M21 22H3C2.4 22 2 21.6 2 21C2 20.4 2.4 20 3 20H21C21.6 20 22 20.4 22 21C22 21.6 21.6 22 21 22ZM13 13.4V3C13 2.4 12.6 2 12 2C11.4 2 11 2.4 11 3V13.4H13Z" fill="black"/>
                <path opacity="0.3" d="M7 13.4H17L12.7 17.7C12.3 18.1 11.7 18.1 11.3 17.7L7 13.4Z" fill="black"/>
            </svg> 
            {% translate_lang "Calendar View" LANGUAGE_CODE %}
        </a>
    </div>
    {% endif %} {% endcomment %}
</div>

<div class="px-10 w-100">
    <div class="{% if page_group_type == 'calendar' %}d-none{% endif %}">
        <div class="mb-5">
            <div class="d-flex align-items-center">
                <form id="filter-form-search" method="get" class="min-w-150px w-100">
                    <div class="d-flex mb-0 position-relative align-items-center">
                        <span class="svg-icon svg-icon-3 search-icon position-absolute top-50 translate-middle-y ms-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                                <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                            </svg>
                        </span>
                            
                        <input
                        id="base-search-input" type="text" name="q" class="form-control bg-white ps-12 h-40px me-2"
                        value={% if search_q %} "{{ search_q }}" {% else %}""{% endif %}
                        placeholder="{% if page_group_type == 'scheduler' %}{% translate_lang "Search Event" LANGUAGE_CODE %}{% else %}{% translate_lang "Search Meeting" LANGUAGE_CODE %}{%endif%}"
                        onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                        >
                        <input type="hidden" value="{{view_id}}" name="view_id">
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="d-flex d-none align-items-center mb-5">
        <div class="bg-gray-100 rounded me-2 d-flex px-5 align-items-center h-40px">
            <div>
                <input class="form-check-input" type="checkbox" id="selectAll" onchange="select_all()" />
                {% if LANGUAGE_CODE == 'ja'%}
                すべて選択
                {% else %}
                Select All
                {% endif %}
            </div>  

        </div>

            <div class="d-none me-2">
                <button id='update-company' class="btn btn-sm bg-gray-100 w-100 me-2 h-40px disabled" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-overflow="true">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19.4 13.9411L10.7 5.24112C10.4 4.94112 10 4.84104 9.60001 5.04104C9.20001 5.24104 9 5.54107 9 5.94107V18.2411C9 18.6411 9.20001 18.941 9.60001 19.141C9.70001 19.241 9.9 19.2411 10 19.2411C10.2 19.2411 10.4 19.141 10.6 19.041C11.4 18.441 12.1 17.941 12.9 17.541L14.4 21.041C14.6 21.641 15.2 21.9411 15.8 21.9411C16 21.9411 16.2 21.9411 16.4 21.8411C17.2 21.5411 17.5 20.6411 17.2 19.8411L15.7 16.2411C16.7 15.9411 17.7 15.741 18.8 15.541C19.2 15.541 19.5 15.2411 19.6 14.8411C19.8 14.6411 19.7 14.2411 19.4 13.9411Z" fill="currentColor"/>
                        <path opacity="0.3" d="M15 6.941C14.8 6.941 14.7 6.94102 14.6 6.84102C14.1 6.64102 13.9 6.04097 14.2 5.54097L15.2 3.54097C15.4 3.04097 16 2.84095 16.5 3.14095C17 3.34095 17.2 3.941 16.9 4.441L15.9 6.441C15.7 6.741 15.4 6.941 15 6.941ZM18.4 9.84102L20.4 8.84102C20.9 8.64102 21.1 8.04097 20.8 7.54097C20.6 7.04097 20 6.84095 19.5 7.14095L17.5 8.14095C17 8.34095 16.8 8.941 17.1 9.441C17.3 9.841 17.6 10.041 18 10.041C18.2 9.94097 18.3 9.94102 18.4 9.84102ZM7.10001 10.941C7.10001 10.341 6.70001 9.941 6.10001 9.941H4C3.4 9.941 3 10.341 3 10.941C3 11.541 3.4 11.941 4 11.941H6.10001C6.70001 11.941 7.10001 11.541 7.10001 10.941ZM4.89999 17.1409L6.89999 16.1409C7.39999 15.9409 7.59999 15.341 7.29999 14.841C7.09999 14.341 6.5 14.141 6 14.441L4 15.441C3.5 15.641 3.30001 16.241 3.60001 16.741C3.80001 17.141 4.1 17.341 4.5 17.341C4.6 17.241 4.79999 17.2409 4.89999 17.1409Z" fill="currentColor"/>
                    </svg>
                    {% if LANGUAGE_CODE == 'ja'%}
                    アクション
                    {% else %}
                    Action
                    {% endif %}
                </button>

                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-bold w-200px" data-kt-menu="true">
                    <div class="mb-5">
                        <div class="separator opacity-75 mb-5"></div>
                        
                        <div class="menu-item px-3">
                            <div class="menu-content px-2 py-1">
                                
                                <button class="border-0 bg-transparent px-4" data-bs-toggle="modal" data-bs-target="#manage_companies_addtolist">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    リストに追加する
                                    {% else %}
                                    Add to List
                                    {% endif %}
                                </button>
                                
                                <button name="bulk_delete_company" class="border-0 bg-transparent px-4" data-bs-toggle="modal" data-bs-target="#manage_delete_bulk">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    会社の削除
                                    {% else %}
                                    Delete Event
                                    {% endif %}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        

            <div class="me-2 d-none">
                <button id='csv_download-company' class="btn btn-sm bg-gray-100 w-100 h-40px disabled" type="submit" name="csv_download" form="company-form">
                    <svg xmlns="http://www.w3.-listorg/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M21 22H3C2.4 22 2 21.6 2 21C2 20.4 2.4 20 3 20H21C21.6 20 22 20.4 22 21C22 21.6 21.6 22 21 22ZM13 13.4V3C13 2.4 12.6 2 12 2C11.4 2 11 2.4 11 3V13.4H13Z" fill="black"/>
                        <path opacity="0.3" d="M7 13.4H17L12.7 17.7C12.3 18.1 11.7 18.1 11.3 17.7L7 13.4Z" fill="black"/>
                    </svg>
                    {% if LANGUAGE_CODE == 'ja'%}
                    ダウンロード
                    {% else %}
                    Download
                    {% endif %}
                </button>
            </div>
        </div>
        
        {% include 'data/calendarcircle/calendar-table.html' %}
</div>

{% endif%}

{% endblock %}