{% extends 'base.html' %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% load tz %}
{% block content %}

{% include "data/common/advance_search/advance-search-style.html" %}
{% include "data/utility/table-css.html" %}
{% include 'data/static/tootip-search-wrapper.html' %}

<style>
    html {
        scroll-padding-top: 80px;
      }
      
    textarea {
        resize: none;
    }

    .htmx-indicator, #workflow-spinner{
        display:none !important;
    }
    .htmx-request .htmx-indicator, #workflow-spinner.htmx-request{
        display:inline-block !important;
    }
    .htmx-request.htmx-indicator, #workflow-spinner.htmx-request{
        display:inline-block !important;
    }

    .htmx-indicator, .loading-spinner{
        display:none !important;
    }
    .htmx-request .htmx-indicator, .loading-spinner.htmx-request{
        display:inline-block !important;
    }
    .htmx-request.htmx-indicator, .loading-spinner.htmx-request{
        display:inline-block !important;
    }

    @keyframes fade {
        0% { opacity: 0; }  
        50% { opacity: 1; }
        100% { opacity: 0; }
    }

    .app-box:hover {background: #F8F6F2}

</style>

{% if permission == 'hide' %}
    {% include 'data/static/no-access-page.html' %}
{% else %}
{% if project %}
<div class="d-flex flex-column flex-lg-row tw-h-[92vh]">
	<div class="w-100 h-100 w-100">
        <div class="d-flex d-flex align-items-center" style="background-color: #FAFAFA !important;">
            {% include 'data/common/module-header-tabs.html' %}

            <div class="w-50 d-flex justify-content-end tw-mr-5" id="view-container">

                {% if target == 'task' %}
                    <div class="ms-2">
                        <button type="button" class="max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 btn py-1 rounded-1 tw-bg-gray-200 hover:tw-bg-gray-300 justify-content-center px-1 view-wizard-button"
                            hx-vals='{"object_type": "task", "view_id":"{{view_id}}", "download_view":true, "p_id": "{{p_id}}"}'
                            hx-get="{% url 'commerce_view_setting' %}" 
                            hx-target="#view-drawer"
                            >
                            <span class="svg-icon svg-icon-4">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                                    <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                                    <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                                </svg>

                            </span>
                            <span class="">
                                {% if LANGUAGE_CODE == 'ja'%}
                                エクスポート
                                {% else %}
                                Export
                                {% endif %}
                            </span>
                        </button>
                    </div>
                    {% if permission|check_permission:'edit' %}
                    {% include 'data/partials/create-task-button.html' %}
                    {% endif %}

                {% else %}
                    {% if permission|check_permission:'edit' %}
                        <div class="mb-2">
                            {% include 'data/partials/create-workflow-button.html' with from=from %}
                        </div>
                    {% else %}
                        <div class="mb-5">
                        </div>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    
        <div class="{% include "data/utility/table-container.html" %}">
            {% include 'data/common/permission-action-warning-message.html' %}
            {% if target == 'task' %}
                {% include "data/taskflow/project_item_view.html" with status=status %}
            {% endif %}

            {% if target == 'workflow' %}
                <div 
                hx-get="{% host_url 'workflows_data' id=view_id host 'app' %}?status={{status}}&menu_key={{menu_key}}{% if highlight_workflow %}&h_workflow={{highlight_workflow.id}}{% endif %}{% if workflows_length %}&length={{workflows_length}}{% endif %}{% if search_q %}&workflow_q={{search_q}}{% endif %}"
                hx-trigger="load, load_workflows_data from:body" 
                hx-indicator=".loading-spinner"
                hx-target="#workflow-data"
                ></div>
                
                <div class="d-flex justify-content-center">
                    <div class="loading-spinner mt-10 mb-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>     
                </div> 
                <div id="workflow-data"></div>
                
            {% endif %}


            {% if target == 'gallery' %}
                {% if app_slug and app_slug != 'taskflow' %}
                    <div class="tab-pane fade show active mb-4  " role="tabpanel">
                        <div class="nav nav-tabs nav-line-tabs fs-6 border-bottom border-bottom-1">
                            <div class="nav-item me-3">
                                <a class="nav-link mx-1 fs-1" href="{% host_url 'workflows_in_app' app_slug=app_slug host 'app' %}">
                                    <span>
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        ワークフロー
                                        {% else %}
                                        Workflows
                                        {% endif %}
                                    </span>
                                </a>
                            </div>
                            <div class="nav-item me-3">
                                <a class="nav-link ms-0 me-2 active fw-bolder border-bottom-2 fs-1">
                                    <span>
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        ギャラリー
                                        {% else %}
                                        Gallery
                                        {% endif %}
                                    </span>
                                </a>
                            </div>
                        </div>
                    </div>
                {% endif %}

                <div class="mb-5 mt-5"
                hx-get="{% host_url 'query_workflows_view' host 'app' %}{% if show_workflow %}?show_workflow={{show_workflow}}{% endif %}"
                hx-trigger="load"
                hx-indicator="#workflow-spinner"
                hx-vals='{"tag_slug": "{{tag_slug}}", "from": "{{from}}"}'

                >
                    <div class="py-5 mb-5 py-0 d-flex align-items-center border-bottom">
                        <span id="workflow-spinner" class="spinner-border spinner-border-sm text-secondary" style="margin-left: auto; margin-right: auto; margin-top: auto; margin-bottom: auto;" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </span>
                
                    </div> 
                </div> 
            
            {% endif %}
        </div>
    </div>
</div>
{% else %}
<div class="col-12 px-10 mt-5">
    <div class="card border mb-5">
        <div class="card-body">
            <h1>
                {% if LANGUAGE_CODE == 'ja'%}
                プロジェクトを作成
                {% else %}
                Create your Project 
                {% endif %}
            </h1>
            <p class="fs-4">
                {% if LANGUAGE_CODE == 'ja'%}
                プロジェクトを作成して、業務管理を行いましょう。
                {% else %}
                Create your first project to streamline operations!
                {% endif %}
            </p>
            {% if permission|check_permission:'edit' %}
            <button id="channel_create_button" class="btn btn-light-primary view-wizard-button"
                hx-vals='{"source": "taskflow", "module_slug" : "{{module.slug}}", "module_object": "{{module_object}}"}'
                hx-get="{% url 'projects_settings' %}" 
                hx-target="#view-drawer">
                {% if LANGUAGE_CODE == 'ja'%}
                    新規プロジェクト
                {% else %}
                    New Project
                {% endif %}
            </button>
            {% endif %}
        </div>
    </div>
</div>
{% endif %}
{% endif %}

<script>
    function check_permission_action(event, permission_type, ...args){
        let source = args.length > 0 ? args[0] : null;
        
        const checkInputs = document.querySelectorAll('.check_input:checked');

        let members = "{{group_members}}"
        members = members.split(',')
        const user_id = '{{request.user.id}}'
        const permission = '{{permission}}';
        const permission_list = permission.split('|');
        let scope = ''
        permission_list.forEach(p => {
            if (p.includes(permission_type)) {
                p_split = p.split('_');
                scope = p_split[0]
            }
        })

        let msg = '';
        let denied = false;
        for (let i = 0; i < checkInputs.length; i++) {
            var owner_id = checkInputs[i].dataset.owner;
            if (owner_id){
                if (scope == 'user'){
                    if (owner_id.toString() !== user_id.toString()) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分のアイテムのみ編集または削除することができます。";
                        {% else %}
                        msg = "Action denied. You are only allowed to edit or delete your own items.";
                        {% endif %}                    
                        checkInputs[i].click()
                        denied = true;
                    }
                } else if (scope == 'team'){
                    if (!members.includes(owner_id.toString())) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分または自分のチームに割り当てられたアイテムのみ編集または削除できます。";
                        {% else %}
                        msg = "Action denied. You can only edit or delete items assigned to you or your team.";
                        {% endif %}
                        checkInputs[i].click()
                        denied = true;
                    }
                } 
            }
        }
        if (denied) {
            event.preventDefault();
            event.stopImmediatePropagation();
            document.getElementById('permissionActionWarning').innerHTML = msg;
            setTimeout(() => {
                document.getElementById('permissionActionWarning').innerHTML = '';
            }, 4000);
            msg = ''
        } else if (source){
            const modalEl = document.getElementById(source);
            const modal = bootstrap.Modal.getOrCreateInstance(modalEl);
            modal.show();
        }
        
    }
</script>

{% endblock %}
    