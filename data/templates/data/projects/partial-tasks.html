{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

{% if views %}
<div class="{% include "data/utility/tab-pane.html" %}" role="tabpanel" style="z-index: 4;">
    <div class="{% include "data/utility/table-nav.html" %}">
        <div class="d-flex align-items-end justify-content-between w-100" id="task-view-contianer">
            
            <div class="w-100 d-lg-block d-none">
                <div class="max-md:tw-hidden w-75 d-flex align-items-center me-3">
                    <div class="nav-item fs-6 text-gray-900">
                        <div class="d-flex align-items-center justify-content-center">
                            <button type="button" class="{% include "data/utility/view-plus-link.html" %} view-wizard-button" 
                            hx-get="{% url 'commerce_view_setting' %}" 
                            hx-vals='{"object_type": "task", "view_button":"create", "p_id": "{{p_id}}"}' 
                            hx-target="#view-drawer" 
                            hx-swap="innerHTML"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus mb-1" viewBox="0 0 16 16">
                                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    {% include "data/projects/partial-dropdown-view-menu.html" %}
                    {% for view in views %}
                        {% if view.title == "main" %}
                            <div class="h-100 {% include "data/utility/view-menu-default-common.html" %} me-2 mh-100px">
                                <a href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?view_id={{view.id}}&p_id={{p_id}}"
                                    class="pb-2 h-100 text-nowrap nav-link ms-0 me-0 fw-bolder {% if view_id == view.id %}active border-bottom-2{% else %}ms-0{% endif %} d-flex justify-content-between align-items-center">
                                    {% if LANGUAGE_CODE == 'ja'%}ビュー{% else %}View{% endif %}
                                </a>                             

                                {% comment %} edit {% endcomment %}
                                {% if view_id == view.id %}
                                <div class="h-100 w-20px nav-item justify-content-center d-flex fs-6 text-gray-900">
                                    <button type="button" class="{% include "data/utility/view-plus-link.html" %} view-wizard-button"
                                        hx-vals='{"object_type": "task", "view_id":"{{view.id}}", "p_id": "{{p_id}}"}'
                                        hx-get="{% url 'commerce_view_setting' %}" 
                                        hx-target="#view-drawer"
                                        
                                        >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                            <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                        </svg>

                                    </button>
                                </div>
                                {% endif %}
                            </div>
                        {% endif %}
                    {% endfor %}

                    {% include 'data/projects/partial-view-menu.html' with target='task' %}
                </div>
            </div>
            <div class="d-lg-none d-block" id="mobile-task-view">
                <div class="d-flex">
                    <select class="min-h-40px border-0 form-select form-select-solid border bg-white select2-this" data-control="select2" name="view_id" placeholder="{% if LANGUAGE_CODE == 'ja' %}ステータス{% else %}Status{% endif %}"
                        hx-target="#task-data"
                        hx-swap="innerHTML"
                        hx-get="{% host_url 'tasks_data' host 'app' %}"
                        hx-vals='{"from":"{{from}}" {% if project_id %},"project":"{{project_id}}"{% endif %}, "module":"{{menu_key}}"}' 
                        hx-trigger="htmx-change">
                        <option {% if not view_id %}selected{% endif %} value="">{% if LANGUAGE_CODE == 'ja' %}すべて{% else %}All{% endif %}</option>
                        {% for view in views %}
                        <option {% if view_id|stringify == view.id|stringify %}selected{% endif %} value="{{view.id}}">{{view.title}}</option>
                        {% endfor %}
                    </select>
                    {% for view in views %}
                        {% if view.id|stringify == view_id|stringify %}
                            <div class="h-100 w-20px nav-item justify-content-center d-flex fs-6 text-gray-900">
                                <button type="button" class="h-100 {% include "data/utility/view-plus-link.html" %} pt-2 view-wizard-button"
                                    hx-get="{% host_url 'view_form' host 'app' %}"
                                    hx-target="#view-drawer"
                                    hx-swap="innerHTML"
                                    hx-vals='{"view_id":"{{view.id}}", "target": "{{target}}" {% if project_id %},"project": "{{project_id}}"{% endif %}, "from": "{{from}}" }'
                                    hx-trigger="click">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                        <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                    </svg>
                                </button>
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>

                <script>
                    $('#mobile-task-view .select2-this').select2();
                    $('#mobile-task-view .select2-this').on('change', function (e) {
                        var selectElement = $(this).closest('select').get(0);
                        var url = new URL("{% if request.is_secure %}https{% else %}http{% endif %}:{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}");
                        url.searchParams.set("view_id", selectElement.value);
                        try {
                            history.pushState({}, "", url);
                        }
                        catch(err) {
                            console.log(err)
                            if (url.href.includes("https")) {
                                url = new URL("http:{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}");
                            } else {
                                url = new URL("https:{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}");
                            }
                            url.searchParams.set("view_id", selectElement.value);
                        }
                        selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                        htmx.trigger("body", "updateTask");
                    });
                </script>
            </div>
            <div class="d-flex">
                <div class="d-flex me-2 {% include "data/utility/search-wrapper.html" %} hover-tooltip">
                    <style>
                        .search-wrapper {
                            display: flex;
                            align-items: center;
                            position: relative;
                            width: 24px;
                            transition: width 0.4s ease-in-out, margin-right 0.4s ease-in-out, opacity 0.4s ease-in-out;
                        }
                    
                        .search-wrapper.expanded {
                            width: 200px; /* New width when expanded */
                            margin-right: -0.5rem !important;
                        }
                    
                        .search-wrapper input {
                            display: none;
                            width: 0;
                            padding: 0;
                            opacity: 0;
                            transition: width 0.4s ease-in-out, margin-right 0.4s ease-in-out, opacity 0.4s ease-in-out;
                        }
                    
                        .search-wrapper.expanded input {
                            display: block;
                            width: 100%;
                            opacity: 1;
                        }
                        
                    
                        .search-icon-view {
                            position: absolute;
                            left: 10px;
                            top: 50%;
                            transform: translateY(-50%);
                        }
                    
                        /* Tooltip container */
                        .hover-tooltip {
                            position: relative;
                            display: inline-block;
                        }
                    
                        /* Tooltip text */
                        .hover-tooltip .hover-tooltip-text {
                            visibility: hidden;
                            width: 80px;
                            background-color: #555;
                            color: #fff;
                            text-align: center;
                            padding: 3px 0;
                            border-radius: 8px;
                    
                            /* Position the hover-tooltip text */
                            position: absolute;
                            z-index: 1;
                            top: 50%;
                            right: 105%;
                            transform: translateY(-50%);
                    
                            /* Fade in hover-tooltip */
                            opacity: 0;
                            transition: opacity 0.5s;
                        }
                    
                        /* Show the hover-tooltip text when you mouse over the hover-tooltip container */
                        .hover-tooltip:hover .hover-tooltip-text {
                            visibility: visible;
                            opacity: 0.9;
                        }
                    </style>
                    <span class="search-wrapper-tooltip hover-tooltip-text">
                        {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}
                    </span>
                    <div class="d-flex align-items-center">
                        <form id="task-search" class="d-flex position-relative me-5" onkeydown="return event.key != 'Enter';"
                            hx-target="#task-data"
                            hx-swap="innerHTML"
                            {% if view_id %}
                            hx-get="{% host_url 'tasks_data' view_id host 'app' %}"
                            {% else %}
                            hx-get="{% host_url 'tasks_data' host 'app' %}"
                            {% endif %}
                            hx-trigger="keyup from:#task-search delay:1000ms, keyup[keyCode=13] from:#task_q"
                            hx-vals='{"from":"{{from}}", "p_id":"{{p_id}}" {% if project_id %},"project":"{{project_id}}"{% endif %}, "module":"{{menu_key}}"}'
                            hx-include="[name=task_q]">
                            <span class="svg-icon svg-icon-3 search-icon-view" onclick="toggleSearch()">
                                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M13 13L10.1 10.1M11.6667 6.33333C11.6667 9.27885 9.27885 11.6667 6.33333 11.6667C3.38781 11.6667 1 9.27885 1 6.33333C1 3.38781 3.38781 1 6.33333 1C9.27885 1 11.6667 3.38781 11.6667 6.33333Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </span>
                            <input
                            id="base-search-input" type="text" name="task_q" class="form-control bg-white ps-12 tw-rounded-lg"
                            value="{% if q %}{{q}}{% endif %}"
                            placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                            onkeypress="if (event.keyCode === 13)document.forms['filter-form-search'].submit();"
                            >
                            {% if request.GET.status == 'archived' %}
                            <input type="hidden" value="archived" name="status">
                            {% endif %}
                            <input type="hidden" value="{{view_id}}" name="view_id">
                        </form>
                        {% include 'data/javascript/toggleSearch.html' with search_q=q %}
                    </div>
                </div>
            </div>
        </div>
        <div id="task-bulk-action-container" class="d-none">
            <span class="me-10">
                <button class="py-1 rounded-1 btn btn-sm btn-light fw-bold mt-2 mb-1" onclick="selectAllTasks()">
                    <span>
                        {% if LANGUAGE_CODE == 'ja'%}
                        すべて選択
                        {% else %}
                        Select All
                        {% endif %}
                    </span>
                </button>
                <button class="py-1 rounded-1 btn btn-sm btn-light fw-bold mt-2 mb-1" onclick="deselectAllTasks()">
                    <span>
                        {% if LANGUAGE_CODE == 'ja'%}
                        選択を解除
                        {% else %}
                        Deselect All
                        {% endif %}
                    </span>
                </button>

                <script>
                    function selectAllTasks() {
                        selectTaskInputs = document.getElementsByClassName('task-selection')
                        for (var i = 0; i < selectTaskInputs.length; i++) {
                            selectTaskInputs[i].checked = true;
                        }
                    }
                    function deselectAllTasks() {
                        selectTaskInputs = document.getElementsByClassName('task-selection')
                        for (var i = 0; i < selectTaskInputs.length; i++) {
                            selectTaskInputs[i].checked = false;
                        }
                        document.getElementById('task-bulk-action-container').classList.add('d-none')
                        document.getElementById('task-view-contianer').classList.remove('d-none')
                    }
                </script>
            </span>

            {% if permission|check_permission:'edit' %}
            <button class="py-1 rounded-1 btn btn-sm btn-light-primary fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'edit', 'edit_task_bulk_modal')">
                <span>
                    {% if LANGUAGE_CODE == 'ja'%}
                    編集
                    {% else %}
                    Edit
                    {% endif %}
                </span>
            </button>
            {% endif %}

            {% if permission|check_permission:'archive' %}
            <button class="btn btn-sm btn-light-success py-1 rounded-1 fw-bold mt-2 mb-1"
                hx-post="{% host_url 'bulk_task_usage_status' host 'app' %}" 
                hx-include="[name='selected_task']" 
                hx-vals='{ {% if view_id %}"view_id":"{{view_id}}",{% endif %} "from":"{{from}}", "p_id":"{{p_id}}", "fn":"activate"}'
                onclick="check_permission_action(event, 'archive')">
                    {% if LANGUAGE_CODE == 'ja'%}
                    有効化
                    {% else %}
                    Activate 
                    {% endif %}
            </button>

            <button class="btn btn-sm btn-light-danger py-1 rounded-1 fw-bold mt-2 mb-1"
                hx-post="{% host_url 'bulk_task_usage_status' host 'app' %}" 
                hx-include="[name='selected_task']" 
                hx-vals='{ {% if view_id %}"view_id":"{{view_id}}",{% endif %} "from":"{{from}}", "p_id":"{{p_id}}", "fn":"archive"}'
                onclick="check_permission_action(event, 'archive')">
                    {% if LANGUAGE_CODE == 'ja'%}
                    アーカイブ
                    {% else %}
                    Archive 
                    {% endif %}
            </button>
            {% endif %}

            {% if permission|check_permission:'edit' %}
            <button class="btn btn-sm btn-light-warning py-1 rounded-1 fw-bold mt-2 mb-1"
                hx-post="{% host_url 'bulk_task_duplicate' host 'app' %}" 
                hx-include="[name='selected_task']" 
                hx-vals='{ {% if view_id %}"view_id":"{{view_id}}",{% endif %} "from":"{{from}}", "p_id":"{{p_id}}"}'
                onclick="check_permission_action(event, 'edit')">
                    {% if LANGUAGE_CODE == 'ja'%}
                    複製
                    {% else %}
                    Duplicate 
                    {% endif %}
            </button>
            {% endif %}

            <script>
                document.body.addEventListener("hideTaskBulkActionBtn", function(evt){
                    document.getElementById('task-bulk-action-container').classList.add('d-none')
                    document.getElementById('task-view-contianer').classList.remove('d-none')
                })
            </script>
        </div>
        
    </div>
</div>
{% include 'data/projects/tasks-bulk-edit-modal.html' %}
{% endif %}


<style>
    table {
        width: auto;
    }
    
    td, th {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
</style>
{% if view_filter.view_type == 'calendar' %}
    {% include "data/taskflow/project_calendar_view.html" %}
    
{% elif view_filter.view_type == 'gantt_chart_with_production_line' %}
    <div class="mt-5 w-100">
        {% if view_filter.sub_view_type|is_uuid %}
            {% include "data/taskflow/gantt_chart_production_line_view.html" %}
        {% else %}
            {% include "data/taskflow/gantt_chart_view.html" %}
        {% endif %}
    </div>
{% elif view_filter.view_type == 'gantt_chart' %}
    <div class="mt-5 w-100">
        {% include "data/taskflow/gantt_chart_view.html" %}
    </div>
{% else %}
    <div class="d-flex flex-column flex-lg-row">
        <div class="flex-lg-row-fluid">
            <form id="task-order-form" class="{% if view_id %}sortable{% endif %} min-w-550px m-0 m-0" id="task_list" hx-post="{% host_url 'project_order' host 'app' %}" hx-indicator="" hx-trigger="changedTaskOrder" hx-swap="none">
                <input hidden name="view_id" value="{{view_id}}" type="text">
                <table id="table-content" class="{% include "data/utility/table.html" %} task-table-content">
                    <thead class="{% include "data/utility/table-header.html" %}">
                        <tr class="">
                            <th class="min-w-10px"></th>
                            <th class="{% include "data/utility/column-checkbox-size.html" %}"></th>
                            <th class="{% include "data/utility/column-id-size.html" %}">ID</th>
                            {% if not 'title' in view_filter.column|safe|string_list_to_list %}
                                <th class="">
                                    {% with args='title'|add:'|'|add:object_type %} 
                                        {% with column_display=args|get_column_display:request %}
                                            {{column_display.name}}
                                        {% endwith %}
                                    {% endwith %}
                                </th>
                            {% endif %}
                            {% for column in view_filter.column|safe|string_list_to_list %}
                                <th class=''>
                                    {% with args=column|add:'|'|add:object_type %} 
                                        {% with column_display=args|get_column_display:request %}
                                            {{column_display.name}}
                                        {% endwith %}
                                    {% endwith %}
                                </th>
                            {% endfor %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for task in tasks %}
                        <tr>
                            <td class="w-10px p-0">
                                <div class="me-2 d-flex justify-content-center cursor-grab z-index-1 position-relative task-grip {% if not view_filter.sort_order_by == 'manual_sort' %}d-none{% endif %}">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-grip-vertical" viewBox="0 0 16 16">
                                        <path d="M7 2a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm3 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0zM7 5a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm3 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0zM7 8a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm3 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm-3 3a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm3 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm-3 3a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm3 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"/>
                                    </svg>
                                    <input type="hidden" name="task" value="{{task.id}}">
                                </div>
                                <input hidden name="task" value="{{task.id}}">
                            </td>
                
                            <td class="w-10px p-0">
                                <input id="{{task.id}}" class="form-check-input task-selection cursor-pointer check_input" type="checkbox" name="selected_task" data-owner="{{task.owner.user.id}}"value="{{task.id}}" onclick="checking_checkbox(this, event)"/>
                            </td>

                            <td class="fw-bolder text-nowrap special-col min-w-50px" style="border-right: 1px solid !important; border-right-color: rgb(234, 234, 234) !important;">
                                <a class="{% include "data/utility/table-link.html" %} manage_full_wizard_button view_form_trigger{{task.id}}"
                                    hx-target="#manage-full-drawer-content"
                                    hx-get="{% host_url 'task_form' host 'app' %}"
                                    hx-vals='{"task_id":"{{task.id}}", "view_id":"{{view_id}}", "from":"{{from}}", "p_id": "{{p_id}}", "type": "update", "module": "{{module}}" , "set_id": "{{set_id}}"}'
                                    hx-swap="innerHTML"
                                    hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"
                                    hx-on::before-request="document.getElementById('manage-full-drawer-content').innerHTML = '';document.getElementById('manage-full-drawer-content').innerHTML = '';document.querySelector('.task-drawer').innerHTML = '';"
                                    >
                                        {{task.task_id|stringformat:"04d"}} 
                                </a>
                            </td>
                            
                            {% include 'data/projects/task-row-detail.html' %}
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </form>
            <div class="pb-1">
                {% include "data/common/paginator/paginator.html" with redirect_url=pagination_url %}
            </div>
        </div>
    </div>

    <script>

        // CHECK DATA Table

        // Function to load script dynamically
        function loadScript(url, callback) {
            var script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = url;
            script.onload = callback;
            script.onerror = function() {
                console.error('Failed to load script:', url);
            };
            document.head.appendChild(script);
        }

        // Initialize DataTable after ensuring dependencies are loaded
        function initializeDataTable() {
            let table = $(".task-table-content").DataTable({
                scrollX: true,
                scrollCollapse: true,
                fixedColumns: {
                    left: 3
                },
                ordering: false,
                searching: false,
                paging: false,
                info: false,
                rowReorder: {
                    selector: '.task-grip',
                    update: false
                },
                language: {
                    emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
                }
            });

            table.on('row-reorder', function(e, diff, edit) {
                htmx.trigger('#task-order-form', 'changedTaskOrder')
            });
        }

        // Ensure jQuery is loaded first, then DataTables
        $(document).ready(function() {
            if (typeof $.fn.DataTable === 'undefined') {
                console.log('Loading DataTables scripts...');
                loadScript('https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js', function() {
                    loadScript('https://cdn.datatables.net/fixedcolumns/4.3.0/js/dataTables.fixedColumns.min.js', function() {
                        // Also load rowReorder extension
                        loadScript('https://cdn.datatables.net/rowreorder/1.4.1/js/dataTables.rowReorder.min.js', function() {
                            console.log('All DataTables scripts loaded');
                            initializeDataTable();
                        });
                    });
                });
            } else {
                console.log('DataTables already loaded');
                initializeDataTable();
            }
        });

        // END sss


        function checking_checkbox(element) {
            if (element.checked) {
                document.getElementById('task-bulk-action-container').classList.remove('d-none')
                document.getElementById('task-view-contianer').classList.add('d-none')
            } else {
                taskSelections = document.getElementsByClassName('task-selection')
                for (let i = 0; i < taskSelections.length; i++) {
                    const element = taskSelections[i];
                    if (element.checked) {
                        return
                    }
                }
                document.getElementById('task-bulk-action-container').classList.add('d-none')
                document.getElementById('task-view-contianer').classList.remove('d-none')
            }
        }
    </script>

    <style>
        .loading-spinner{
            display:none;
        }

        .loading-spinner .spinner-border {
            width: 4rem;
            height: 4rem;
        }
        .htmx-request .loading-spinner{
            display:inline-block;
        }
        .htmx-request.loading-spinner{
            display:inline-block;
        }
    </style>
{% endif %}
