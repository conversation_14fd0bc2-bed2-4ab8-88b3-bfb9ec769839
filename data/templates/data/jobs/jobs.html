{% extends 'base.html' %}
{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

{% block content %}

<style>
    .htmx-indicator{
        display:none;
    }
    .htmx-request .htmx-indicator{
        display:inline-block;
    }
    .htmx-request.htmx-indicator{
        display:inline-block;
    }
</style>

<style>
    table {
        width: auto;
    }
    
    td, th {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    td.z-index-3 {
        z-index: 3!important;
    }
</style>


{% include 'data/static/tootip-search-wrapper.html' %}
<div class="d-flex d-flex align-items-center" style="background-color: #FAFAFA !important;">
    {% include 'data/common/module-header-tabs.html' %}

    <div class="w-50 d-flex justify-content-end tw-mr-12" id="view-container">
        {% if permission|check_permission:'edit' %}
        {% comment %} <button class="{% include "data/utility/create-button.html" %} dropdown-toggle dropdown-toggle-split" type="button" {% endcomment %}
        {% comment %} data-bs-toggle="dropdown"  {% endcomment %}
        
        <button class="tw-w-[100px] align-items-center d-flex btn btn-primary btn-md py-1 rounded-1 ms-2 manage-view-settings-button" type="button"

            hx-get="{% url 'job_load_drawer' %}" 
            hx-vals = '{"drawer_type":"new_job","view_id":"{{view_filter.view.id}}"}'
            hx-target="#manage-contacts-view-settings-drawer"
            hx-indicator=".loading-drawer-spinner" 

            style="border-radius: 0.475rem 0 0 0.475rem; height: 32px;"
            >
            <span class="svg-icon svg-icon-4">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"/>
                    <rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"/>
                    <rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"/>
                </svg>
            </span>
    
            <span class="fs-7 ps-1 fw-bolder w-85px">
                {% if LANGUAGE_CODE == 'ja'%}
                新規
                {% else %}
                New
                {% endif %}
            </span>
        </button>
                                
        {% endif %}
    </div>
</div>
{% if permission == 'hide' %}
    {% include 'data/static/no-access-page.html' %}
{% else %}


<div class="d-flex flex-column w-100">
    <div class="px-10 pt-0 py-10">

        {% comment %} Views Part {% endcomment %}
        <div class="{% include "data/utility/tab-pane.html" %}" role="tabpanel" style="z-index:4 !important; top: 0px !important">
            <div class="{% include "data/utility/table-nav.html" %}">
                <div class="d-flex align-items-end justify-content-between w-100" id="deals-view-container">
                    <div class="w-50">
                        <div class="d-flex align-items-center">
                            <div class="nav-item me-1 d-flex align-items-center">
                                <a class="{% include "data/utility/view-menu-default.html" %}" 
                                    type="button"
                                    href = "{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    ビュー
                                    {% else %}
                                    View
                                    {% endif %} 
                                </a>
                                {% if not view_filter.view.title %}
                                <div class="w-20px nav-item justify-content-center d-flex fs-6 text-gray-900">
                                    <button type="button" class="{% include "data/utility/view-plus-link.html" %} manage-view-settings-button"
                                        hx-vals='{"module": "{{menu_key}}", "object_type": "{{object_type}}", "view_id":"{{view_filter.view.id}}"}'
                                        hx-get="{% url 'commerce_view_setting' %}" 
                                        hx-indicator=".loading-drawer-spinner,.view-form"
                                        hx-target="#manage-contacts-view-settings-drawer"
                                        >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                            <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                        </svg>
                                    </button>
                                </div>
                                {% endif %}
                            </div>

                            {% comment %} Put Items here {% endcomment %}
                            {% include 'data/projects/partial-view-menu.html' %}

                            <div class="nav-item fs-6 text-gray-900">
                                <button type="button" class="{% include "data/utility/view-plus-link.html" %} manage-view-settings-button" 
                                    hx-vals='{"module": "{{menu_key}}", "object_type": "{{object_type}}", "view_button":"create"}'
                                    hx-get="{% url 'commerce_view_setting' %}" 
                                    hx-indicator=".loading-drawer-spinner,.view-form"
                                    hx-target="#manage-contacts-view-settings-drawer"
                                    
                                    >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex w-50 justify-content-end">
                        <div class="justify-content-end">
                            <div class="{% include "data/utility/search-wrapper.html" %} hover-tooltip">
                                <span class="search-wrapper-tooltip hover-tooltip-text">
                                    {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}
                                </span>
                                <div class="d-flex align-items-center">
                                    <form id="filter-form-search" method="get" class="w-100">
                                        <div class="d-flex mb-0 position-relative" style="height: 26px;">
                                            <span class="svg-icon svg-icon-3 search-icon-view" onclick="toggleSearch()">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                    <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                                                    <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                                                </svg>
                                            </span>
                                            <input
                                            id="base-search-input" type="text" name="q" class="form-control bg-white ps-12 tw-rounded-lg"
                                            value={% if search_q %} "{{ search_q }}" {% else %}""{% endif %}
                                            placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                                            onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                                            >
                                            {% if view_id %}<input type="hidden" value="{{view_id}}" name="view_id">{% endif %}
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="{% include "data/utility/table-button.html" %}">
                            {% comment %} Export {% endcomment %}
                        </div>

                        <div class="{% include "data/utility/table-button.html" %}">
                        {% comment %} Import {% endcomment %}
                        </div>
                    </div>
                </div>

                <div id="deals-bulk-action-container" class="d-none">
                    <span class="me-10">
                        <button class="py-1 rounded-1 btn btn-sm btn-light fw-bold mt-2 mb-1" onclick="selectAllContacts()">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}
                                すべて選択
                                {% else %}
                                Select All
                                {% endif %}
                            </span>
                        </button>
                        <button class="py-1 rounded-1 btn btn-sm btn-light fw-bold mt-2 mb-1" onclick="deselectAllContacts()">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}
                                選択を解除
                                {% else %}
                                Deselect All
                                {% endif %}
                            </span>
                        </button>
        
                        <script>
                            function selectAllContacts() {
                                selectTaskInputs = document.getElementsByClassName('deals-selection')
                                for (var i = 0; i < selectTaskInputs.length; i++) {
                                    selectTaskInputs[i].checked = true;
                                }
                            }
                            function deselectAllContacts() {
                                selectTaskInputs = document.getElementsByClassName('deals-selection')
                                for (var i = 0; i < selectTaskInputs.length; i++) {
                                    selectTaskInputs[i].checked = false;
                                }
                                document.getElementById('deals-bulk-action-container').classList.add('d-none')
                                document.getElementById('deals-view-container').classList.remove('d-none')
                            }
                        </script>
                    </span>

                    {% if permission|check_permission:'archive' %}
                    <button class="py-1 rounded-1 btn btn-sm btn-light-success fw-bold mt-2 mb-1" data-bs-toggle="modal" data-bs-target="#manage_restore_bulk">
                        {% if LANGUAGE_CODE == 'ja'%}
                        有効化
                        {% else %}
                        Activate
                        {% endif %}
                    </button>
                    <button class="btn btn-sm btn-light-danger py-1 rounded-1 fw-bold mt-2 mb-1" data-bs-toggle="modal" data-bs-target="#manage_delete_bulk">
                        {% if LANGUAGE_CODE == 'ja'%}
                        アーカイブ
                        {% else %}
                        Archive 
                        {% endif %}
                    </button>
                    {% endif %}

                    <script>
                        document.body.addEventListener("hideTaskBulkActionBtn", function(evt){
                            document.getElementById('deals-bulk-action-container').classList.add('d-none')
                            document.getElementById('deals-view-contianer').classList.remove('d-none')
                        })
                    </script>
                    {% if permission|check_permission:'edit' %}
                        <button class="py-1 rounded-1 btn btn-sm btn-light-info fw-bold mt-2 mb-1 create-view-settings-button" type="button"
                        
                            hx-vals='js:{"drawer_type":"deals", "section":"bulk-action", "case_ids": getSelectedCases()}'
                            hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                            hx-target="#manage-view-settings-drawer"
                            hx-swap="innerHTML"
                            hx-trigger="click"

                            style="border-radius: 0.475rem 0 0 0.475rem;"
                            >
                            {% if LANGUAGE_CODE == 'ja'%}
                            アクション
                            {% else %}
                            Action 
                            {% endif %}
                        </button>
                    
                    {% endif %}
                    <script>
                        function getSelectedCases() {
                            var selectedOrders = [];
                            var classNameElements = document.getElementsByClassName("deals-selection");
                            
                            if (classNameElements){
                                classNameElements.forEach(function(classNameElement) {
                                    if (classNameElement.checked) {
                                        selectedOrders.push(classNameElement.value);
                                    }
                                });  
                            }
                            return selectedOrders;
                        }
                    </script>
                </div>
                

            </div>
        </div>
        
        {% comment %} End of Views {% endcomment %}
        
        <div class="mb-2 d-none" id="d-select-additional-options">
            <div class="status bg-light-dark text-center pt-4 pb-3 px-5 rounded">
                {% if LANGUAGE_CODE == 'ja'%}
                このページの全ての案件が選択されました。 
                {% else %}
                All cases on this page are selected. 
                {% endif %}
                 <a onclick="toggleText()" 
                    class="btn btn-primary" 
                    data-bs-toggle="collapse" 
                    id="select-additional-options-toggle" 
                    role="button" 
                    aria-expanded="false" 
                    aria-controls="collapseExample">
                    {% if LANGUAGE_CODE == 'ja'%}
                    全ての案件を選択する
                    {% else %}
                    Select all cases
                    {% endif %}
                </a>  
            </div>
        </div>
        
        <div class="d-flex flex-column flex-lg-row">
            <div class="flex-lg-row-fluid">

                <form method="POST" id="jobs-form">
                    {% csrf_token %}
    
                    <div class="pt-0">
                        <table class="{% include "data/utility/table.html" %}">
                            <thead class="{% include "data/utility/table-header.html" %}">
                                <tr class="align-middle">
                                    {% for job_column in job_columns %}

                                        <th {% if job_column == 'checkbox' %} 
                                            class="min-w-40px text-nowrap" 
                                        {% elif job_column == 'job_id' %}
                                            class="border-right-1 text-nowrap"
                                        {% else %} 
                                            class="text-nowrap" 
                                        {% endif %}>
                                                {% if job_column != 'checkbox' %}
                                                    
                                                    {% if job_column|search_custom_field_object_jobs:request %}
                                                        {% with channel_column=job_column|search_custom_field_object_jobs:request %}
                                                            {{channel_column.name|display_column_jobs:request}}
                                                        {% endwith %}
                                                    {% else %}
                                                        {% with job_column=job_column|display_column_jobs:request %}
                                                            {{job_column}}
                                                        {% endwith %}
                                                    {% endif %}
                                                {% endif %}
                                        </th>
                                        {% if job_column == 'job_id' %} 
                                        <th class="" style="width: 20px;">
                                        </th>
                                        {% endif %}
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody class="fs-6">
                                
                                {% for job in jobs %}
                                    <tr class='w-100'>
                                        {% for row_type in job_columns %}
                                            {% if "checkbox" == row_type %}
                                                <td class="w-40px">
                                                    <input id="{{job.id}}" class="form-check-input" type="checkbox" name="checkbox" value="{{job.id}}" onclick="checking_checkbox(this)"/>
                                                </td>

                                            {% elif "job_id" == row_type %}
                                            
                                                <td class="fw-bolder text-nowrap w-100 w-40px" style="border-right: 1px solid !important; border-right-color: rgb(234, 234, 234) !important;">

                                                    <a class="text-nowrap text-center mb-0 text-dark text-hover-primary fw-bolder cursor-pointer customer-create-wizard job_{{job.id}}" 
                                             
                                                        hx-get="{% host_url 'get_job' id=job.id host 'app' %}"
                                                        hx-target="#customer-drawer"
                                                        hx-trigger="click"
                                                    >
                                                    {{job.job_id|stringformat:"04d"}}
                                                    </a>
                                                </td>
                                                

                                                <td class="" style="width: 20px;">
                                                </td>
                                                
                                            {% elif "title" == row_type %}
                                                
                                                <td class='fw-bolder'>
                                                    {% if job.usage_status != 'archived' %}
                                                    <a class="text-dark text-hover-primary cursor-pointer customer-create-wizard"
                                                        
                                                        hx-get="{% host_url 'get_job' id=job.id host 'app' %}"
                                                        hx-target="#customer-drawer"
                                                        hx-trigger="click" 
                                                    
                                                    >  
                                                        {% if job.title %}
                                                            {{job.title}}
                                                        {% endif %}
                                                    </a>
                                                    {% else %}
                                                        <div class="blurry-text">####<div>
                                                    {% endif %}
                                                    
                                                </td>
                                            
                                            {% elif "description" == row_type %}
                                                <td>
                                                    {% if job.usage_status != 'archived' %}
                                                    {% if job.description %}
                                                        {{job.description}}
                                                    {% endif %}
                                                    {% else %}
                                                        <div class="blurry-text">####<div>
                                                    {% endif %}
                                                </td>

                                            {% elif "status" == row_type %}
                                            <td>
                                                {% if job.usage_status != 'archived' %}
                                                    <span class="">
                                                        {% if 'status'|get_custom_property_object:job %}
                                                            {% with value_map_label='status'|get_custom_property_object:job|get_attr:'value'|string_list_to_list %}
                                                                {% for value, label in value_map_label.items %}
                                                                    {% if job.status == value %}
                                                                        {{label}}
                                                                    {% endif %}
                                                                {% endfor %}
                                                            {% endwith %}
                                                        {% else %}
                                                            {% with job_column=job.status|display_column_jobs:request %}
                                                                {{job_column}}
                                                            {% endwith %}
                                                        {% endif %}
                                                    </span>
                                                {% else %}
                                                    <div class="blurry-text">####<div>
                                                {% endif %}
                                            </td>

                                            {% elif "preview" == row_type %}
                                            <td>
                                                {% if job.usage_status != 'archived' %}
                                                <a target="_blank" href="{% host_url 'job_details' id=job.id host 'static' %}">URL</a>
                                                {% else %}
                                                    <div class="blurry-text">####<div>
                                                {% endif %}
                                            </td>

                                            {% elif "applications" == row_type %}
                                            <td>
                                                {% if job.usage_status != 'archived' %}
                                                <a class="text-nowrap text-center mb-0 text-dark text-hover-primary fw-bolder cursor-pointer" 
                                                    href="{% host_url 'job_applicants_view' job_id=job.id host 'app' %}"
                                                    >
                                                    <span class="menu-icon me-1">
                                                        <span class="svg-icon svg-icon-2">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-people" viewBox="0 0 16 16">
                                                                <path d="M15 14s1 0 1-1-1-4-5-4-5 3-5 4 1 1 1 1zm-7.978-1L7 12.996c.001-.264.167-1.03.76-1.72C8.312 10.629 9.282 10 11 10c1.717 0 2.687.63 3.24 1.276.593.69.758 1.457.76 1.72l-.008.002-.014.002zM11 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4m3-2a3 3 0 1 1-6 0 3 3 0 0 1 6 0M6.936 9.28a6 6 0 0 0-1.23-.247A7 7 0 0 0 5 9c-4 0-5 3-5 4q0 1 1 1h4.216A2.24 2.24 0 0 1 5 13c0-1.01.377-2.042 1.09-2.904.243-.294.526-.569.846-.816M4.92 10A5.5 5.5 0 0 0 4 13H1c0-.26.164-1.03.76-1.724.545-.636 1.492-1.256 3.16-1.275ZM1.5 5.5a3 3 0 1 1 6 0 3 3 0 0 1-6 0m3-2a2 2 0 1 0 0 4 2 2 0 0 0 0-4"/>
                                                              </svg>
                                                              
                                                        </span>
                                                    </span>
                                                    <span class="fs-2 fw-bold {% if LANGUAGE_CODE == 'ja'%} fs-lg-7 {% else %} fs-lg-6 {% endif %}">
                                                        {% if LANGUAGE_CODE == 'ja'%}
                                                        応募者
                                                        {% else %}
                                                        Applicants
                                                        {% endif %}
                                                    </span>
                                                    |
                                                    <span class="fs-2 fw-bold {% if LANGUAGE_CODE == 'ja'%} fs-lg-7 {% else %} fs-lg-6 {% endif %}">
                                                        {{job.jobapplication_set.all|length}}
                                                    </span>
                                                </a>
                                                {% else %}
                                                    <div class="blurry-text">####<div>
                                                {% endif %}

                                            </td>

                                            {% elif "interview" == row_type %}
                                            <td>
                                                {% if job.usage_status != 'archived' %}
                                                <a class="text-nowrap text-center mb-0 text-dark text-hover-primary fw-bolder cursor-pointer" 
                                                    href="{% host_url 'job_interview' job_id=job.id host 'app' %}"
                                                    >
                                                    <span class="menu-icon me-1">
                                                        <span class="svg-icon svg-icon-2">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-chat-right-quote" viewBox="0 0 16 16">
                                                                <path d="M2 1a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h9.586a2 2 0 0 1 1.414.586l2 2V2a1 1 0 0 0-1-1zm12-1a2 2 0 0 1 2 2v12.793a.5.5 0 0 1-.854.353l-2.853-2.853a1 1 0 0 0-.707-.293H2a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2z"/>
                                                                <path d="M7.066 4.76A1.665 1.665 0 0 0 4 5.668a1.667 1.667 0 0 0 2.561 1.406c-.131.389-.375.804-.777 1.22a.417.417 0 1 0 .6.58c1.486-1.54 1.293-3.214.682-4.112zm4 0A1.665 1.665 0 0 0 8 5.668a1.667 1.667 0 0 0 2.561 1.406c-.131.389-.375.804-.777 1.22a.417.417 0 1 0 .6.58c1.486-1.54 1.293-3.214.682-4.112z"/>
                                                              </svg>
                                                        </span>
                                                    </span>
                                                    <span class="fs-2 fw-bold {% if LANGUAGE_CODE == 'ja'%} fs-lg-7 {% else %} fs-lg-6 {% endif %}">
                                                        {% if LANGUAGE_CODE == 'ja'%}
                                                        面接
                                                        {% else %}
                                                        Interviews
                                                        {% endif %}
                                                    </span>
                                                    |
                                                    <span class="fs-2 fw-bold {% if LANGUAGE_CODE == 'ja'%} fs-lg-7 {% else %} fs-lg-6 {% endif %}">
                                                        {{job.interview_set.all|length}}
                                                    </span>
                                                </a>

                                                {% else %}
                                                    <div class="blurry-text">####<div>
                                                {% endif %}
                                            </td>

                                            {% elif "scorecard" == row_type %}
                                            <td>
                                                {% if job.usage_status != 'archived' %}
                                                <a class="text-nowrap text-center mb-0 text-dark text-hover-primary fw-bolder cursor-pointer" 
                                                    href="{% host_url 'job_scorecard' job_id=job.id host 'app' %}"
                                                    >
                                                    <span class="menu-icon me-1">
                                                        <span class="svg-icon svg-icon-2">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-pencil-square" viewBox="0 0 16 16">
                                                                <path d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z"/>
                                                                <path fill-rule="evenodd" d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5z"/>
                                                              </svg>
                                                              
                                                        </span>
                                                    </span>
                                                    <span class="fs-2 fw-bold {% if LANGUAGE_CODE == 'ja'%} fs-lg-7 {% else %} fs-lg-6 {% endif %}">
                                                        {% if LANGUAGE_CODE == 'ja'%}
                                                        スコアカード
                                                        {% else %}
                                                        Scorecards
                                                        {% endif %}
                                                    </span>
                                                    |
                                                    <span class="fs-2 fw-bold {% if LANGUAGE_CODE == 'ja'%} fs-lg-7 {% else %} fs-lg-6 {% endif %}">
                                                        {{job.scorecard_set.all|length}}
                                                    </span>
                                                </a>
                                                {% else %}
                                                    <div class="blurry-text">####<div>
                                                {% endif %}

                                            </td>


                                            {% elif "location" == row_type %}
                                            <td>
                                                {% if job.usage_status != 'archived' %}
                                                    {{job.location|title}}
                                                {% else %}
                                                    <div class="blurry-text">####<div>
                                                {% endif %}
                                            </td>

                                            {% elif "job_type" == row_type %}
                                            <td>
                                                {% if job.usage_status != 'archived' %}
                                                    <span class="">
                                                        {% if 'job_type'|get_custom_property_object:job %}
                                                            {% with value_map_label='job_type'|get_custom_property_object:job|get_attr:'value'|string_list_to_list %}
                                                                {% for value, label in value_map_label.items %}
                                                                    {% if job.job_type == value %}
                                                                        {{label}}
                                                                    {% endif %}
                                                                {% endfor %}
                                                            {% endwith %}
                                                        {% else %}
                                                            {% with job_column=job.job_type|display_column_jobs:request %}
                                                                {{job_column}}
                                                            {% endwith %}
                                                        {% endif %}
                                                    </span>
                                                {% else %}
                                                <div class="blurry-text">####<div>
                                                {% endif %}
                                            </td>

                                            
                                            {% elif "created_at" == row_type %}
                                                <td>
                                                    {% if job.usage_status != 'archived' %}
                                                    {% if job.created_at %}
                                                        {% date_format job.created_at 1 %}
                                                    {% endif %}
                                                    {% else %}
                                                    <div class="blurry-text">####<div>
                                                    {% endif %}
                                                </td>
                                            
                                            {% elif "usage_status" == row_type %}
                                                <td>
                                                    {% if job.usage_status != 'archived' %}
                                                      
                                                        {% if job.usage_status == 'active' %}
                                                            {% if LANGUAGE_CODE == 'ja'%}有効{% else %}Active{% endif %}
                                                        {% else %}
                                                            {% if LANGUAGE_CODE == 'ja'%}アーカイブ{% else %}Archive{% endif %}
                                                        {% endif %}
                                                   
                                                    {% else %}
                                                    <div class="blurry-text">####<div>
                                                    {% endif %}
                                                </td>



                                            {% elif row_type|is_uuid %}
                                                <td class="fw-bold">
                                                    <div class="text-start">
                                                        {% if job.usage_status != 'archived' %}
                                                            {% with job_id=job.id|stringify %}
                                                                {% with args=row_type|add:'|'|add:"jobs"|add:'|'|add:job_id %} 
                                                                    {% with column_display=args|get_column_display:request %}
                                                                     
                                                                        {% if column_display.type == 'formula' %}
                                                                            {% include "data/common/custom_field/formula/row-partial.html" with obj_id=job.id CustomFieldName_id=column_display.id %}                            
                                                                        {% else %}
                                                                            {% include "data/common/custom_field/row-partial-list.html" with CustomFieldName=column_display obj_id=column_display.value value=column_display.value %}
                                                                        {% endif %}
                                                                    {% endwith %}
                                                                {% endwith %}   
                                                            {% endwith%}
                                                        {% endif %}
                                                    </div>
                                                </td>   
                                        
                                            {% else %}

                                                <td class="fw-bold">
                                                    <div class="text-start">
                                                        {% if job.usage_status != 'archived' %}
                                                            {% if job|get_attr:row_type %}
                                                                {{job|get_attr:row_type}}
                                                            {% endif %}
                                                        {% endif %}
                                                    </div>
                                                </td>
                                  
                                            {% endif %}

                                        {% endfor %}
                                    </tr>
                                {% endfor %}
                                
                            </tbody> 
                        </table>    
                    </div>
                </form>
            
                <div class="{% include "data/utility/pagination.html" %}">
                    {% if LANGUAGE_CODE == 'ja'%}
                    {{paginator_item_begin}}–{{paginator_item_end}} の {{paginator.count}} 件
                    {% else %}
                    Viewing {{paginator_item_begin}}–{{paginator_item_end}} of {{paginator.count}} results
                    {% endif %}
    
                    <div>
                        
                        {% if page_content.has_previous %}     
                            <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page=1&{% query_transform %}">&laquo; First</a>
                            <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.previous_page_number }}&{% query_transform %}">Previous</a>
                        {% endif %}
                                
                        {% if page_content.has_next %}
                            <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.next_page_number }}&{% query_transform %}">Next</a>
                            <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.paginator.num_pages }}&{% query_transform %}">Last &raquo;</a>
                        {% endif %}
                    </div>
                </div>
                
            </div>
        </div>

    
    </div>
</div>
{% endif %}



{% endblock %}
{% block js %}
<script>
    $(document).ready(function() {
        $('.select2-this').select2();
    });

    $(document).ready(function() {
        var table = $(".table-content").DataTable({
            scrollX:        true,
            scrollCollapse: true,
            fixedColumns:   {
                left: 2
            },
            ordering: false,
            searching: false,  // Hide the search bar
            paging: false,      // Hide pagination
            info: false,        // Hide the information text
            language: {
                emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
            }
        });

        var dropdowns = $('.dropdown-toggle', table.cells(null, 1).nodes());
        var dropdown = dropdowns.each((index, dropdownToggleEl) => {
            var instance = new bootstrap.Dropdown(dropdownToggleEl, {
                popperConfig(defaultBsPopperConfig) {
                    return { ...defaultBsPopperConfig, strategy: "fixed" };
                },
            });

            dropdownToggleEl.addEventListener("show.bs.dropdown", function (event) {
                $(event.target).closest("td").addClass("z-index-3");
            });

            dropdownToggleEl.addEventListener("hide.bs.dropdown", function (event) {
                $(event.target).closest("td").removeClass("z-index-3");
            });
        });

    });

    function getTitleValue(elm){
        let res = ''
        elm.closest('form').getElementsByTagName('input').forEach(function(item) {if (item.name == 'title') {
            res = item.value
        }})
        return res
    }

    function getLocationValue(elm){
        let res = ''
        elm.closest('form').getElementsByTagName('input').forEach(function(item) {if (item.name == 'location') {
            res = item.value
        }})
        return res
    }

    function getJobTypeValue(elm){
        let res = ''
        elm.closest('form').getElementsByTagName('select').forEach(function(item) {if (item.name == 'job_type') {
            res = item.value
        }})
        return res
    }
</script>

{% include 'data/javascript/toggleSearch.html' %} 

{% endblock %}
