{% extends 'base.html' %}
{% load tz %}
{% load custom_tags %}
{% load i18n %}
{% load hosts %}
{% load humanize %}
{% get_current_language as LANGUAGE_CODE %}
{% block content %}

{% include "data/utility/table-css.html" %}

{% include 'data/static/tootip-search-wrapper.html' %}
<div class="d-flex d-flex align-items-center" style="background-color: #FAFAFA !important;">
    {% include 'data/common/module-header-tabs.html' %}

    <div class="w-50 d-flex justify-content-end tw-mr-5" id="view-container">

        <div class="{% include "data/utility/header-action-button.html" %}">
            <div class="{% include "data/utility/table-button.html" %}">
                <button class="btn tw-font-[500] tw-rounded-l-lg tw-rounded-r-none tw-border-0 tw-bg-gray-200 hover:tw-bg-gray-300 max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 py-1 justify-content-center px-1 customer-create-wizard" type="button"
                    hx-get="{% url 'load_drawer_sync_contacts' %}"
                    hx-vals='{"drawer_type":"company-view-export-import","view_id":"{{view_filter.view.id}}","page": "company","import_export_type":"export"}'
                    hx-include="[name='checkbox']"
                    hx-target="#customer-drawer"
                    hx-trigger="click"
                    hx-swap="innerHTML"
                    >
    
                    <span class="svg-icon svg-icon-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                            <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                        </svg>
                    </span>
                    <span class="">
                        {% if LANGUAGE_CODE == 'ja'%}
                        エクスポート
                        {% else %}
                        Export
                        {% endif %}
                    </span>
                </button>
            </div>
            <div class="{% include "data/utility/table-button.html" %}">
                {% if permission|check_permission:'edit' %}
                
                <button class="btn tw-font-[500] tw-rounded-l-none tw-rounded-r-lg tw-border-0 tw-bg-gray-200 hover:tw-bg-gray-300 max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 py-1 justify-content-center px-1 customer-create-wizard" type="button"
                        hx-get="{% url 'load_drawer_sync_contacts' %}"
                        hx-vals='{"drawer_type":"company-view-export-import","view_id":"{{view_filter.view.id}}","page": "company","import_export_type":"import"}'
                        hx-target="#customer-drawer"
                        hx-trigger="click"
                        >
                    <span class="svg-icon svg-icon-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-download" viewBox="0 0 16 16">
                            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                            <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708z"/>
                        </svg>
                    </span>
                    <span class="">
                        {% if LANGUAGE_CODE == 'ja'%}
                        インポート
                        {% else %}
                        Import
                        {% endif %}
                    </span>
                </button>
    
                {% endif %}
            </div>
        </div>

        {% if permission|check_permission:'edit' %}
            {% if not property_sets %}
                
                <button class="tw-w-[100px] align-items-center d-flex btn btn-primary btn-md py-1 rounded-1 view_form_trigger customer-create-wizard" type="button"
                    hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                    hx-vals='{"drawer_type":"companies", "view_id": "{{view_filter.view.id}}","set_id":"{{set_id}}" }'
                    hx-target="#customer-drawer"
                    hx-trigger="click"
                    style="height: 26px;"
                    >
                    <span class="svg-icon svg-icon-4">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8.00065 3.33301V12.6663M3.33398 7.99967H12.6673" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </span>

                    <span class="fs-7 ps-1 fw-bolder w-85px">
                        {% if LANGUAGE_CODE == 'ja'%}
                        新規
                        {% else %}
                        New 
                        {% endif %}
                    </span>
                </button>
            {% else %}
                <div class="btn-group tw-h-[32px]">
                    <button class="tw-w-[100px] align-items-center d-flex btn btn-primary btn-md customer-create-wizard view_form_trigger py-1" type="button"
                        hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                        hx-vals='{"drawer_type":"companies", "view_id": "{{view_filter.view.id}}","set_id":"{{set_id}}" }'
                        hx-target="#customer-drawer"
                        style="border-radius: 0.475rem 0 0 0.475rem;"
                        >
                        <span class="svg-icon svg-icon-4">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8.00065 3.33301V12.6663M3.33398 7.99967H12.6673" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </span>
                        <span class="fs-7 ps-1 fw-bolder w-85px">
                            {% if LANGUAGE_CODE == 'ja'%}
                            新規
                            {% else %}
                            New
                            {% endif %}
                        </span>
                    </button>
                    <button type="button"
                        class="tw-w-[30px] align-items-center d-flex btn btn-primary btn-md dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split"
                        data-bs-toggle="dropdown"
                        aria-expanded="false"
                        style="border-radius: 0 0.475rem 0.475rem 0;border-left: 1px solid #dee2e6;"
                    >
                        <span class="svg-icon svg-icon-4">
                            <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                                <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                            </svg>
                        </span>
                    </button>
                    <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                        {% for set in property_sets %}
                        <li>
                            <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden customer-create-wizard" type="button"
                                hx-get="{% url 'new_customerlinkapp_drawer' %}"
                                hx-vals = '{"drawer_type":"companies", "view_id": "{{view_id}}", "set_id": "{{set.id}}"}'
                                hx-target="#customer-drawer"
                                style="border-radius: 0.475rem 0 0 0.475rem;"
                                >
                                {% if set.name %}
                                    {{ set.name}}
                                {% else %}
                                    {% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default{% endif %}
                                {% endif %}
                            </button>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            {% endif %}
        {% endif %}
    </div>
</div>

{% if permission == 'hide' %}
    {% include 'data/static/no-access-page.html' %}
{% else %}

{% include "data/common/advance_search/advance-search-style.html" %}

{% if permission|check_permission:'edit' %}
    <div id='modal-load'
        hx-vals='{"object_type": "{{object_type}}","module": "{{menu_key}}","view_id": "{{view_id}}"}'
        hx-get="{% url 'get_bulk_update_properties' %}" 
        hx-trigger='load'
        hx-target="this">
    </div>
{% endif %}
{% comment %} Table Start {% endcomment %}
<div class="w-100 px-0">
    {% include 'data/common/permission-action-warning-message.html' %}
    {% comment %} Views Part {% endcomment %}
    <div class="{% include "data/utility/tab-pane.html" %}" role="tabpanel" style="z-index:4 !important;">
        <div class="{% include "data/utility/table-nav.html" %}">
            <div class="{% include "data/utility/table-content.html" %}" id="view-container">
                {% comment %} Desktop {% endcomment %}
                <div class="max-md:tw-hidden w-75 d-flex align-items-center">
                    <div class="nav-item fs-6 text-gray-900">
                        <div class="d-flex align-items-center justify-content-center">
                            <button type="button" class="align-items-center justify-content-center d-flex btn text-gray-600 bg-transparent text-hover-primary tw-px-[5px] create-view-settings-button mb-2 tw-w-[30px]"
                                style="height: 26px;"
                                hx-vals='{"drawer_type":"contacts-view-settings","page":"companies"}'
                                hx-get="{% url 'new_customerlinkapp_drawer' %}"
                                hx-target="#manage-view-settings-drawer"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>

                    {% include 'data/projects/partial-dropdown-view-menu.html' %}

                    <div class="{% include "data/utility/view-menu-nav-item.html" %}">
                        <a class="{% include "data/utility/view-menu-default.html" %}"
                            type="button"
                            href = "{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">

                            {% if LANGUAGE_CODE == 'ja'%}
                            ビュー
                            {% else %}
                            View
                            {% endif %}
                        </a>
                        {% if not view_filter.view.title %}
                        <div class="w-20px nav-item justify-content-center d-flex fs-6 text-gray-900">

                            <button type="button" class="{% include "data/utility/view-plus-link.html" %} update-view-settings-button"
                                hx-vals='{"module": "{{menu_key}}", "drawer_type":"contacts-view-settings","page":"companies","view_id":"{{view_filter.view.id}}"}'
                                hx-get="{% url 'new_customerlinkapp_drawer' %}"
                                hx-target="#manage-update-view-settings-drawer"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                                >

                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                    <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                </svg>
                            </button>
                        </div>
                        {% endif %}
                    </div>

                    {% comment %} Put Items here {% endcomment %}
                    {% include 'data/projects/partial-view-menu.html' %}

                </div>

                <div class="tw-hidden max-md:tw-flex tw-w-1/2">
                    <div class="d-flex align-items-center">
                        <!-- Example split danger button -->
                        <div class="btn-group mb-2">
                            <button type="button" class="tw-max-w-[90px] align-items-center d-flex btn bg-white border btn-md tw-px-[10px] customer-create-wizard"
                                style="height: 26px; border-radius: 0.475rem 0 0 0.475rem;"
                                hx-vals='{"drawer_type":"contacts-view-settings","page": "companies", "view_id":"{{view_filter.view.id}}"}'
                                hx-get="{% url 'new_customerlinkapp_drawer' %}"
                                hx-target="#customer-drawer"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                            >
                                <span class="fs-7 ps-1 fw-bolder tw-text-ellipsis tw-overflow-hidden tw-text-nowrap">
                                    {% if view_filter.view.title %}
                                        {{ view_filter.view.title }}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ja'%}ビュー{% else %}View{% endif %}
                                    {% endif %}
                                </span>
                            </button>
                            <button type="button"
                                class="tw-w-[30px] align-items-center d-flex btn btn-white border btn-md  dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split"
                                data-bs-toggle="dropdown"
                                aria-expanded="false"
                                style="height: 26px; border-radius: 0 0.475rem 0.475rem 0;"
                            >
                                <span class="svg-icon svg-icon-4">
                                    <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                                        <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                                    </svg>
                                </span>
                            </button>
                            <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                                <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">{% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default View{% endif %}</a></li>
                                <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden {% if request.GET.status == 'archived' %}active fw-bolder{% endif %}" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?status=archived">{% if LANGUAGE_CODE == 'ja' %}アーカイブ{% else %}Archived{% endif %}</a></li>

                                {% for view in views %}
                                    {% if view.title %}
                                        <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?view_id={{view.id}}">{{view.title}}</a></li>
                                    {% endif %}
                                {% endfor %}
                            </ul>
                        </div>
                        <div class="fs-6 text-gray-900 mb-2">
                            <button type="button" class="tw-border-0 tw-bg-transparent tw-text-gray-900 customer-create-wizard"
                                hx-vals='{"drawer_type":"contacts-view-settings","page": "companies"}'
                                hx-get="{% url 'new_customerlinkapp_drawer' %}"
                                hx-target="#customer-drawer"
                                hx-trigger="click"
                                hx-swap="innerHTML">

                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="d-flex w-50 justify-content-end" id="view-container-1">
                    <div class="{% include "data/utility/search-container.html" %}">
                        <div class="{% include "data/utility/search-wrapper.html" %}">
                            <div class="d-flex align-items-center">
                                <form id="filter-form-search" method="get" class="w-100">
                                    <div class="d-flex mb-0 position-relative align-items-center" style="height: 26px;">
                                        <span class="svg-icon svg-icon-3 search-icon-view">
                                            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M13 13L10.1 10.1M11.6667 6.33333C11.6667 9.27885 9.27885 11.6667 6.33333 11.6667C3.38781 11.6667 1 9.27885 1 6.33333C1 3.38781 3.38781 1 6.33333 1C9.27885 1 11.6667 3.38781 11.6667 6.33333Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        {% if view_id %}
                                        <input type="hidden" name="view_id" value="{{view_id}}">
                                        {% endif %}
                                        <input
                                        id="base-search-input" type="text" name="q" class="form-control bg-white ps-12 pe-16 h-25px tw-rounded-lg"
                                        value="{% if advance_search_filter %}{% for k, v in advance_search_filter.items %}{% if k|search_custom_field_object_company:request %}{% with channel_column=k|search_custom_field_object_company:request %}{{channel_column.name}}{% endwith %}{% else %}{{ k|display_column_company:request }}{% endif %} {% translate_lang v.key|display_filter_rule:LANGUAGE_CODE LANGUAGE_CODE %} {{ v.value }}{% if not forloop.last %}, {% endif %}{% endfor %}{% elif search_q %}{{ search_q }}{% else %}{% endif %}"
                                        {% if page_type != 'companies' %}
                                        placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                                        {% else %}
                                        placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                                        {% endif %}
                                        onkeypress="if(event.keyCode == 13)document.forms['filter-form-search'].submit();"
                                        >
                                        {% if request.GET.status == 'archived' %}
                                        <input type="hidden" value="archived" name="status">
                                        {% endif %}
                                        <span class="svg-icon {% if advance_search.is_active %}svg-icon-primary{%else%}svg-icon-muted{% endif %} svg-icon-3 filter-icon filter-drawer-btn me-7"
                                            hx-get="{% url 'advance_search_drawer' %}"
                                            hx-vals='{"object_type": "company", "module": "{{menu_key}}", "view_id": "{{view_id}}"}'
                                            hx-indicator=".loading-drawer-spinner"
                                            hx-target="#filter-drawer-content"
                                            hx-trigger="click"
                                            hx-swap="innerHTML"
                                        >
                                            <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M3 5H11M1 1H13M5 9H9" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn"
                                            hx-get="{% url 'advance_search_drawer' %}"
                                            hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True", "view_id": "{{view_id}}"}'
                                            hx-indicator=".loading-drawer-spinner"
                                            hx-target="#filter-drawer-content"
                                            hx-trigger="click"
                                            hx-swap="innerHTML"
                                        >
                                            <svg width="14" height="12" viewBox="0 0 14 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M1 3.33301L9 3.33301M9 3.33301C9 4.43758 9.89543 5.33301 11 5.33301C12.1046 5.33301 13 4.43758 13 3.33301C13 2.22844 12.1046 1.33301 11 1.33301C9.89543 1.33301 9 2.22844 9 3.33301ZM5 8.66634L13 8.66634M5 8.66634C5 9.77091 4.10457 10.6663 3 10.6663C1.89543 10.6663 1 9.77091 1 8.66634C1 7.56177 1.89543 6.66634 3 6.66634C4.10457 6.66634 5 7.56177 5 8.66634Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        {% if view_id %}<input type="hidden" value="{{view_id}}" name="view_id">{% endif %}
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="{% include "data/utility/view-menu-search.html" %}">
                        <button
                            onclick="openSearch()"
                            class="tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer hover-tooltip" type="button"
                            >
                            <span class="search-wrapper-tooltip hover-tooltip-text">
                                {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}
                            </span>
                            <span class="tw-flex svg-icon svg-icon-3">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                                    <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                                </svg>
                            </span>
                        </button>
                    </div>
                </div>
            </div>
            {% comment %} Desktop {% endcomment %}


            <div id="view-header-container" class="tw-hidden w-100 px-5">
                <div class="justify-content-between align-items-center flex-row d-flex">
                    <div class="w-100">
                        {% include 'data/common/select-all-in-view-button.html' %}
                        
                        {% if permission|check_permission:'edit' %}
                        <button class="py-1 rounded-1 btn btn-sm btn-light-primary fw-bold mt-2 mb-1" onclick="bulk_check_toggle(),check_permission_action(event, 'edit', 'edit_bulk_modal')">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}
                                編集
                                {% else %}
                                Edit
                                {% endif %}
                            </span>
                        </button>
                        <button class="py-1 rounded-1 btn btn-sm btn-light-warning fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'edit')" name="bulk_duplicate" type="submit" form="company-form">
                            {% if LANGUAGE_CODE == 'ja'%}
                            複製
                            {% else %}
                            Duplicate
                            {% endif %}
                        </button>
                        {% endif %}
                        {% if permission|check_permission:'archive' %}
                            <button class="py-1 rounded-1 btn btn-sm btn-light-success me-1 fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'archive', 'manage_restore_bulk')">
                                {% if LANGUAGE_CODE == 'ja'%}
                                有効化
                                {% else %}
                                Activate
                                {% endif %}
                            </button>
                            <button class="btn btn-sm btn-light-danger py-1 rounded-1 me-1 fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'archive', 'manage_delete_bulk')">
                                {% if LANGUAGE_CODE == 'ja'%}
                                アーカイブ
                                {% else %}
                                Archive
                                {% endif %}
                            </button>
                        {% endif %}
                        <script>
                            document.body.addEventListener("hideTaskBulkActionBtn", function(evt){
                                document.getElementById('task-bulk-action-container').classList.add('d-none')
                                document.getElementById('task-view-contianer').classList.remove('d-none')
                            })
                        </script>
                    </div>

                    <div class="d-flex">
                        {% if permission|check_permission:'edit' %}

                        <div class="{% include "data/utility/table-button.html" %}">
                            
                            <button class="{% include "data/utility/gray-header-button.html" %} customer-create-wizard" type="button"
                                hx-get="{% url 'load_drawer_sync_contacts' %}"
                                onclick="fillActionCompanyIds(this)"
                                hx-include="[name='checkbox']"
                                hx-target="#customer-drawer"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                                >
                                <span class="svg-icon svg-icon-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                                        <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                                    </svg>
                                </span>
                                <span class="">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    エクスポート
                                    {% else %}
                                    Export
                                    {% endif %}
                                </span>
                            </button>
                        </div>

                        <script>
                            function fillActionCompanyIds(elm) {
                                // Now set the hx-vals attribute with the updated company IDs
                                elm.setAttribute('hx-vals', 'js:{"drawer_type":"company-view-export-import", "view_id": "{{view_filter.view.id}}", "page": "company", "import_export_type":"export", "company_ids": getSelectedCompany(), "open_drawer": "{{open_drawer}}"}');
                            }

                            function getSelectedCompany() {
                                var selectedCompany = [];
                                var classNameElements = document.getElementsByClassName("company-selection");
                                if (classNameElements){
                                    classNameElements.forEach(function(classNameElement) {
                                        if (classNameElement.checked) {
                                            selectedCompany.push(classNameElement.value);
                                        }
                                    });  
                                }
                                return selectedCompany;
                            }
                        </script>
                        {% endif %}
                    </div>
                </div>
                {% include 'data/common/select-all-in-view-record-msg.html' %}
            </div>
        </div>
    </div>
    {% comment %} End of Views {% endcomment %}

    <div class="{% if search_q %}max-md:tw-flex {% else %}max-md:tw-hidden {% endif %}tw-hidden tw-items-center tw-py-2 border-bottom border-bottom-1 tw-transition-all tw-duration-300" id="search-bar">
        <form id="filter-form-search" method="get" class="w-100">
            <div class="d-flex mb-0 position-relative align-items-center" style="height: 26px;">
                <span class="svg-icon svg-icon-3 search-icon-view">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                        <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                    </svg>
                </span>
                {% if view_id %}
                <input type="hidden" name="view_id" value="{{view_id}}">
                {% endif %}
                <input
                id="base-search-input-mobile" type="text" name="q" class="form-control bg-white ps-12 pe-16"
                value="{% if advance_search_filter %}{% for k, v in advance_search_filter.items %}{% if k|search_custom_field_object_company:request %}{% with channel_column=k|search_custom_field_object_company:request %}{{channel_column.name}}{% endwith %}{% else %}{{ k|display_column_company:request }}{% endif %} {% translate_lang v.key|display_filter_rule:LANGUAGE_CODE LANGUAGE_CODE %} {{ v.value }}{% if not forloop.last %}, {% endif %}{% endfor %}{% elif search_q %}{{ search_q }}{% else %}{% endif %}"
                {% if page_type != 'companies' %}
                placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                {% else %}
                placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                {% endif %}
                onkeypress="if(event.keyCode == 13)document.forms['filter-form-search'].submit();"
                >
                {% if request.GET.status == 'archived' %}
                <input type="hidden" value="archived" name="status">
                {% endif %}
                <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn me-7"
                    hx-get="{% url 'advance_search_drawer' %}"
                    hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True", "view_id": "{{view_id}}"}'
                    hx-indicator=".loading-drawer-spinner"
                    hx-target="#filter-drawer-content"
                    hx-trigger="click"
                    hx-swap="innerHTML"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path fill="black" d="M19.43 12.98c.04-.32.07-.65.07-.98s-.03-.66-.07-.98l2.11-1.65a.5.5 0 0 0 .12-.63l-2-3.46a.5.5 0 0 0-.6-.22l-2.49 1a7.03 7.03 0 0 0-1.7-.98l-.38-2.65A.5.5 0 0 0 14 2h-4a.5.5 0 0 0-.5.45l-.38 2.65c-.63.25-1.21.57-1.74.98l-2.49-1a.5.5 0 0 0-.6.22l-2 3.46a.5.5 0 0 0 .12.63L4.57 11c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65a.5.5 0 0 0-.12.63l2 3.46a.5.5 0 0 0 .6.22l2.49-1c.53.41 1.11.73 1.74.98l.38 2.65c.03.24.24.45.5.45h4c.26 0 .47-.21.5-.45l.38-2.65c.63-.25 1.21-.57 1.74-.98l2.49 1a.5.5 0 0 0 .6-.22l2-3.46a.5.5 0 0 0-.12-.63l-2.11-1.65zM12 15.5A3.5 3.5 0 1 1 12 8a3.5 3.5 0 0 1 0 7.5z"/>
                    </svg>
                </span>
                <span class="svg-icon {% if advance_search.is_active %}svg-icon-primary{%else%}svg-icon-muted{% endif %} svg-icon-3 filter-icon filter-drawer-btn"
                    hx-get="{% url 'advance_search_drawer' %}"
                    hx-vals='{"object_type": "company", "module": "{{menu_key}}", "view_id": "{{view_id}}"}'
                    hx-indicator=".loading-drawer-spinner"
                    hx-target="#filter-drawer-content"
                    hx-trigger="click"
                    hx-swap="innerHTML"
                >
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M21 7H3C2.4 7 2 6.6 2 6V4C2 3.4 2.4 3 3 3H21C21.6 3 22 3.4 22 4V6C22 6.6 21.6 7 21 7Z" fill="currentColor"/>
                        <path opacity="0.3" d="M21 14H3C2.4 14 2 13.6 2 13V11C2 10.4 2.4 10 3 10H21C21.6 10 22 10.4 22 11V13C22 13.6 21.6 14 21 14ZM22 20V18C22 17.4 21.6 17 21 17H3C2.4 17 2 17.4 2 18V20C2 20.6 2.4 21 3 21H21C21.6 21 22 20.6 22 20Z" fill="currentColor"/>
                    </svg>
                </span>
                <input type="hidden" value="{{view_id}}" name="view_id">
            </div>
        </form>
    </div>


    <div class="d-flex flex-column flex-lg-row">
        <div class="flex-lg-row-fluid">
            <form method="POST" action="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}" id="company-form">
                {% csrf_token %}

                {% if companies_columns %}
                <input type="hidden" name="companies_columns" value="{{companies_columns}}" />
                {% endif %}

                {% if config_view == 'list' %}
                <div class="pt-0 table-responsive" style="max-height: 75vh;">

                    <table class="{% include "data/utility/table.html" %} companies-table">
                        <thead class="{% include "data/utility/table-header.html" %} position-sticky">
                            <tr class="align-middle border-bottom border-bottom-1">
                                {% for company_column in companies_columns %}
                                <th {% if company_column == 'checkbox' %}
                                        class="{% include 'data/utility/column-checkbox.html' %}"
                                    {% elif company_column == 'company_id'  %}
                                        class="{% include 'data/utility/column-id.html' %}"
                                    {% else %}
                                        class="text-nowrap"
                                    {% endif %}>
                                        {% if company_column != 'checkbox' %}
                                            {% if company_column|search_custom_field_object_company:request %}
                                                {% with channel_column=company_column|search_custom_field_object_company:request %}
                                                    {{channel_column.name|display_column_company:request}}
                                                {% endwith %}
                                            {% elif "child" in company_column and "|" in company_column %}
                                                {% with channel_column=company_column|search_custom_field_object_company_child:request %}
                                                    {{channel_column.name|display_column_company:request}} |
                                                    {% if LANGUAGE_CODE == 'ja' %}子オブジェクト{% else %}Child Objects{% endif %}
                                                {% endwith %}
                                            {% else %}
                                                {% with company_column=company_column|display_column_company:request %}
                                                    {{company_column}}
                                                {% endwith %}
                                            {% endif %}
                                        {% endif %}
                                </th>
                                {% if company_column == 'company_id' %}
                                <th class="" style="width: 20px;">
                                </th>
                                {% endif %}
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody class="fs-6">

                            {% for company in companies %}
                                <tr id="row-{{company.id}}"
                                hx-get="{% host_url 'company_row_detail' company.id host 'app' %}"
                                hx-vals='{"view_id": "{{view_filter.view.id}}", "selected_company_id": "{{selected_company_id}}", "page": "{{page}}", "target":"{{target}}"}'
                                hx-trigger="load"
                                hx-indicator=".row_load-{{company.id}}">
                                    {% comment %}Calculate total columns: base columns + extra column for company_id{% endcomment %}
                                    {% if 'company_id' in companies_columns %}
                                        {% include "data/utility/table-row-loading.html" with object_id=company.id colspan=companies_columns|length|add:1 %}
                                    {% else %}
                                        {% include "data/utility/table-row-loading.html" with object_id=company.id colspan=companies_columns|length %}
                                    {% endif %}
                                </tr>
                            {% endfor %}

                        </tbody>
                    </table>
                </div>
                {% elif config_view == "card" %}
                <div class="w-100 row row-eq-height d-flex flex-wrap g-6 gy-5 mx-0">

                    {% for company in companies %}
                    <div class="col-md-6 col-lg-4 pt-5">
                        <div class="card h-100 border">
                            <div class="card-body position-relative">
                                <input style="left:10px; top:10px" id="{{company.id}}" class="form-check-input position-absolute company-selection cursor-pointer check_input" type="checkbox" name="checkbox" value="{{company.id}}" onclick="checking_checkbox(this, event)"/>

                                <div class="text-center mb-3">
                                    <a class="text-dark cursor-pointer"
                                        hx-get="{% host_url 'load_explore_company' company.id host 'app' %}"
                                        hx-target="#manage-profile-company"
                                        hx-trigger="click"
                                        id="profile_company_wizard_button"
                                        >

                                        {% if company.image_url %}
                                        <div class="symbol symbol-50px">
                                            <img alt="Pic" src="{{ company.image_url }}" style="object-fit:cover !important; aspect-ratio: 9 / 9;"/>
                                        </div>
                                        {% elif company.image_file %}
                                        <div class="symbol symbol-50px">
                                            <img alt="Pic" src="{{ company.image_file.url }}" style="object-fit:cover !important; aspect-ratio: 9 / 9;"/>
                                        </div>
                                        {% else %}
                                        <span class="svg-icon svg-icon-muted svg-icon-3hx">
                                            <svg class="h-30px w-30px" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path opacity="0.3" d="M16.5 9C16.5 13.125 13.125 16.5 9 16.5C4.875 16.5 1.5 13.125 1.5 9C1.5 4.875 4.875 1.5 9 1.5C13.125 1.5 16.5 4.875 16.5 9Z" fill="currentColor"/>
                                            <path d="M9 16.5C10.95 16.5 12.75 15.75 14.025 14.55C13.425 12.675 11.4 11.25 9 11.25C6.6 11.25 4.57499 12.675 3.97499 14.55C5.24999 15.75 7.05 16.5 9 16.5Z" fill="currentColor"/>
                                            <rect x="7" y="6" width="4" height="4" rx="2" fill="currentColor"/>
                                            </svg>
                                        </span>
                                        {% endif %}

                                    </a>
                                </div>
                                <div class="d-flex flex-column justify-content-center">
                                    <a  id="profile_company_wizard_button"
                                        class="text-center mb-0 text-dark text-hover-primary fw-bolder cursor-pointer"
                                        hx-get="{% host_url 'load_explore_company' company.id host 'app' %}"
                                        hx-target="#manage-profile-company"
                                        hx-trigger="click"
                                        >
                                        {{company.name}}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

            </form>
        </div>
    </div>
        {% include "data/utility/pagination-complete.html" with page_content=page_content paginator_item_begin=paginator_item_begin paginator_item_end=paginator_item_end paginator=paginator menu_key=menu_key object_type=object_type %}



        <div class="modal fade" tabindex="-1" id="manage_companies_addtolist">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header pb-0 border-0 justify-content-end">
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                        </div>
                    </div>
                    <div class="modal-body pb-0">
                        <div class="mb-13 text-center">
                            <h3 class="modal-title">

                                {% if LANGUAGE_CODE == 'ja'%}
                                企業をリストに追加
                                {% else %}
                                Adding Company to List
                                {% endif %}

                            </h3>
                            <p class="text-muted fw-bold fs-5">
                                {% if LANGUAGE_CODE == 'ja'%}
                                選択した企業をリストに追加する
                                {% else %}
                                Selected companies to add to list
                                {% endif %}

                            </p>
                        </div>
                        <div class="border-bottom">
                            <div class="fv-rowd-flex flex-column mb-8">
                                <label class="{% include 'data/utility/form-label.html' %}">
                                    <span class="required">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        企業一覧
                                        {% else %}
                                        Companies List
                                        {% endif %}
                                    </span>
                                </label>
                                <div class="">
                                    <select class="bg-white form-select form-select-solid border"
                                    data-control="select2"
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    data-placeholder="企業リスト"
                                    {% else %}
                                    data-placeholder="Company List"
                                    {% endif %}
                                    data-allow-clear="true"
                                    multiple="multiple"
                                    name="account_addtolist"
                                    >
                                        <option></option>
                                        {% for contacs in company_lists %}
                                            {% if company_list != contacs %}
                                            <option value="{{contacs.id}}">{{contacs.name}}</option>
                                            {% endif %}
                                        {% endfor %}

                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <input name='flag_all' id='flag_all' class="flag_all" hidden type="checkbox" ></input>
                    {% if view_id %}
                    <input type="hidden" value="{{view_id}}" name="view_id">
                    {% endif %}

                    <div class="modal-footer border-0">
                        <button name="bulk_update_addtolist" type="submit" class="btn btn-dark">{% if LANGUAGE_CODE == 'ja'%}編集を保存{% else %}Save Updates{% endif %}</button>
                    </div>
                </div>
            </div>
        </div>


        <div class="modal fade" tabindex="-1" id="manage_restore_bulk">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header pb-0 border-0 justify-content-end">
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                        </div>
                    </div>
                    <div class="modal-body pb-0">
                        <div class="mb-13 text-center">
                            <h3 class="modal-title">
                                {% if LANGUAGE_CODE == 'ja'%}
                                一括有効化の確認
                                {% else %}
                                Bulk Restore Confirmations
                                {% endif %}
                            </h3>
                        </div>
                        <div class="border-bottom">
                            <div class="fv-rowd-flex flex-column mb-8">
                                <label class="{% include 'data/utility/form-label.html' %}">
                                    <span class="">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        これらの企業を本当に有効化しますか?
                                        {% else %}
                                        Are you sure to activate these company?
                                        {% endif %}
                                    </span>
                                </label>

                            </div>
                        </div>
                    </div>

                    <input name='flag_all' id='flag_all' class="flag_all" hidden type="checkbox" ></input>
                    {% if view_id %}
                    <input type="hidden" value="{{view_id}}" name="view_id">
                    {% endif %}

                    <div class="modal-footer border-0">
                        <button name="bulk_restore_companies" type="submit" class="btn btn-success">
                            {% if LANGUAGE_CODE == 'ja'%}
                            有効化
                            {% else %}
                            Activate
                            {% endif %}
                        </button>
                        <a data-bs-dismiss="modal" class="btn border bg-gray-200">
                            {% if LANGUAGE_CODE == 'ja'%}
                            キャンセル
                            {% else %}
                            Cancel
                            {% endif %}
                        </a>
                    </div>
                </div>
            </div>
        </div>


        <div class="modal fade" tabindex="-1" id="manage_delete_bulk">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header pb-0 border-0 justify-content-end">
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                        </div>
                    </div>
                    <div class="modal-body pb-0">
                        <div class="mb-13 text-center">
                            <h3 class="modal-title">

                                {% if LANGUAGE_CODE == 'ja'%}
                                一括アーカイブの確認
                                {% else %}
                                Bulk Archive Confirmation
                                {% endif %}

                            </h3>
                        </div>
                        <div class="border-bottom">
                            <div class="fv-rowd-flex flex-column mb-8">
                                <label class="{% include 'data/utility/form-label.html' %}">
                                    <span class="">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        選択されたレコードをアーカイブしてもよろしいですか?
                                        {% else %}
                                        Are you sure to archive selected records?
                                        {% endif %}
                                    </span>
                                </label>

                            </div>
                        </div>
                    </div>

                    <input name='flag_all' class="flag_all" hidden></input>

                    <div class="modal-footer border-0">
                        <button name="bulk_delete_companies" type="submit" class="btn btn-danger">

                            {% if LANGUAGE_CODE == 'ja'%}
                            アーカイブ
                            {% else %}
                            Archive
                            {% endif %}

                        </button>
                        <a data-bs-dismiss="modal" class="btn border bg-gray-200">
                            {% if LANGUAGE_CODE == 'ja'%}
                            キャンセル
                            {% else %}
                            Cancel
                            {% endif %}
                        </a>
                    </div>
                </div>
            </div>
        </div>

    {% comment %} =========DRAWER CONTENT===================================================================== {% endcomment %}
    <style>
        .daterangepicker {
            margin-right: 20px
        }

        .tagify[disabled] {
            background: #F6F1E9 !important;
            opacity: 1;
            filter: none;
        }

    </style>

{% endif %}
{% endblock %}

{% block js %}

    <script>
        // Function to load script dynamically
        function loadScript(url, callback) {
            var script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = url;
            script.onload = callback;
            script.onerror = function() {
                console.error('Failed to load script:', url);
            };
            document.head.appendChild(script);
        }

        // Check if jQuery is loaded, if not, load it
        function ensureJQuery(callback) {
            if (typeof window.jQuery === 'undefined') {
                console.log('jQuery not loaded, loading now...');
                loadScript('https://cdn.jsdelivr.net/jquery/latest/jquery.min.js', function() {
                    console.log('jQuery loaded successfully');
                    if (callback) callback();
                });
            } else {
                console.log('jQuery already loaded');
                if (callback) callback();
            }
        }

        // Check if DataTable is loaded, if not, load it
        function ensureDataTableScripts(callback) {
            if (typeof $.fn.DataTable === 'undefined') {
                console.log('DataTable not loaded, loading scripts...');
                loadScript('https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js', function() {
                    console.log('DataTable core loaded');
                    loadScript('https://cdn.datatables.net/fixedcolumns/4.3.0/js/dataTables.fixedColumns.min.js', function() {
                        console.log('DataTable FixedColumns loaded');
                        if (callback) callback();
                    });
                });
            } else {
                console.log('DataTable already loaded');
                if (callback) callback();
            }
        }

        // Make sure jQuery and DataTables are properly initialized
        function ensureDataTable(callback) {
            // First ensure jQuery is loaded
            ensureJQuery(function() {
                // Then ensure DataTable scripts are loaded
                ensureDataTableScripts(function() {
                    if (callback && typeof callback === 'function') {
                        callback();
                    }
                });
            });
        }

        {% if companies %}
        // Render DataTable after htmx conmpleted
        var requestNum = {{companies|length}}
        document.addEventListener('htmx:afterRequest', function(evt) {
            if (requestNum <= 0) return;

            if (evt.target.tagName.toLowerCase() === 'tr') {
                requestNum -= 1;
                console.log(requestNum)
                if (requestNum <= 0) {
                    ensureDataTable(function() {
                        // Debug: Count headers and columns before DataTable initialization
                        var $table = $(".companies-table");
                        var headerCount = $table.find("thead tr:first th").length;
                        var firstRowCellCount = $table.find("tbody tr:first td").length;
                        console.log("DEBUG: Header count:", headerCount);
                        console.log("DEBUG: First row cell count:", firstRowCellCount);
                        console.log("DEBUG: Companies columns:", '{{companies_columns|join:", "}}');
                        
                        var table = $(".table-content").DataTable({
                            scrollX: true,
                            scrollY: "75vh",
                            scrollCollapse: true,
                            fixedColumns:   {
                                left: 2
                            },
                            ordering: false,
                            searching: false,  // Hide the search bar
                            paging: false,      // Hide pagination
                            info: false,        // Hide the information text
                            language: {
                                emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
                            }
                        });

                        const dropdowns = $('.dropdown-toggle', table.cells(null, 1).nodes());
                        const dropdown = dropdowns.each((index, dropdownToggleEl) => {
                            var instance = new bootstrap.Dropdown(dropdownToggleEl, {
                                popperConfig(defaultBsPopperConfig) {
                                    return { ...defaultBsPopperConfig, strategy: "fixed" };
                                },
                            });

                            dropdownToggleEl.addEventListener("show.bs.dropdown", function (event) {
                                $(event.target).closest("td").addClass("z-index-3");
                            });

                            dropdownToggleEl.addEventListener("hide.bs.dropdown", function (event) {
                                $(event.target).closest("td").removeClass("z-index-3");
                            });
                        });

                        {% include "data/common/open-drawer.js" %}
                    });
                }
            }
        });
        {% else %}
            $(document).ready(function() {
                ensureDataTable(function() {
                    // Debug: Count headers and columns before DataTable initialization (empty table)
                    var $table = $(".companies-table");
                    var headerCount = $table.find("thead tr:first th").length;
                    var firstRowCellCount = $table.find("tbody tr:first td").length;
                    console.log("DEBUG (empty): Header count:", headerCount);
                    console.log("DEBUG (empty): First row cell count:", firstRowCellCount);
                    console.log("DEBUG (empty): Companies columns:", '{{companies_columns|join:", "}}');
                    
                    var table = $(".table-content").DataTable({
                        scrollX: true,
                        scrollY: "75vh",
                        scrollCollapse: true,
                        fixedColumns:   {
                            left: 2
                        },
                        order: [[1, 'desc']],
                        ordering: false,
                        searching: false,  // Hide the search bar
                        paging: false,      // Hide pagination
                        info: false,        // Hide the information text
                        language: {
                            emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
                        }
                    });
                    var dropdowns = $('.dropdown-toggle', table.cells(null, 1).nodes());
                    console.log("dropdowns: ", dropdowns)
                    var dropdown = dropdowns.each((index, dropdownToggleEl) => {
                        var instance = new bootstrap.Dropdown(dropdownToggleEl, {
                            popperConfig(defaultBsPopperConfig) {
                                return { ...defaultBsPopperConfig, strategy: "fixed" };
                            },
                        });

                        dropdownToggleEl.addEventListener("show.bs.dropdown", function (event) {
                            $(event.target).closest("td").addClass("z-index-3");
                        });

                        dropdownToggleEl.addEventListener("hide.bs.dropdown", function (event) {
                            $(event.target).closest("td").removeClass("z-index-3");
                        });
                    });
                });
            });
        {% endif %}

        function toggleText() {
            var x = document.getElementById("select-additional-options-toggle");
            var toggle_data = x.getAttribute('toggle-data')
            if (toggle_data !== "true") {
                {% if LANGUAGE_CODE == 'ja'%}
                x.innerHTML = "選択を解除";
                {% else %}
                x.innerHTML = "Clear All";
                {% endif %}

              $(".flag_all").each(function(index, element) {
                    element.value = true
                });

                x.setAttribute('toggle-data',"true")

            } else {

                x.setAttribute('toggle-data',"false")

                addcontactelem = document.getElementById("update-company");
                addcontactelem.classList.add("disabled");
                downloadelem = document.getElementById("csv_download-company");
                downloadelem.classList.add("disabled");
                $('input[type=checkbox]').prop('checked', false);

                //Hide
                var element_select_options = document.getElementById("d-select-additional-options");
                element_select_options.classList.add("d-none")

                x.innerHTML = "{% if LANGUAGE_CODE == 'ja'%}すべて選択 ({{paginator.count}}) このセクションの連絡先{%else%}Select All ({{paginator.count}}) contacts in this sections{%endif%}";

                $(".flag_all").each(function(index, element) {
                    element.value = false
                });

            }
          }

        var count_checked = 0;
        function select_all() {
            $('#selectAll').prop('checked', !$('#selectAll').prop('checked'));
            if ($('#selectAll').prop('checked') == true) {
                addcontactelem = document.getElementById("update-company");
                addcontactelem.classList.add("disabled");
                downloadelem = document.getElementById("csv_download-company");
                downloadelem.classList.add("disabled");
                $('input[type=checkbox]').prop('checked', false);

                var element_select_options = document.getElementById("d-select-additional-options");
                element_select_options.classList.add("d-none")

            } else {
                addcontactelem = document.getElementById("update-company");
                addcontactelem.classList.remove("disabled");
                downloadelem = document.getElementById("csv_download-company");
                downloadelem.classList.remove("disabled");
                $('input[type=checkbox]').prop('checked', true);

                var element_select_options = document.getElementById("d-select-additional-options");
                element_select_options.classList.remove("d-none")

            }
        }
    </script>

    {% include 'data/javascript/toggleSearch.html' %}

<script>
    // Keyboard shortcut for opening the 'Create New' drawer (n key)
    document.addEventListener('keydown', function(event) {
        // Ignore if input, textarea, or contenteditable is focused
        const active = document.activeElement;
        if (active && (active.tagName === 'INPUT' || active.tagName === 'TEXTAREA' || active.isContentEditable)) {
            return;
        }

        // Check if the key pressed is 'n'
        if (event.key.toLowerCase() === 'n') {
            event.preventDefault(); // Prevent default 'n' behavior
            
            // Find the 'Create New' button (adjust selector if needed)
            // Using the class combination that seems unique to the main 'New' button
            const newButton = document.querySelector('.customer-create-wizard.view_form_trigger'); 
            
            if (newButton) {
                newButton.click(); // Simulate click to open the drawer
            }
        }
    });
</script>

{% comment %} Custom search functionality with proper error handling {% endcomment %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Make functions globally available for search
            window.toggleSearch = function() {
                const searchWrapper = document.querySelector('.search-wrapper');
                if (!searchWrapper) {
                    console.log("Search wrapper not found");
                    return;
                }

                searchWrapper.classList.toggle('expanded');
                searchWrapper.classList.toggle('hover-tooltip');

                const toolTipText = searchWrapper.querySelector('.search-wrapper-tooltip');
                if (toolTipText) {
                    toolTipText.classList.toggle('d-none');
                }

                const searchInput = document.getElementById('base-search-input');
                if (searchInput) {
                    searchInput.focus();
                }
            };

            window.toolTipCheck = function() {
                const searchWrapper = document.querySelector('.search-wrapper');
                if (!searchWrapper) {
                    console.log("Search wrapper not found");
                    return;
                }

                searchWrapper.classList.toggle('hover-tooltip');

                const toolTipText = searchWrapper.querySelector('.search-wrapper-tooltip');
                if (toolTipText) {
                    toolTipText.classList.toggle('d-none');
                }
            };

            window.openSearch = function() {
                const search_bar = document.getElementById('search-bar');
                if (!search_bar) {
                    console.log("Search bar not found");
                    return;
                }

                search_bar.classList.toggle('max-md:tw-flex');
                search_bar.classList.toggle('max-md:tw-hidden');

                const searchInput = document.getElementById('base-search-input-mobile');
                if (searchInput) {
                    searchInput.focus();
                }
            };

            {% if search_q %}
            // Use a timeout to ensure DOM is ready
            setTimeout(function() {
                if (typeof window.toolTipCheck === 'function') {
                    window.toolTipCheck();
                }
            }, 300);
            {% endif %}
        });
    </script>

    <script>
        function check_permission_action(event, permission_type, ...args){

            let source = args.length > 0 ? args[0] : null;

            const checkInputs = document.querySelectorAll('.check_input:checked');

            let members = "{{group_members}}"
            members = members.split(',')
            const user_id = '{{request.user.id}}'
            const permission = '{{permission}}';
            const permission_list = permission.split('|');
            let scope = ''
            permission_list.forEach(p => {
                if (p.includes(permission_type)) {
                    p_split = p.split('_');
                    scope = p_split[0]
                }
            })

            let msg = '';
            let denied = false;
            for (let i = 0; i < checkInputs.length; i++) {
                var owner_id = checkInputs[i].dataset.owner;
                if (owner_id){
                    if (scope == 'user'){
                        if (owner_id.toString() !== user_id.toString()) {
                            {% if LANGUAGE_CODE == 'ja' %}
                            msg = "操作が拒否されました。自分のアイテムのみ編集または削除することができます。";
                            {% else %}
                            msg = "Action denied. You are only allowed to edit or delete your own items.";
                            {% endif %}                    
                            checkInputs[i].click()
                            denied = true;
                        }
                    } else if (scope == 'team'){
                        if (!members.includes(owner_id.toString())) {
                            {% if LANGUAGE_CODE == 'ja' %}
                            msg = "操作が拒否されました。自分または自分のチームに割り当てられたアイテムのみ編集または削除できます。";
                            {% else %}
                            msg = "Action denied. You can only edit or delete items assigned to you or your team.";
                            {% endif %}
                            checkInputs[i].click()
                            denied = true;
                        }
                    } 
                }
            }
            if (denied) {
                event.preventDefault();
                event.stopImmediatePropagation();
                document.getElementById('permissionActionWarning').innerHTML = msg;
                setTimeout(() => {
                    document.getElementById('permissionActionWarning').innerHTML = '';
                }, 4000);
                msg = ''
            } else if (source){
                const modalEl = document.getElementById(source);
                const modal = bootstrap.Modal.getOrCreateInstance(modalEl);
                modal.show();
            }

        }
    </script>

    {% comment %} DataTable initialization for companies table {% endcomment %}
    {% include "data/utility/table-datatable-init.html" with table_class="companies-table" object_list=companies %}

{% endblock %}
