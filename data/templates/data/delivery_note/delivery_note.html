{% extends 'base.html' %}
{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% block content %}

<style>
    .delivery_slip-item:hover .delivery-note-selection{
        border-width: 1px !important;
    }
    .dataTables_scrollHead {
        position: sticky !important;
        z-index: 3;
        background-color: white;
    }
</style>

<div class="d-flex align-items-end mb-2 align-items-center" style="background-color: #FAFAFA !important;">
    {% include 'data/common/module-header-tabs.html' %}
    {% include 'data/common/view-menu-new-design.html' %}
</div>
{% if permission == 'hide' %}
    {% include 'data/static/no-access-page.html' %}
{% else %}
<div class="mt-0 mb-10 tw-pl-2">
    {% include 'data/common/permission-action-warning-message.html' %}
    {% include 'data/common/view-menu.html' %}
    <div id="data-table">
        {% include 'data/delivery_note/delivery-note-table.html' %}
    </div>
    {% if contact_id %}
        <span class="d-none">
            {% with contact_id|get_contact_obj as contact %}
                {% include 'data/partials/partial-load-contact-company-drawer.html' with contact=contact open='open' %}
            {% endwith %}
        </span>
    {% elif company_id %}
        <span class="d-none">
            {% with company_id|get_company_obj as company %}
                {% include 'data/partials/partial-load-contact-company-drawer.html' with company=company open='open' %}
            {% endwith %}
        </span>
    {% endif %}
</div>
{% endif %}



{% endblock %}

{% block js%}
 
<script>
    function isNumberKey(evt) {
        var charCode = (evt.which) ? evt.which : evt.keyCode;
        var inputValue = evt.target.value;
      
        var hasPeriod = inputValue.indexOf('.') !== -1;
        var hasComma = inputValue.indexOf(',') !== -1;
      
        // Allow numeric keys (0-9), period (.) if not already present, and comma (,) if not already present
        if (
          (charCode >= 48 && charCode <= 57) ||  // 0-9
          (charCode === 46 && !hasComma && !hasPeriod) ||  // . (allow only if not already present and no comma present)
          (charCode === 44 && !hasPeriod && !hasComma)     // , (allow only if not already present and no period present)
        ) {
          return true;
        } else {
          return false;
        }
      }
      document.addEventListener("htmx:beforeRequest", function(event) {
        if (event.target.id === "billings_wizard_button") {
            document.getElementById("manage-full-drawer-content").innerHTML = ''
            // You can modify event properties or run any custom logic here
        }
    });
    
    function selectAllInView() {
        const totalItems = {{paginator.count}}
        document.getElementById('total-item-selected').innerText = totalItems;
        var items = document.getElementsByClassName('check_input')
        for (var i = 0; i < items.length; i++) {
            items[i].checked = true;
        }
        document.getElementById('flag_all').checked = true
    }
</script>

<script>
    // Keyboard shortcut for opening the 'Create New' drawer (n key)
    document.addEventListener('keydown', function(event) {
    // Ignore if input, textarea, or contenteditable is focused
    const active = document.activeElement;
    if (active && (active.tagName === 'INPUT' || active.tagName === 'TEXTAREA' || active.isContentEditable)) {
        return;
    }

    // Check if the key pressed is 'n'
    if (event.key.toLowerCase() === 'n') {
        event.preventDefault(); // Prevent default 'n' behavior
        
        // Find the 'Create New' button
        const newButton = document.querySelector('.view_form_trigger'); 
        
        if (newButton) {
            newButton.click(); // Simulate click to open the drawer
        }
    }
    });
</script>

<script>
    function check_permission_action(event, permission_type, ...args){

        let source = args.length > 0 ? args[0] : null;

        const checkInputs = document.querySelectorAll('.check_input:checked');

        let members = "{{group_members}}"
        members = members.split(',')
        const user_id = '{{request.user.id}}'
        const permission = '{{permission}}';
        const permission_list = permission.split('|');
        let scope = ''
        permission_list.forEach(p => {
            if (p.includes(permission_type)) {
                p_split = p.split('_');
                scope = p_split[0]
            }
        })

        let msg = '';
        let denied = false;
        for (let i = 0; i < checkInputs.length; i++) {
            var owner_id = checkInputs[i].dataset.owner;
            if (owner_id){
                if (scope == 'user'){
                    if (owner_id.toString() !== user_id.toString()) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分のアイテムのみ編集または削除することができます。";
                        {% else %}
                        msg = "Action denied. You are only allowed to edit or delete your own items.";
                        {% endif %}                    
                        checkInputs[i].click()
                        denied = true;
                    }
                } else if (scope == 'team'){
                    if (!members.includes(owner_id.toString())) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分または自分のチームに割り当てられたアイテムのみ編集または削除できます。";
                        {% else %}
                        msg = "Action denied. You can only edit or delete items assigned to you or your team.";
                        {% endif %}
                        checkInputs[i].click()
                        denied = true;
                    }
                } 
            }
        }
        if (denied) {
            event.preventDefault();
            event.stopImmediatePropagation();
            document.getElementById('permissionActionWarning').innerHTML = msg;
            setTimeout(() => {
                document.getElementById('permissionActionWarning').innerHTML = '';
            }, 4000);
            msg = ''
        } else if (source){
            const modalEl = document.getElementById(source);
            const modal = bootstrap.Modal.getOrCreateInstance(modalEl);
            modal.show();
        }

    }
</script>

{% endblock %}