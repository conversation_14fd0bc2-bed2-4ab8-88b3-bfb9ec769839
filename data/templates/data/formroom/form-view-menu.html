{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}

<style>
    table {
        width: auto;
    }
    
    td, th {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
</style>

{% include 'data/static/tootip-search-wrapper.html' %}


<div class="tw-flex justify-content-between align-items-end border-bottom border-bottom-1 {% include "data/utility/tab-pane.html" %}" id="contact-view-container-1" role="tabpanel" style="z-index:4 !important; top: 4% !important">
    {% comment %} Desktop {% endcomment %}
    <div class="max-md:tw-hidden w-50">
        <div class="{% include "data/utility/table-nav-2.html" %}">
            <div class="d-flex align-items-center justify-content-between w-100">
                <div class="d-flex align-items-end me-3 w-100">    
                    {% for view in views %}
                        {% if view.title == "main" %}
                            <div class="{% include "data/utility/view-menu-default-common.html" %} me-2 mh-100px">
                                <a  href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?view_id={{view.id}}"
                                    class="{% include "data/utility/view-menu-default-2.html" %}" >
                                    {% if LANGUAGE_CODE == 'ja'%}ビュー{% else %}View{% endif %}
                                </a>


                                {% comment %} edit {% endcomment %}
                                {% if current_view.id == view.id %}
                                <div class="w-20px nav-item justify-content-center d-flex fs-6 text-gray-900">
                                    <button type="button" class="{% include "data/utility/view-plus-link.html" %} formroom_wizard_button"
                                        hx-vals='{"module": "{{menu_key}}", "object_type": "{{object_type}}", "view_id":"{{view.id}}"}'
                                        hx-get="{% url 'commerce_view_setting' %}" 
                                        hx-indicator=".loading-drawer-spinner,.view-form"
                                        hx-target="#formroom_form"
                                        >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                            <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                        </svg>
                                        
                                    </button>
                                </div>
                                {% endif %}

                            </div>
                        {% endif %}
                    {% endfor %}

                    {% include 'data/projects/partial-view-menu.html' with target=object_type %}

                    <div class="nav-item  fs-6 text-gray-900">
                        <button type="button" class="nav-link mx-1 formroom_wizard_button" 
                            hx-vals='{"object_type": "{{object_type}}", "view_button":"create"}'
                            hx-get="{% url 'commerce_view_setting' %}" 
                            hx-indicator=".loading-drawer-spinner,.view-form"
                            hx-target="#formroom_form"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                            </svg>
                        </button>
                    </div>
                
                    
                </div>
            </div>
        </div>
    </div>

    {% comment %} Mobile View {% endcomment %}
    <div class="tw-hidden max-md:tw-flex tw-w-1/2">
        <div class="d-flex align-items-center">
            <div class="btn-group mb-2">
                <button type="button" class="tw-max-w-[90px] align-items-center d-flex btn bg-white border btn-md tw-px-[10px] manage-view-settings-button"
                    style="height: 26px; border-radius: 0.475rem 0 0 0.475rem;"
                    hx-vals='{"object_type": "{{object_type}}", "view_id":"{{view.id}}"}'
                    hx-get="{% url 'commerce_view_setting' %}" 
                    hx-indicator=".loading-drawer-spinner,.view-form"
                    hx-target="#manage-contacts-view-settings-drawer"
                >
                    {% if view.title == "main" %}
                        <span class="fs-7 ps-1 fw-bolder tw-text-ellipsis tw-overflow-hidden tw-text-nowrap">
                        {% if LANGUAGE_CODE == 'ja'%}ビュー{% else %}View{% endif %}
                        </span>
                    {% else %}
                        <span class="fs-7 ps-1 fw-bolder tw-text-ellipsis tw-overflow-hidden tw-text-nowrap">   
                            {% if view.title %}
                                {{ view.title }}
                            {% else %}
                                {% if LANGUAGE_CODE == 'ja'%}ビュー{% else %}View{% endif %}
                            {% endif %}
                        </span>
                    {% endif %}
                </button>
                <button type="button" 
                    class="tw-w-[30px] align-items-center d-flex btn btn-white border btn-md  dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split" 
                    data-bs-toggle="dropdown" 
                    aria-expanded="false"
                    style="height: 26px; border-radius: 0 0.475rem 0.475rem 0;"
                >
                    <span class="svg-icon svg-icon-4">
                        <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                            <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                        </svg>
                    </span>
                </button>
                <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                    <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">{% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default View{% endif %}</a></li>
                    <div class="dropdown-divider"></div>
                    {% for view in views %}
                        {% if view.title != "main" %}
                            <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?view_id={{view.id}}">{{view.title}}</a></li>
                        {% endif %}
                    {% endfor %}
                </ul>
            </div>
            <div class="fs-6 text-gray-900 mb-2">
                <button type="button" class="tw-border-0 tw-bg-transparent tw-text-gray-900 manage-view-settings-button" 
                    hx-vals='{"object_type": "{{object_type}}", "view_button":"create"}'
                    hx-get="{% url 'commerce_view_setting' %}" 
                    hx-target="#manage-contacts-view-settings-drawer"
                    hx-indicator=".loading-drawer-spinner,.view-form"
                    hx-trigger="click"
                    hx-swap="innerHTML">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>
    
    <div class="d-flex align-items-center max-md:tw-mb-0 w-50 justify-content-end mb-3">
        <div class="me-2 search-wrapper expanded hover-tooltip">
            <span class="search-wrapper-tooltip hover-tooltip-text">
                {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}

            </span>
            <div class="d-flex align-items-center">
                <form id="filter-form-search" method="GET" class="w-100">
                    <div class="d-flex mb-0 position-relative align-items-center" style="height: 26px;">
                        <span class="svg-icon svg-icon-3 search-icon-view" onclick="toggleSearch()">
                            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M13 13L10.1 10.1M11.6667 6.33333C11.6667 9.27885 9.27885 11.6667 6.33333 11.6667C3.38781 11.6667 1 9.27885 1 6.33333C1 3.38781 3.38781 1 6.33333 1C9.27885 1 11.6667 3.38781 11.6667 6.33333Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </span>

                        <input
                        id="base-search-input" type="text" name="q" class="form-control bg-white ps-12 h-25px me-2 tw-rounded-lg"
                        value={% if search_q %} "{{ search_q }}" {% else %}""{% endif %}
                        placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                        onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                        >
                        <input type="hidden" value="{{view_id}}" name="view_id">
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


<div id="contact-bulk-action-container" class="tw-hidden justify-content-between align-items-end flex-row border-bottom-1 border-bottom my-5 {% include "data/utility/tab-pane.html" %}">
    <span class="me-10">
        <button class="py-1 rounded-1 btn btn-sm btn-light fw-bold mt-2 mb-1" onclick="select_all_objects()">
            <span>
                {% if LANGUAGE_CODE == 'ja'%}
                すべて選択
                {% else %}
                Select All
                {% endif %}
            </span>
        </button>
        <button class="py-1 rounded-1 btn btn-sm btn-light fw-bold mt-2 mb-1" onclick="deselect_all_objects()">
            <span>
                {% if LANGUAGE_CODE == 'ja'%}
                選択を解除
                {% else %}
                Deselect All
                {% endif %}
            </span>
        </button>
        
        <script>
            function select_all_objects() {            
                $('.check_input').prop('checked', true);
            }
            function deselect_all_objects() {        
                $('.check_input').prop('checked', false);
                document.getElementById('contact-bulk-action-container').classList.add('tw-hidden')
                document.getElementById('contact-view-container').classList.remove('tw-hidden')
                document.getElementById('contact-view-container-1').classList.remove('tw-hidden')
            }
        </script>
    </span>
    
    {% if permission|check_permission:'edit' %}
    <button class="btn btn-sm btn-light-warning py-1 rounded-1 fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'edit')" name="bulk_type" value="duplicate" form="bulk-update">
        {% if LANGUAGE_CODE == 'ja'%}
        複製
        {% else %}
        Duplicate 
        {% endif %}
    </button>
    {% endif %}
    

    {% if permission|check_permission:'edit' %}
    <button class="py-1 rounded-1 btn btn-sm btn-light-info fw-bold mt-2 mb-1 formroom_wizard_button" type="button"
        hx-vals='js:{"section":"object-action","selected_ids": getSelected()}'
        hx-get="{% url 'form_view_drawer' %}" 
        hx-target="#formroom_form" 
        hx-indicator=".loading-drawer-spinner,.formroom-form"
        style="border-radius: 0.475rem 0 0 0.475rem;"
        onclick="check_permission_action(event, 'edit')"
        >
        {% if LANGUAGE_CODE == 'ja'%}
        アクション
        {% else %}
        Action 
        {% endif %}
    </button>

    <script>
        function getSelected() {
            var selected = [];
            var classNameElements = document.getElementsByClassName("check_input");
            if (classNameElements){
                classNameElements.forEach(function(classNameElement) {
                    if (classNameElement.checked) {
                        selected.push(classNameElement.value);
                    }
                });  
            }
            console.log(selected)
            return selected;
        }
    </script>
    {% endif %}

    <script>
        document.body.addEventListener("hideTaskBulkActionBtn", function(evt){
            document.getElementById('task-bulk-action-container').classList.add('d-none')
            document.getElementById('task-view-contianer').classList.remove('d-none')
        })
    </script>


 
</div>
{% include 'data/javascript/toggleSearch.html' %} 

<script>
    
    var index_1 = '';
    var index_2 = '';
    const checking_checkbox = (elem,event) => {
        if (event.shiftKey) {
            check_inputs = document.getElementsByClassName('check_input')
            
            index_2 = elem;

            //Do Check
            var pos_1 = '';
            var pos_2 = '';
            for (var i = 0; i < check_inputs.length; i++) {
                if (index_1 == check_inputs[i]){
                    pos_1 = i;
                }
                else if (index_2 == check_inputs[i]){
                    pos_2 = i;
                }
            }
            
            if (pos_1 > pos_2){
                for (var i = pos_2; i < pos_1; i++) {
                    check_inputs[i].checked = true;
                }
            }
            else {
                for (var i = pos_1; i < pos_2; i++) {
                    console.log(i);
                    check_inputs[i].checked = true;
                    
                }
            }
        }
        else{
            if (elem.checked) {
                index_1 = elem;
                document.getElementById('contact-bulk-action-container').classList.remove('tw-hidden')
                document.getElementById('contact-view-container').classList.add('tw-hidden') 
                document.getElementById('contact-view-container-1').classList.add('tw-hidden') 
            } else {
                taskSelections = document.querySelectorAll('input[type=checkbox]')
                for (let i = 0; i < taskSelections.length; i++) {
                    const element = taskSelections[i];
                    console.log(element.checked)
                    if (element.checked) {
                        return
                    }
                }
                document.getElementById('contact-bulk-action-container').classList.add('tw-hidden')
                document.getElementById('contact-view-container').classList.remove('tw-hidden')
                document.getElementById('contact-view-container-1').classList.remove('tw-hidden')
            }
        }
    }
</script>
