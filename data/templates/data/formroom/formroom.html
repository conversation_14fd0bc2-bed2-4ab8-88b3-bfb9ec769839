{% extends 'base.html' %}
{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% block content %}

<div id="form-container">

<div class="d-flex align-items-center flex-lg-row" style="background-color: #FAFAFA !important;">
    {% include 'data/common/module-header-tabs.html' %}
    <div class="w-50 d-flex justify-content-end tw-mr-5" id="contact-view-container">
        {% if permission|check_permission:'edit' %}
            <div class="ms-2" data-bs-toggle="tooltip" data-bs-placement="right" data-bs-delay-show="1000" data-kt-initialized="1">
                <div class="btn-group tw-h-[32px]">
                    <form action="{% host_url 'formroom' host 'app' %}" method="post">
                        {% csrf_token %}
                        <button type="submit" 
                            id="create-form-wizard-button" 
                            class="py-1 ms-2 w-100px align-items-center d-flex btn btn-primary btn-md rounded-1 shopturbo-create-wizard-button" type="button"
                            style="height: 32px;"
                        >
                            <span class="svg-icon svg-icon-4">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"/>
                                    <rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"/>
                                    <rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"/>
                                </svg>
                            </span>
                    
                            <span class="fs-7 ps-1 fw-bolder w-85px">
                                {% if LANGUAGE_CODE == 'ja'%}
                                新規
                                {% else %}
                                New
                                {% endif %}
                            </span>
                            <input type="hidden" name="add_form" value="true">
                        </button>
                    </form>
                </div>
            </div>
        {% endif %}
    </div>
</div>

{% if permission == 'hide' %}
    {% include 'data/static/no-access-page.html' %}
{% else %}
<div class="{% include "data/utility/table-container.html" %}">
    {% include 'data/common/permission-action-warning-message.html' %}
    {% include 'data/formroom/form-view-menu.html' %}
</div>
<div class="px-lg-10 px-5 w-100">
        
    <form method="POST" id="order-form">
        {% csrf_token %}
        <div class=" pt-0 py-10">
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <thead class="{% include "data/utility/table-header.html" %}">
                    <tr class="align-middle">
                        <th class="min-w-40px"></th>
                        {% for column in view_filter.column|safe|string_list_to_list %}
                            <th {% if column ==  id_field or column ==  'id' %} class="w-50px border-right-1" {% else %} class="fw-bolder col-2" {% endif %}>
                                {% with args=column|add:'|'|add:object_type %} 
                                    {% with column_display=args|get_column_display:request %}
                                        {{column_display.name}}
                                    {% endwith %}
                                {% endwith %}
                            </th>
                            {% if column ==  id_field or column ==  'id' %} 
                            <th class="" style="width: 20px;">
                            </th>
                            {% endif %}
                        {% endfor %}
                    </tr>

                </thead>
        
                <tbody class="fs-6">
                        {% for form in forms %}
                            <tr>
                                {% for column in forms_columns %}
                                <!-- checkbox -->
                                {% if "checkbox" == column %}
                                    <td class="w-40px">
                                        <input form="bulk-update" id="{{form.id}}" class="form-check-input cursor-pointer check_input" type="checkbox" name="forms" data-owner="{{form.owner.user.id}}" value="{{form.id}}" onclick="checking_checkbox(this, event)"/>
                                    </td>
                                    
                                {% elif column == 'title' %}
                                    <td class="fw-bolder">
                                        {% if form.title %}
                                        <a href="{% host_url 'form_manage' form.id host 'app' %}" class="text-black">
                                            {% if form.title %}
                                                {{form.title}}
                                            {% else %}
                                                {{form.id}}
                                            {% endif %}
                                        </a>
                                            
                                        {% endif %}
                                    </td>
                                
                                {% elif column == 'owner' %}
                                    <td class="fw-bold">
                                        {% if form.owner %}
                                            {% with form.owner.user as user %}
                                            {% if user.verification.profile_photo %}
                                            <img class="w-20px rounded-circle me-2" src="{{user.verification.profile_photo.url}}" style="object-fit: cover !important;aspect-ratio: 16 / 16;" />
                                            {% else %}
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M6.28548 15.0861C7.34369 13.1814 9.35142 12 11.5304 12H12.4696C14.6486 12 16.6563 13.1814 17.7145 15.0861L19.3493 18.0287C20.0899 19.3618 19.1259 21 17.601 21H6.39903C4.87406 21 3.91012 19.3618 4.65071 18.0287L6.28548 15.0861Z" fill="currentColor"/>
                                                    <rect opacity="0.3" x="8" y="3" width="8" height="8" rx="4" fill="currentColor"/>
                                                </svg>    
                                            {% endif %}
                                            <span >
                                                {{user.first_name}}
                                            </span>
                                            {% endwith %}
                                        {% endif %}
                                    </td>
                                {% elif column == 'user' %}
                                    <td class="">
                                        {% if form.user %}
                                            {{form.user.first_name}}
                                        {% endif %}
                                    </td>

                                {% elif column == 'visibility' %}
                                    <td class="">
                                        {{ request|display_visibility_form:form.visibility }}
                                    </td>

                                {% elif column == 'entries' %}
                                    <td class="">
                                        <button 
                                            class="btn btn-primary text-white" 
                                            hx-trigger="click"
                                            hx-target="#form-container"
                                            hx-swap="innerHTML"
                                            hx-post="{% host_url 'form_entry' form.id host 'app' %}"
                                            hx-vals='{"list_form_entries": "true"}'
                                        > 
                                            <span >
                                                {{form.responses.all|length}}
                                            </span>
                                        </button>
                                    </td>

                                {% elif column == 'created_at' %}
                                    <td class="">
                                        {% if form.created_at %}
                                        {{form.created_at}}
                                        {% else %}
                                        -
                                        {% endif %}
                                    </td>      

                                {% elif column == 'updated_at' %}
                                <td class="">
                                    {% if form.updated_at %}
                                    {{form.updated_at}}
                                    {% else %}
                                    -
                                    {% endif %}
                                </td>                                
                                {% endif %}
                                <!-- Add more data cells as needed -->
                                {% endfor %}
                                <td class="text-end">
                                    <div class="d-flex align-items-center justify-content-end">
                                        <div class="btn-group" role="group">
                                            {% if permission|check_permission:'edit' %}
                                            <button id='view-syncs' type="button" 
                                                class="btn btn-light-primary btn-sm create-view-settings-button"
                                                hx-post="{% host_url 'form_manage' id=form.id host 'app' %}" 
                                                hx-vals='{"form_logic_drawer": true, "module": "{{menu_key}}"}'
                                                hx-trigger="click"
                                                hx-target="#manage-form-share-drawer"
                                                onclick="check_permission_action(event, 'edit', 'action_from_row', '{{form.owner.user.id}}')">
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                ロジック
                                                {% else %}
                                                Logic
                                                {% endif %}
                                            </button>
                                            {% endif %}

                                            <button id='view-syncs' type="button" 
                                                class="btn btn-light-primary btn-sm create-view-settings-button"
                                                hx-post="{% host_url 'form_manage' id=form.id host 'app' %}" 
                                                hx-vals='{"form_share_drawer": true}'
                                                hx-trigger="click"
                                                hx-target="#manage-form-share-drawer">
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                共有
                                                {% else %}
                                                Share
                                                {% endif %}
                                            </button>
                                            {% if permission|check_permission:'edit' %}
                                            <a href="{% host_url 'form_manage' id=form.id host 'app' %}" 
                                            class="btn btn-light-primary btn-sm" onclick="check_permission_action(event, 'edit',  'action_from_row', '{{form.owner.user.id}}')">
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                編集
                                                {% else %}
                                                Edit
                                                {% endif %}
                                            </a>
                                            {% endif %}
                                            {% if permission|check_permission:'archive' %}
                                            <div data-bs-toggle="modal" 
                                                class="btn btn-light-danger btn-sm" onclick="check_permission_action(event, 'archive',  'action_from_row', '{{form.owner.user.id}}', 'form_delete_confirmation' )">
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                削除
                                                {% else %}
                                                Delete 
                                                {% endif %}
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <!-- delete modal confirmation -->
                                    <div class="modal fade" tabindex="-1" id="form_delete_confirmation">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header pb-0 border-0">
                                                    <div class="mb-5 text-center">
                                                        <h3 class="modal-title">
                                                            {% if LANGUAGE_CODE == 'ja'%}
                                                            フォームの削除の確認
                                                            {% else %}
                                                            Form Delete Confirmations
                                                            {% endif %}
                                                        </h3>
                                                    </div>
                                                </div>
                                                <div class="modal-body pb-0">
                                                    <div class="border-bottom">
                                                        <div class="fv-rowd-flex flex-column mb-8">
                                                            <label class="{% include 'data/utility/form-label.html' %}">
                                                                <span style="text-align: start;">
                                                                    {% if LANGUAGE_CODE == 'ja'%}
                                                                    一度削除してしまうと、データは完全に消去されます。
                                                                    <br/>
                                                                    このフォームを削除してもよろしいですか?
                                                                    {% else %}
                                                                    This action cannot be undone. 
                                                                    <br/>
                                                                    Are you sure to delete this Form?
                                                                    {% endif %}
                                                                </span>
                                                            </label>
                                                    
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <input name='flag_all' class="flag_all" hidden></input>
                                
                                                <div class="modal-footer border-0">
                                                    <button data-bs-dismiss="modal" class="btn btn-light-danger"
                                                        hx-post="{% host_url 'form_manage' id=form.id host 'app' %}"
                                                        hx-trigger="click"
                                                        hx-vals='{"delete_form":true}'
                                                        hx-swap="outerHTML"
                                                        hx-target="closest tr"
                                                        >
                                                        {% if LANGUAGE_CODE == 'ja'%}
                                                        削除
                                                        {% else %}
                                                        Delete 
                                                        {% endif %}
                                                    </button>
                                                    <a data-bs-dismiss="modal" class="btn btn-dark">
                                                        {% if LANGUAGE_CODE == 'ja'%}
                                                        キャンセル
                                                        {% else %}
                                                        Cancel
                                                        {% endif %}
                                                    </a>
                                                </div>
                                                
                                            </div>
                                        </div>
                                    </div> 
                                </td>
                            </tr>
                        {% endfor %}

                </tbody>
            </table>
        </div>
    </form>
</div>


<script>
    function check_permission_action(event, permission_type, ...args){
        let source = args.length > 0 ? args[0] : null;
        let checkInputs = document.querySelectorAll('.check_input:checked');

        if (source === 'action_from_row'){
            checkInputs = [{'dataset': {'owner': args[1]}}]
        }

        let members = "{{group_members}}"
        members = members.split(',')
        const user_id = '{{request.user.id}}'
        const permission = '{{permission}}';
        const permission_list = permission.split('|');
        let scope = ''
        permission_list.forEach(p => {
            if (p.includes(permission_type)) {
                p_split = p.split('_');
                scope = p_split[0]
            }
        })

        let msg = '';
        let denied = false;
        for (let i = 0; i < checkInputs.length; i++) {
            var owner_id = checkInputs[i].dataset.owner;
            if (owner_id){
                if (scope == 'user'){
                    if (owner_id.toString() !== user_id.toString()) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分のアイテムのみ編集または削除することができます。";
                        {% else %}
                        msg = "Action denied. You are only allowed to edit or delete your own items.";
                        {% endif %}   
                        denied = true;
                        if (source !== 'action_from_row') checkInputs[i].click()
                    }
                } else if (scope == 'team'){
                    if (!members.includes(owner_id.toString())) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分または自分のチームに割り当てられたアイテムのみ編集または削除できます。";
                        {% else %}
                        msg = "Action denied. You can only edit or delete items assigned to you or your team.";
                        {% endif %}
                        denied = true;
                        if (source !== 'action_from_row') checkInputs[i].click()
                    }
                } 
            }
        }
        if (permission_type === 'archive'){
            source = args[2]
        }
        if (denied) {
            event.preventDefault();
            event.stopImmediatePropagation();
            event.stopPropagation()
            document.getElementById('permissionActionWarning').innerHTML = msg;
            setTimeout(() => {
                document.getElementById('permissionActionWarning').innerHTML = '';
            }, 4000);
            msg = ''
        } else if (source){
            const modalEl = document.getElementById(source);
            const modal = bootstrap.Modal.getOrCreateInstance(modalEl);
            modal.show();
        }
        
    }
</script>

{% endif%}
</div>
{% endblock %}