{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<style>
    table {
        border-collapse: separate;
        border-spacing: 0;
    }

    th {
        position: sticky;
        top: 0;
        background: #fff !important;
        border-bottom: 1px solid #ccc !important;
        z-index: 10;
    }

</style>

<div class="estimate_table table-responsive" style="max-height: 75vh;">
    <table class="{% include "data/utility/table.html" %} estimates-table">
        <thead class="{% include "data/utility/table-header.html" %} position-sticky">
            <tr class="align-middle">
                <th class="min-w-40px"></th>
                {% for column in view_filter.column|safe|string_list_to_list %}
                    <th {% if column == 'checkbox' %}
                            class="w-10px"
                        {% elif column ==  'id_est' %}
                            class="{% include "data/utility/column-id.html" %}"
                        {% else %}
                            style="z-index: -10"
                        {% endif %}
                        >
                        {% with args=column|add:'|'|add:object_type %}
                            {% if column|split:'|'|length > 1 %}
                                {% with channel_column=column|split:'|'|search_custom_field_object_order_customer:request %}
                                    {{channel_column.name}}
                                {% endwith %}
                            {% else %}
                                {% with column_display=args|get_column_display:request %}
                                    {{column_display.name}}
                                {% endwith %}
                            {% endif %}
                        {% endwith %}
                    </th>
                    {% if column ==  'id_est' %}
                    <th class="" style="width: 20px;z-index: -10">
                    </th>
                    {% endif %}
                {% endfor %}
            </tr>
        </thead>

        <tbody>
            {% for estimate in estimates %}
                {% include "data/common/dynamic_table/dynamic-datatable-row.html" with row_detail_url="estimate_row_detail" item=estimate %}
            {% endfor %}
        </tbody>
    </table>
</div>

{% if est_id %}
    <a  hx-get="{% host_url 'estimate_edit' est_id host 'app' %}"
        hx-target="#manage-full-drawer-content"
        hx-vals = '{"set_id": "{{set_id}}","module":"{{module}}"}'
        hx-on="htmx:beforeSend:
            document.getElementById('manage-full-drawer-content').innerHTML = '';
            "
        hx-trigger="click"
        hx-indicator=".loading-drawer-spinner"
        class="estimate-open manage_full_wizard_button d-none text-center mb-0 text-dark text-hover-primary fw-bolder cursor-pointer estimate_{{est_id}}">
    </a>
<script>
    $(document).ready(function() {
        setTimeout(function() {
            document.getElementsByClassName('estimate-open')[0].click();
        }, 0);
    });
</script>
{% endif %}

{% if isopen and isopen != '' %} 
     <a
        hx-get="{% host_url 'estimate_edit' isopen host 'app' %}"
        hx-target="#manage-full-drawer-content"
        hx-vals = '{"set_id": "{{set_id}}","module":"{{module}}","side_drawer": "{{side_drawer}}","view_id": "{{view_filter.view.id}}"}'
        hx-on:htmx:before-send="document.getElementById('manage-full-drawer-content').innerHTML = '';document.getElementById('create-new-drawer-content').innerHTML = '';"
        hx-trigger="click"
        hx-indicator=".loading-drawer-spinner,.billings-form"        
        class="text-center mb-0 text-dark text-hover-primary fw-bolder manage_full_wizard_button cursor-pointer estimate_open">
    </a>
    <script>
        $(document).ready(function() {
            setTimeout(function() {
                document.querySelector('.estimate_open').click();
            }, 500);
        });
    </script>
{% endif %}

{% include "data/common/paginator/paginator.html" with redirect_url=pagination_url %}

{% include "data/common/dynamic_table/dynamic-datatable-js.html" with rows=estimates table_name="estimates" %}

