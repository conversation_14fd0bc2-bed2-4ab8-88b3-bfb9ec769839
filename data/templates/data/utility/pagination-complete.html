{% comment %}
Enhanced reusable pagination component based on order table design
Usage:
{% include "data/utility/pagination-complete.html" with
    page_content=page_content
    paginator_item_begin=paginator_item_begin
    paginator_item_end=paginator_item_end
    paginator=paginator
    menu_key=menu_key
    object_type=object_type
%}
{% endcomment %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<div class="{% include "data/utility/pagination.html" %}">
    {% if LANGUAGE_CODE == 'ja'%}
        {{paginator_item_begin}}–{{paginator_item_end}} の {{paginator.count}} 件
    {% else %}
        Viewing {{paginator_item_begin}}–{{paginator_item_end}} of {{paginator.count}} results
    {% endif %}

    <div class="ms-5">
        {% if page_content.has_previous %}
            <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page=1&{% query_transform %}">&laquo; {% translate_lang "First" LANGUAGE_CODE %}</a>
            <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.previous_page_number }}&{% query_transform %}">&lsaquo; {% translate_lang "Previous" LANGUAGE_CODE %}</a>
        {% endif %}

        {% if page_content.has_next %}
            <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.next_page_number }}&{% query_transform %}">{% translate_lang "Next" LANGUAGE_CODE %} &rsaquo;</a>
            <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.paginator.num_pages }}&{% query_transform %}">{% translate_lang "Last" LANGUAGE_CODE %} &raquo;</a>
        {% endif %}
    </div>
</div>
