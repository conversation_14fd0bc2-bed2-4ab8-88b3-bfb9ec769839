{% comment %}
Shared Table Column Header Component
Usage: {% include "data/utility/table-column-header.html" with column=column_name object_type=object_type %}
{% endcomment %}

{% load custom_tags %}
{% load i18n %}
{% get_current_language as LANGUAGE_CODE %}

<th {% if column == 'checkbox' %}
        class="{% include 'data/utility/column-checkbox-size.html' %}"
    {% elif column == 'contact_id' or column == 'order_id' or column == 'id' %}
        class="{% include 'data/utility/column-id-size.html' %}"
    {% else %}
        class="text-nowrap"
    {% endif %}>
    {% if column != 'checkbox' %}
        {% comment %}Handle different column types{% endcomment %}
        {% if object_type == 'contacts' %}
            {% if column|search_custom_field_object_contacts:request %}
                {% with channel_column=column|search_custom_field_object_contacts:request %}
                    {{channel_column.name|display_column_contacts:request}}
                {% endwith %}
            {% elif column|search_channel_objects:request %}
                {% with channel_column=column|search_channel_objects:request %}
                    {{channel_column}}
                {% endwith %}
            {% elif " - line user id" in column|lower %}
                {% with channel_column=column|get_line_name_channel %}
                    {{channel_column}}
                {% endwith %}
            {% else %}
                {% with column_name=column|display_column_contacts:request %}
                    {{column_name}}
                {% endwith %}
            {% endif %}
        {% elif object_type == 'orders' %}
            {% comment %}Add order-specific column header logic here{% endcomment %}
            {% with column_name=column|display_column_orders:request %}
                {{column_name}}
            {% endwith %}
        {% else %}
            {% comment %}Generic fallback{% endcomment %}
            {{ column|title }}
        {% endif %}
    {% endif %}
</th>
{% if column == 'contact_id' or column == 'order_id' %}
<th class="" style="width: 20px;"></th>
{% endif %}
