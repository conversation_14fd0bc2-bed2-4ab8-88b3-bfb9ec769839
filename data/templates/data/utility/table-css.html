<style>
    /* GENERIC CSS: Works for all tables with DataTables */

    table {
        width: auto;
    }

    td, th {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* DataTables Integration - Common patterns from orders table */
    .dataTables_scrollHead {
        position: sticky !important;
        top: 0 !important; /* Override DataTables default */
        z-index: 3;
        background-color: white;
    }

    /* GENERIC FALLBACK: Works for any table with DataTables FixedColumns */
    /* Remove JavaScript debugging borders and backgrounds */
    table th:nth-child(1)[style*="background-color"],
    table td:nth-child(1)[style*="background-color"],
    table th:nth-child(2)[style*="background-color"],
    table td:nth-child(2)[style*="background-color"] {
        background-color: white !important;
        border: none !important;
    }

    /* Ensure header borders are consistent */
    table thead th,
    table thead td {
        background-color: white !important;
    }

    /* Style the table within the scroll head */
    .dataTables_wrapper .dataTables_scrollHead table {
        margin-bottom: 0 !important;
        background-color: white !important;
    }

    .dataTables_wrapper .dataTables_scrollHead table thead tr {
        background-color: white !important;
    }

    /* Override any manual positioning that conflicts with FixedColumns */
    table th:nth-child(1):not(.dtfc-fixed-left),
    table td:nth-child(1):not(.dtfc-fixed-left),
    table th:nth-child(2):not(.dtfc-fixed-left),
    table td:nth-child(2):not(.dtfc-fixed-left) {
        position: static !important;
        left: auto !important;
    }

    /* Ensure DataTables wrapper allows proper scrolling */
    .dataTables_wrapper {
        overflow: visible !important;
    }

    .dataTables_wrapper .dataTables_scroll {
        overflow: visible !important;
    }

    .dataTables_wrapper .dataTables_scrollBody {
        overflow-x: auto !important;
        overflow-y: auto !important;
    }

    /* GLOBAL FIX: Ensure ALL dropdown menus appear above DataTables elements */
    .dropdown-menu,
    .dropdown-menu.show,
    ul.dropdown-menu,
    ul.dropdown-menu.show {
        z-index: 1060 !important; /* Higher than Bootstrap modal backdrop (1050) */
    }

    /* Specific fix for view dropdown menus that appear outside table containers */
    .dropdown-menu[data-popper-placement],
    .dropdown-menu[style*="transform"] {
        z-index: 1070 !important; /* Even higher for positioned dropdowns */
    }

    /* Ensure view container allows dropdown overflow but maintains positioning */
    #view-container {
        overflow: visible !important;
        position: relative !important;
    }

    /* Ensure table navigation area allows dropdown overflow but maintains layout */
    .nav.nav-tabs.nav-line-tabs,
    .tw-flex.align-items-end.justify-content-between,
    .d-flex.align-items-end.justify-content-between {
        overflow: visible !important;
        position: relative !important;
    }

    /* Ensure dropdown container allows overflow */
    .nav-item {
        overflow: visible !important;
    }

</style>

