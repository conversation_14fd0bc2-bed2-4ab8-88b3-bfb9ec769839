{% comment %}
Shared Table Container Wrapper Component
Usage: {% include "data/utility/table-container-wrapper.html" with table_id="your-table-id" table_class="your-table-class" %}
{% endcomment %}

<div class="pt-0 table-responsive" style="max-height: 75vh;">
    <table id="{{ table_id|default:'data-table' }}" class="{% include "data/utility/table.html" %} {{ table_class|default:'data-table' }}">
        <thead class="{% include "data/utility/table-header.html" %} position-sticky">
            {% block table_header %}
            {% comment %}Override this block in the including template{% endcomment %}
            {% endblock %}
        </thead>
        <tbody class="fs-6">
            {% block table_body %}
            {% comment %}Override this block in the including template{% endcomment %}
            {% endblock %}
        </tbody>
    </table>
</div>
