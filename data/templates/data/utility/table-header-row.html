{% comment %}
Reusable table header row component
Usage: {% include "data/utility/table-header-row.html" with columns=table_columns object_type="company" %}
{% endcomment %}

<tr class="align-middle border-bottom border-bottom-1">
    {% for column in columns %}
    <th {% if column == 'checkbox' %} 
        class="{% include "data/utility/column-checkbox-size.html" %}" 
        {% elif column == object_type|add:'_id' %}
        class="{% include "data/utility/column-id-size.html" %}"
        {% else %} 
        class="text-nowrap" 
        {% endif %}>
            {% if column != 'checkbox' %}
                {% comment %} Column header display logic will be customized per object type {% endcomment %}
                {{ column|title }}
            {% endif %}
    </th>
    {% if column == object_type|add:'_id' %} 
    <th class="" style="width: 20px;">
    </th>
    {% endif %}
    {% endfor %}
</tr>
