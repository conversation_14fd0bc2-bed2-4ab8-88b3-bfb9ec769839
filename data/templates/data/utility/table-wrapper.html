{% comment %}
Reusable table wrapper with responsive design and scrolling
Usage: {% include "data/utility/table-wrapper.html" with table_class="orders-table" %}
{% endcomment %}

<div class="pt-0 table-responsive" style="max-height: 75vh;">
    <table class="{% include "data/utility/table.html" %} {{ table_class|default:"data-table" }}">
        <thead class="{% include "data/utility/table-header.html" %} position-sticky">
            {{ table_header }}
        </thead>
        <tbody class="fs-6">
            {{ table_body }}
        </tbody>
    </table>
</div>
