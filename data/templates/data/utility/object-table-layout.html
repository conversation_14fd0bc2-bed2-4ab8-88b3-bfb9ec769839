{% comment %}
Reusable object table layout component following orders template pattern
Usage: 
{% include "data/utility/object-table-layout.html" with 
    object_type="company"
    objects=companies
    columns=companies_columns
    config_view=config_view
    form_id="company-form"
    table_class="companies-table"
%}
{% endcomment %}

<div class="d-flex flex-column flex-lg-row">
    <div class="flex-lg-row-fluid">
        <form method="POST" id="{{ form_id|default:'object-form' }}">
            {% csrf_token %}

            {% comment %}Hidden form fields for column configuration{% endcomment %}
            {% if columns %}
            <input type="hidden" name="{{ object_type }}_columns" value="{{ columns }}" />
            {% endif %}

            {% if config_view == 'list' %}
                <div class="pt-0 table-responsive" style="max-height: 75vh;">
                    {{ table_content }}
                </div>
            {% elif config_view == 'card' %}
                <div class="w-100 row row-eq-height d-flex flex-wrap g-6 gy-5">
                    {{ card_content }}
                </div>
            {% elif config_view == 'kanban' %}
                {{ kanban_content }}
            {% endif %}

        </form>
    </div>
</div>
