{% comment %}
Shared DataTables Configuration Component
Usage: {% include "data/utility/table-datatables-config.html" with table_class="your-table-class" %}
{% endcomment %}
{% load i18n %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<script>
    function initializeDataTable(tableClass = '{{ table_class|default:"data-table" }}') {
        var table = $("." + tableClass).DataTable({
            scrollX: true,
            scrollY: "75vh",
            scrollCollapse: true,
            autoWidth: true,
            fixedColumns: {
                left: 2
            },
            ordering: false,
            searching: false,
            paging: false,
            info: false,
            language: {
                emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
            },
            columnDefs: [
                {
                    targets: '_all',
                    className: 'text-nowrap'
                }
            ]
        });

        // Adjust table width after initialization
        $(window).on('resize', function() {
            table.columns.adjust();
        });

        return table;
    }
</script>
