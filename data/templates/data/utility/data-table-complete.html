{% comment %}
Complete reusable data table template
Usage: 
{% include "data/utility/data-table-complete.html" with 
    table_class="companies-table" 
    object_list=companies 
    columns=companies_columns 
    object_type="company"
    row_detail_url="company_row_detail"
    view_filter=view_filter
    page=page
    target=target
%}
{% endcomment %}

<div class="pt-0 table-responsive" style="max-height: 75vh;">
    <table class="{% include "data/utility/table.html" %} {{ table_class }}">
        <thead class="{% include "data/utility/table-header.html" %} position-sticky">
            <tr class="align-middle border-bottom border-bottom-1">
                {% for column in columns %}
                <th {% if column == 'checkbox' %} 
                    class="{% include "data/utility/column-checkbox-size.html" %}" 
                    {% elif column == object_type|add:'_id' %}
                    class="{% include "data/utility/column-id-size.html" %}"
                    {% else %} 
                    class="text-nowrap" 
                    {% endif %}>
                        {% if column != 'checkbox' %}
                            {% comment %} Column header display logic - customize per object type {% endcomment %}
                            {% if object_type == 'company' %}
                                {% if column|search_custom_field_object_company:request %}
                                    {% with channel_column=column|search_custom_field_object_company:request %}
                                        {{channel_column.name|display_column_company:request}}
                                    {% endwith %}
                                {% elif "child" in column and "|" in column %}
                                    {% with channel_column=column|search_custom_field_object_company_child:request %}
                                        {{channel_column.name|display_column_company:request}} |
                                        {% if LANGUAGE_CODE == 'ja' %}子オブジェクト{% else %}Child Objects{% endif %}
                                    {% endwith %}
                                {% else %}
                                    {% with company_column=column|display_column_company:request %}
                                        {{company_column}}
                                    {% endwith %}
                                {% endif %}
                            {% else %}
                                {{ column|title }}
                            {% endif %}
                        {% endif %}
                </th>
                {% if column == object_type|add:'_id' %} 
                <th class="" style="width: 20px;">
                </th>
                {% endif %}
                {% endfor %}
            </tr>
        </thead>

        <tbody class="fs-6">
            {% for object in object_list %}
                <tr id="row-{{object.id}}"
                hx-get="{% host_url row_detail_url object.id host 'app' %}"
                hx-vals='{"view_id": "{{view_filter.view.id}}", "selected_{{ object_type }}_id": "{{selected_object_id}}", "page": "{{page}}", "target":"{{target}}"}'
                hx-trigger="load"
                hx-indicator=".row_load-{{object.id}}">
                    {% include "data/utility/table-row-loading.html" with object_id=object.id %}
                </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
