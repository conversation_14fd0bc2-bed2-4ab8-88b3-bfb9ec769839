{% comment %}
Shared Table Row Loading Component
Usage: {% include "data/utility/table-row-loading.html" with object_id=object.id colspan=columns_count %}
{% endcomment %}

<td class="d-flex justify-content-center w-100" {% if colspan %}colspan="{{ colspan }}"{% endif %}>
    <style>
        /* Styles for the spinner */
        .row_load-{{ object_id }} {
            display: none; /* Initially hidden */
        }
        .htmx-request .row_load-{{ object_id }},
        .htmx-request.row_load-{{ object_id }} {
            display: inline-block; /* Display during htmx request */
        }
    </style>
    <!-- Spinner icon -->
    <span class="spinner-border spinner-border-lg text-secondary row_load-{{ object_id }}" role="status">
        <span class="visually-hidden">Loading...</span>
    </span>
</td>
