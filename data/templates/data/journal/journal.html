{% extends 'base.html' %}
{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% block content %}

<div class="d-flex align-items-end mb-2 align-items-center" style="background-color: #FAFAFA !important;">
    {% include 'data/common/module-header-tabs.html' %}
    {% include 'data/common/view-menu-new-design.html' %}
</div>
{% if permission == 'hide' %}
    {% include 'data/static/no-access-page.html' %}
{% else %}
    <div class="{% include "data/utility/table-container.html" %}">
        {% include 'data/common/permission-action-warning-message.html' %}
        {% include 'data/common/view-menu.html' %}
        {% if view_filter.view_type == 'journal' %}
            {% include 'data/journal/journal-journal-view-table.html' %}
        {% elif view_filter.view_type == 'balance_sheet' %}
            {% include 'data/journal/journal-balance-sheet-view-table.html' %}
        {% elif view_filter.view_type == 'profit_and_loss' %}
            {% include 'data/journal/journal-profit-and-loss-view-table.html' %}
        {% elif view_filter.view_type == 'receivable_list' or view_filter.view_type == 'payable_list' %}
            {% include 'data/journal/journal-receivable-payable-list-table.html' %}
        {% else %} 
            {% comment %} this is for view_filter.view_type == 'table' or others view in future {% endcomment %}
            {% include 'data/journal/journal-table.html' %}
        {% endif%}
        
        {% if selected_object %}
            <a 
                hx-get="{% host_url manage_obj_link selected_object.id host 'app' %}?view_id={{view_id}}"  
                hx-target="#accounting_form_lg"
                hx-trigger="click"
                id="invoice_wizard_button"
                hx-indicator=".loading-drawer-spinner,.accounting_form_lg"
                class="selected_object_{{selected_object.id}} accounting_wizard_lg_button">
            </a>
            <script>
                window.onload = function() {
                    document.querySelector('.selected_object_{{selected_object.id}}').click();
                };
            </script>
        {% endif %}
    </div>
{% endif%}
{% endblock %}

{% block js %}
    {% include 'data/javascript/analyticsMembersJS.html' %}
    {% include 'data/journal/journalJS.html' %}
{% endblock %}
