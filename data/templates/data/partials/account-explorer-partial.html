{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}


<style>
    .filter-box .select2-container .select2-selection--single {height:40px !important}
</style>


<div class="d-flex flex-column w-100">
    <div class="px-10 pt-0 py-10 mt-5">
        <div>
            <div class="mb-5">
                <div class="d-flex align-items-center">
                    <form id="filter-form-search" method="get" class="min-w-150px w-100">
        
                        <div class="d-flex mb-0 position-relative align-items-center">
                            <span class="svg-icon svg-icon-3 search-icon position-absolute top-50 translate-middle-y ms-4">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                                    <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                                </svg>
                            </span>
                                
                            <input
                            id="base-search-input" type="text" name="q" class="form-control bg-white ps-12 h-40px me-2"
                            value={% if search_q %} "{{ search_q }}" {% else %}""{% endif %}
                            {% if page_type != 'companies' %}
                            placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                            {% else %}
                            placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                            {% endif %}
                            onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                            >
                            <input type="hidden" value="{{view_id}}" name="view_id">
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="d-flex align-items-center w-100 mb-5">
            <div class="bg-gray-100 rounded me-2 d-flex px-5 align-items-center h-40px">
                <div class=" ">
                    <input class="form-check-input" type="checkbox" id="selectAll" onchange="select_all()" />
                    {% if LANGUAGE_CODE == 'ja'%}
                        すべて選択
                    {% else %}
                    Select All
                    {% endif %}
                </div>
            </div>
            <div class=" me-2">
                <button id='update-contact' class="btn btn-sm bg-gray-100 w-100 me-2 h-40px disabled" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-overflow="true">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19.4 13.9411L10.7 5.24112C10.4 4.94112 10 4.84104 9.60001 5.04104C9.20001 5.24104 9 5.54107 9 5.94107V18.2411C9 18.6411 9.20001 18.941 9.60001 19.141C9.70001 19.241 9.9 19.2411 10 19.2411C10.2 19.2411 10.4 19.141 10.6 19.041C11.4 18.441 12.1 17.941 12.9 17.541L14.4 21.041C14.6 21.641 15.2 21.9411 15.8 21.9411C16 21.9411 16.2 21.9411 16.4 21.8411C17.2 21.5411 17.5 20.6411 17.2 19.8411L15.7 16.2411C16.7 15.9411 17.7 15.741 18.8 15.541C19.2 15.541 19.5 15.2411 19.6 14.8411C19.8 14.6411 19.7 14.2411 19.4 13.9411Z" fill="currentColor"/>
                        <path opacity="0.3" d="M15 6.941C14.8 6.941 14.7 6.94102 14.6 6.84102C14.1 6.64102 13.9 6.04097 14.2 5.54097L15.2 3.54097C15.4 3.04097 16 2.84095 16.5 3.14095C17 3.34095 17.2 3.941 16.9 4.441L15.9 6.441C15.7 6.741 15.4 6.941 15 6.941ZM18.4 9.84102L20.4 8.84102C20.9 8.64102 21.1 8.04097 20.8 7.54097C20.6 7.04097 20 6.84095 19.5 7.14095L17.5 8.14095C17 8.34095 16.8 8.941 17.1 9.441C17.3 9.841 17.6 10.041 18 10.041C18.2 9.94097 18.3 9.94102 18.4 9.84102ZM7.10001 10.941C7.10001 10.341 6.70001 9.941 6.10001 9.941H4C3.4 9.941 3 10.341 3 10.941C3 11.541 3.4 11.941 4 11.941H6.10001C6.70001 11.941 7.10001 11.541 7.10001 10.941ZM4.89999 17.1409L6.89999 16.1409C7.39999 15.9409 7.59999 15.341 7.29999 14.841C7.09999 14.341 6.5 14.141 6 14.441L4 15.441C3.5 15.641 3.30001 16.241 3.60001 16.741C3.80001 17.141 4.1 17.341 4.5 17.341C4.6 17.241 4.79999 17.2409 4.89999 17.1409Z" fill="currentColor"/>
                    </svg>
                    {% if LANGUAGE_CODE == 'ja'%}
                    アクション
                    {% else %}
                    Action
                    {% endif %}
    
                </button>
                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-bold w-200px" data-kt-menu="true">
                    <div class="mb-5">
                        <div class="separator opacity-75 mb-5"></div>
                        {% comment %} <h3 class="px-6 text-muted fs-6">Twitter</h3> {% endcomment %}
                        <div class="menu-item px-3">
                            {% comment %} <div class="menu-content px-2 py-1">
                                <button class="border-0 bg-transparent px-4" data-bs-toggle="modal" data-bs-target="#manage_twitter_unfollow">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    メッセージを送る
                                    {% else %}
                                    Send DM
                                    {% endif %}
    
                                </button>
                            </div>

                            <div class="menu-content px-2 py-1">
                                <button class="border-0 bg-transparent px-4" data-bs-toggle="modal" data-bs-target="#manage_twitter_follow">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    フォローする
                                    {% else %}
                                    Follow Accounts
                                    {% endif %}
    
                                </button>
                            </div>
                            <div class="menu-content px-2 py-1">
                                <button class="border-0 bg-transparent px-4" data-bs-toggle="modal" data-bs-target="#manage_twitter_unfollow">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    フォローを外す
                                    {% else %}
                                    Unfollow Accounts
                                    {% endif %}
                                </button>
                            </div> {% endcomment %}
                            <div class="menu-content px-2 py-1">
                                <button name="bulk_delete_contacts" class="border-0 bg-transparent px-4" data-bs-toggle="modal" data-bs-target="#manage_delete_bulk">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    一括削除
                                    {% else %}
                                    Delete Bulk
                                    {% endif %}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="me-2">
                <button id='csv_download-contact' class="btn btn-sm bg-gray-100 w-100 h-40px disabled" type="submit" name="csv_download" form="contact-company-form">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M21 22H3C2.4 22 2 21.6 2 21C2 20.4 2.4 20 3 20H21C21.6 20 22 20.4 22 21C22 21.6 21.6 22 21 22ZM13 13.4V3C13 2.4 12.6 2 12 2C11.4 2 11 2.4 11 3V13.4H13Z" fill="black"/>
                        <path opacity="0.3" d="M7 13.4H17L12.7 17.7C12.3 18.1 11.7 18.1 11.3 17.7L7 13.4Z" fill="black"/>
                    </svg>
                    {% if LANGUAGE_CODE == 'ja'%}
                    ダウンロード
                    {% else %}
                    Download
                    {% endif %}
                </button>
            </div>
        </div>

        

        <div class="mb-2 d-none" id="d-select-additional-options">
            <div class="status bg-light-dark text-center pt-4 pb-3 px-5 rounded">
                {% if LANGUAGE_CODE == 'ja'%}
                このページの全ての連絡先が選択されました。 
                {% else %}
                All contacts on this page are selected. 
                {% endif %}
    
                 <a onclick="toggleText()" 
                    class="btn btn-dark" 
                    data-bs-toggle="collapse" 
                    id="select-additional-options-toggle" 
                    role="button" 
                    aria-expanded="false" 
                    aria-controls="collapseExample">
                    {% if LANGUAGE_CODE == 'ja'%}
                    すべての連絡先を選択する
                    {% else %}
                    Select all contacts
                    {% endif %}
                </a>
                
            </div>
        </div>


        {% if page_type != 'companies' %}
        <form method="POST" action="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}" id="contact-company-form">
            {% csrf_token %}
            <div class="d-flex flex-column flex-lg-row">
                <div class="flex-lg-row-fluid">
                    <div class="pt-0">
                        <div class="row row-eq-height d-flex flex-wrap g-6 gy-3 " id="contact_cards">

                        {% for contact in contacts %}
                        <div class="col-md-6 col-lg-4">
                            <div class="card h-100 border">
                                <div class="card-body position-relative">
                                    <input style="left:10px; top:10px" id="{{contact.id}}" class="form-check-input position-absolute" type="checkbox" name="checkbox" value="{{contact.id}}" onclick="checking_checkbox(this)"/>
                                    <div class="text-center mb-3">
                                        <a class="text-dark cursor-pointer"
                                            hx-get="{% host_url 'load_explore_profile' contact.id host 'app' %}"
                                            {% if contact_list.id %}
                                                hx-vals='{"contact_list_id":"{{ contact_list.id }}"}'
                                            {% else %}
                                                hx-vals='{"contact_list_id":""}'
                                            {% endif %}
                                            hx-target="#manage-profile"
                                            hx-trigger="click"
                                            id="profile_wizard_button"
                                            >  
                                           
                                            {% if contact.image_url %}
                                            <div class="symbol symbol-50px">
                                                <img alt="Pic" src="{{ contact.image_url }}" style="object-fit:cover !important; aspect-ratio: 9 / 9;"/>
                                            </div>
                                            {% elif contact.image_file %}
                                            <div class="symbol symbol-50px">
                                                <img alt="Pic" src="{{ contact.image_file.url }}" style="object-fit:cover !important; aspect-ratio: 9 / 9;"/>
                                            </div>
                                            {% else %}
                                            <span class="svg-icon svg-icon-muted svg-icon-4hx">
                                                <svg class="h-50px w-50px" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path opacity="0.3" d="M16.5 9C16.5 13.125 13.125 16.5 9 16.5C4.875 16.5 1.5 13.125 1.5 9C1.5 4.875 4.875 1.5 9 1.5C13.125 1.5 16.5 4.875 16.5 9Z" fill="currentColor"/>
                                                <path d="M9 16.5C10.95 16.5 12.75 15.75 14.025 14.55C13.425 12.675 11.4 11.25 9 11.25C6.6 11.25 4.57499 12.675 3.97499 14.55C5.24999 15.75 7.05 16.5 9 16.5Z" fill="currentColor"/>
                                                <rect x="7" y="6" width="4" height="4" rx="2" fill="currentColor"/>
                                                </svg>
                                            </span>    
                                            {% endif %}
                                            
                                        </a> 


                                    </div>
                                    <div class="d-flex flex-column justify-content-center">
                                        <a 
                                            hx-get="{% host_url 'load_explore_profile' contact.id host 'app' %}"
                                            {% if contact_list.id %}
                                                hx-vals='{"contact_list_id":"{{ contact_list.id }}"}'
                                            {% else %}
                                                hx-vals='{"contact_list_id":""}'
                                            {% endif %}
                                            hx-target="#manage-profile"
                                            hx-trigger="click"
                                            id="profile_wizard_button"
                                            class="text-center mb-0 text-dark text-hover-primary fw-bolder cursor-pointer">
                                            {{contact|display_contact_name:LANGUAGE_CODE}}
                                        </a>
                                    </div>

                                
                                    {% if contact.bio %}
                                        <div class="text-center fw-bold fs-6 text-gray-500 mb-3">
                                            {% if contact.bio|length > 120 %}
                                                {{contact.bio|safe|truncatewords:5|truncatechars:50}}
                                            {% else %}
                                                {{contact.bio|safe|truncatewords:10|truncatechars:50}}
                                            {% endif %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        <div class="{% include "data/utility/pagination.html" %}">
                            {% if LANGUAGE_CODE == 'ja'%}
                            {{paginator_item_begin}}–{{paginator_item_end}} の {{paginator.count}} 件
                            {% else %}
                            Viewing {{paginator_item_begin}}–{{paginator_item_end}} of {{paginator.count}} results
                            {% endif %}
            
                            <div>
                                {% if contact_list %} 
                                    {% if page_content.has_previous %}     
                                    <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'list_detail' id=contact_list.id host 'app' %}?page=1&{% query_transform %}">&laquo; First</a>
                                    <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'list_detail' id=contact_list.id host 'app' %}?page={{ page_content.previous_page_number }}&{% query_transform %}">Previous</a>
                                    {% endif %}
                                            
                                    {% if page_content.has_next %}
                                        <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'list_detail' id=contact_list.id host 'app' %}?page={{ page_content.next_page_number }}&{% query_transform %}">Next</a>
                                        <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'list_detail' id=contact_list.id host 'app' %}?page={{ page_content.paginator.num_pages }}&{% query_transform %}">Last &raquo;</a>
                                    {% endif %}
                                {% else %}
                                    {% if page_content.has_previous %}     
                                        <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page=1&{% query_transform %}">&laquo; First</a>
                                        <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.previous_page_number }}&{% query_transform %}">Previous</a>
                                    {% endif %}
                                            
                                    {% if page_content.has_next %}
                                        <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.next_page_number }}&{% query_transform %}">Next</a>
                                        <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.paginator.num_pages }}&{% query_transform %}">Last &raquo;</a>
                                    {% endif %}
                                {% endif %}

                            </div>
                        </div>
                    </div>
                </div>
            </div>  
            
            <div class="modal fade" tabindex="-1" id="manage_delete_bulk">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header pb-0 border-0 justify-content-end">
                            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                            </div>
                        </div>
                        <div class="modal-body pb-0">
                            <div class="mb-13 text-center">
                                <h3 class="modal-title">
                                    
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    一括削除の確認
                                    {% else %}
                                    Bulk Delete Confirmations
                                    {% endif %}
                                
                                </h3>
                            </div>
                            <div class="border-bottom">
                                <div class="fv-rowd-flex flex-column mb-8">
                                    <label class="{% include 'data/utility/form-label.html' %}">
                                        <span class="">
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            この操作は元に戻すことができません。連絡先を削除してもよろしいですか?
                                            {% else %}
                                            This actions cannot be undone. Are you sure to delete contacts?
                                            {% endif %}
                                        </span>
                                    </label>
                               
                                </div>
                            </div>

                        </div>
                        
                        <input name='contact_list' value="{{contact_list.id}}" hidden> 
                        <input name='flag_all' class="flag_all" hidden></input>
        
                        <div class="modal-footer border-0">
                            <button name="bulk_delete_contacts" type="submit" class="btn btn-danger">{% trans 'Delete'%}</button>
                            <a data-bs-dismiss="modal" class="btn btn-dark">
                                {% if LANGUAGE_CODE == 'ja'%}
                                キャンセル
                                {% else %}
                                Cancel
                                {% endif %}

                            </a>
                        </div>
                    </div>
                </div>
            </div> 
            
        </form>
        {% endif %}

        

        {% if page_type == 'companies' %}
        <form method="POST" action="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}" id="contact-company-form">
            {% csrf_token %}
            <div class="d-flex flex-column flex-lg-row">
                <div class="flex-lg-row-fluid">
                    <div class="pt-0">
                        <div class="row row-eq-height d-flex flex-wrap g-6 gy-5">

                        {% for company in companies %}
                        <div class="col-md-6 col-lg-4">
                            <div class="card h-100 border">
                                <div class="card-body position-relative">
                                    <input style="left:10px; top:10px" id="{{company.id}}" class="form-check-input position-absolute" type="checkbox" name="checkbox" value="{{company.id}}" onclick="checking_checkbox(this)"/>
                                    <div class="text-center mb-3">
                                        <a class="text-dark cursor-pointer"
                                            hx-get="{% host_url 'load_explore_company' company.id host 'app' %}"
                                            hx-target="#manage-profile-company"
                                            hx-trigger="click"
                                            id="profile_company_wizard_button"
                                            >  
                                           
                                            {% if company.image_url %}
                                            <div class="symbol symbol-50px">
                                                <img alt="Pic" src="{{ company.image_url }}" style="object-fit:cover !important; aspect-ratio: 9 / 9;"/>
                                            </div>
                                            {% elif company.image_file %}
                                            <div class="symbol symbol-50px">
                                                <img alt="Pic" src="{{ company.image_file.url }}" style="object-fit:cover !important; aspect-ratio: 9 / 9;"/>
                                            </div>
                                            {% else %}
                                            <span class="svg-icon svg-icon-muted svg-icon-4hx">
                                                <svg class="h-50px w-50px" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path opacity="0.3" d="M16.5 9C16.5 13.125 13.125 16.5 9 16.5C4.875 16.5 1.5 13.125 1.5 9C1.5 4.875 4.875 1.5 9 1.5C13.125 1.5 16.5 4.875 16.5 9Z" fill="currentColor"/>
                                                <path d="M9 16.5C10.95 16.5 12.75 15.75 14.025 14.55C13.425 12.675 11.4 11.25 9 11.25C6.6 11.25 4.57499 12.675 3.97499 14.55C5.24999 15.75 7.05 16.5 9 16.5Z" fill="currentColor"/>
                                                <rect x="7" y="6" width="4" height="4" rx="2" fill="currentColor"/>
                                                </svg>
                                            </span>    
                                            {% endif %}
                                            
                                        </a> 
                                    </div>
                                    <div class="d-flex flex-column justify-content-center">
                                        <a 
                                            hx-get="{% host_url 'load_explore_company' company.id host 'app' %}"
                                            hx-target="#manage-profile-company"
                                            hx-trigger="click"
                                            id="profile_company_wizard_button"
                                            class="text-center mb-0 text-dark text-hover-primary fw-bolder cursor-pointer">
                                                {{company.name}}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="{% include "data/utility/pagination.html" %}">
                        {% if LANGUAGE_CODE == 'ja'%}
                        {{paginator_item_begin}}–{{paginator_item_end}} の {{paginator.count}} 件
                        {% else %}
                        Viewing {{paginator_item_begin}}–{{paginator_item_end}} of {{paginator.count}} results
                        {% endif %}
                        <div>
                            {% if page_content.has_previous %}     
                                <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page=1&{% query_transform %}">&laquo; First</a>
                                <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.previous_page_number }}&{% query_transform %}">Previous</a>
                            {% endif %}
                                    
                            {% if page_content.has_next %}
                                <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.next_page_number }}&{% query_transform %}">Next</a>
                                <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.paginator.num_pages }}&{% query_transform %}">Last &raquo;</a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>  


            <div class="modal fade" tabindex="-1" id="manage_delete_bulk">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header pb-0 border-0 justify-content-end">
                            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                            </div>
                        </div>
                        <div class="modal-body pb-0">
                            <div class="mb-13 text-center">
                                <h3 class="modal-title">
                                    
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    一括削除の確認
                                    {% else %}
                                    Bulk Delete Confirmations
                                    {% endif %}
                                
                                </h3>
                            </div>
                            <div class="border-bottom">
                                <div class="fv-rowd-flex flex-column mb-8">
                                    <label class="{% include 'data/utility/form-label.html' %}">
                                        <span class="">
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            この操作は元に戻すことができません。連絡先を削除してもよろしいですか?
                                            {% else %}
                                            This actions cannot be undone. Are you sure to delete companies?
                                            {% endif %}
                                        </span>
                                    </label>
                               
                                </div>
                            </div>
                        </div>
                        
                        <input name='company_list' value="{{company_list.id}}" hidden> 
                        <input name='flag_all' class="flag_all" hidden></input>
        
                        <div class="modal-footer border-0">
                            <button name="bulk_delete_companies" type="submit" class="btn btn-danger">{% trans 'Delete'%}</button>
                            <a data-bs-dismiss="modal" class="btn btn-dark">
                                {% if LANGUAGE_CODE == 'ja'%}
                                キャンセル
                                {% else %}
                                Cancel
                                {% endif %}
                            
                            </a>
                        </div>
                    </div>
                </div>
            </div> 
            
       </form>
        {% endif %}
    </div>
</div>
