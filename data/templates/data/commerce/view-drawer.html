{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}
{% comment %} ORDERS / ITEMS / SUBSCRIPTIONS / Invoice / Estimate {% endcomment %}
<div class="bg-white border-0 shadow-none card rounded-0">
    <div class="card-header" id="kt_help_header">
        <h5 class="text-gray-600 card-title fw-bold">
            {% if download_view %}
                {% if LANGUAGE_CODE == 'ja'%}
                    ダウンロードを管理
                {% else %}
                    Manage Download
                {% endif %}
            {% else %}
                {% if LANGUAGE_CODE == 'ja'%}
                    ビューの管理
                {% else %}
                    Manage Views
                {% endif %}
            {% endif %}
        </h5>
        <div class="card-toolbar">
            <button type="button" class="btn btn-sm btn-icon explore-btn-dismiss me-n5" data-kt-drawer-dismiss="true">
                <span class="svg-icon svg-icon-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="black"></rect>
                        <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
                    </svg>
                </span>
            </button>
        </div>
    </div>

    <div class="card-body">
        <div id="change-stamp-section" class="mb-10">
            <div class="">
                <form method="POST"
                    {% if download_form_url and download_view %}
                        action="{% host_url download_form_url host 'app' %}"
                    {% else %}
                        action="{% host_url 'commerce_view_setting' host 'app' %}"
                    {% endif %}
                    >
                    {% csrf_token %}

                    {% if module %}
                        <input type="hidden" name="module" value="{{module}}" />
                    {% endif %}

                    {% if view %}
                        <input type="hidden" name="view_id" {% if view.id %} value="{{view.id}}" {% endif %} />
                    {% endif %}

                    {% if p_id %}
                        <input type="hidden" name="p_id" {% if p_id %} value="{{p_id}}" {% endif %} />
                    {% endif %}

                    <input hidden id="object_type" name="object_type" value="{{object_type}}" />
                    <input hidden name="download_formats" value="{{download_formats|join:','}}" />

                    {% if view.title == "main" %}
                        <input type="hidden" name="name" value="{{view.title}}">
                    {% elif view.title or not view %}
                        {% if not download_view %}
                        <div class="mt-4 min-w-200px">
                            <span class="py-0 mb-3 border-0 fs-5 fw-bolder text-active-primary ms-0 required">
                                {% if LANGUAGE_CODE == 'ja'%}
                                名前
                                {% else %}
                                Name
                                {% endif %}
                            </span>

                            <input required class="form-control" type="text" name="name" {% if view.title == "main"%}disabled{% endif %}
                                placeholder="{% if LANGUAGE_CODE == 'ja' %}名前{% else %}Name{% endif %}"

                                {% if view and view.title == "main" %}
                                    value="main"
                                {% elif view and view.title %}
                                    value="{{view.title}}"
                                {% endif %}
                            />
                        </div>
                        {% endif %}
                    {% endif %}

                    <div class="min-w-200px mt-5 {% if download_view %}d-none{% endif %}">
                        <span class="py-0 mb-3 border-0 fs-5 fw-bolder text-active-primary ms-0">
                            {% if LANGUAGE_CODE == 'ja'%}
                            タイプ
                            {% else %}
                            Type
                            {% endif %}
                        </span>

                        <select required id="view-field" class="bg-white border min-h-40px form-select form-select-solid select2-this" data-control="select2" name="view_type" onchange="select_onchange(this)" placeholder="{% if LANGUAGE_CODE == 'ja' %}ビュー{% else %}View{% endif %}">
                            {% for vt in view_types %}
                                <option value="{{vt.0}}" {% if view_filter.view_type == vt.0 %}selected{% endif %}> {% if LANGUAGE_CODE == 'ja' %}{{vt.2}}{% else %}{{vt.1}}{% endif %} </option>
                            {% endfor %}
                        </select>

                        <script>
                            $(document).ready(function() {
                                var view_field_elm = document.getElementById('view-field')
                                select_onchange(view_field_elm)
                                
                                // Initialize sort method visibility
                                var orderBySelect = document.querySelector('select[name="order-by"]');
                                if (orderBySelect) {
                                    toggleSortMethodVisibility(orderBySelect);
                                }
                            });
                        </script>

                    </div>
                    <div id="sub-view-type" class="{% if view_filter.view_type == 'gantt_chart' %}{% else %}d-none{% endif %} mt-4 min-w-200px">
                        <div class="d-flex">
                            <span class="py-0 mb-3 border-0 fs-5 fw-bolder text-active-primary ms-0">
                            {% if LANGUAGE_CODE == 'ja'%}
                            グループ
                            {% else %}
                            Group By
                            {% endif %}
                        </span>

                        <div class="max-md:tw-hidden tw-flex me-2 d-flex justify-content-end">
                            <a class="max-md:tw-mb-3 py-1 d-flex align-items-center" href = "{% host_url 'workspace_setting' host 'app' %}?setting_type={{object_type}}&p_id={{p_id}}" target="_blank"
                                type="button">
                                <span id='' class="svg-icon svg-icon-primary svg-icon-2x cursor-pointer"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Design/Edit.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <rect x="0" y="0" width="24" height="24"/>
                                        <path d="M8,17.9148182 L8,5.96685884 C8,5.56391781 8.16211443,5.17792052 8.44982609,4.89581508 L10.965708,2.42895648 C11.5426798,1.86322723 12.4640974,1.85620921 13.0496196,2.41308426 L15.5337377,4.77566479 C15.8314604,5.0588212 16,5.45170806 16,5.86258077 L16,17.9148182 C16,18.7432453 15.3284271,19.4148182 14.5,19.4148182 L9.5,19.4148182 C8.67157288,19.4148182 8,18.7432453 8,17.9148182 Z" fill="#000000" fill-rule="nonzero" transform="translate(12.000000, 10.707409) rotate(-135.000000) translate(-12.000000, -10.707409) "/>
                                        <rect fill="#000000" opacity="0.3" x="5" y="20" width="15" height="2" rx="1"/>
                                    </g>
                                </svg><!--end::Svg Icon--></span>
                                <span class="fs-7 ps-1 fw-bolder">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    オブジェクト管理
                                    {% else %}
                                    Object Manager
                                    {% endif %}
                                </span>
                            </a>
                        </div>
                        </div>
                        
                        <select class="form-control select2-this min-h-40px" type="text" name="sub_view_type"
                            data-placeholder="{% if LANGUAGE_CODE == 'ja' %}タイプを選択{% else %}Select Type{% endif %}"
                            id="group-by"
                        >   
                            {% for prop in production_property_list %}
                                <option value="{{prop.id}}" {% if view_filter.sub_view_type == prop.id|to_str %}selected{% endif %}> {{prop.name}} </option>
                            {% endfor %}
                        </select>

                    </div>

                    {% if not download_view %}
                        {% if object_type|in_list:'purchaseorder,slips,task,estimates,delivery_slips,invoices,receipts,commerce_inventory_warehouse,expense,bill,journal,commerce_meter,workflow' %}
                            <div class="mb-4"
                                hx-get="{% host_url 'form_sets' host 'app' %}"
                                hx-vals='{
                                    "page_group_type": "{{object_type}}",
                                    "view_id":"{{view_filter.view.id}}"
                                }'
                                hx-trigger="load"
                                hx-swap="#property-sets-table"
                            >
                                <div id="property-sets-table"></div>
                            </div>
                        {% endif %}
                    {% endif %}

                    <div id="journal-column-field" class="d-none mt-4 min-w-200px">
                        <span class="py-0 mb-3 border-0 fs-5 fw-bolder text-active-primary ms-0">
                            {% if LANGUAGE_CODE == 'ja'%}
                            勘定項目
                            {% else %}
                            Account
                            {% endif %}
                        </span>

                        <input class="form-control" type="text" name="sub_view_type_account"
                            placeholder="{% if LANGUAGE_CODE == 'ja' %}勘定項目{% else %}Account{% endif %}"
                            id="count_category"
                        />

                    </div>
                    {% if object_type == constant.TYPE_OBJECT_JOURNAL %}
                    <div id="journal-balance-sheet-selector" class="{% if view_filter.view_type == 'balance_sheet' %}{% else %}d-none{% endif %} mt-4 min-w-200px">
                        <span class="py-0 mb-3 border-0 fs-5 fw-bolder text-active-primary ms-0">
                            {% if LANGUAGE_CODE == 'ja'%}
                            バランスシートの種類
                            {% else %}
                            Balance Sheet Type
                            {% endif %}
                        </span>

                        <select class="form-control select2-this min-h-40px" type="text" name="sub_view_type"
                            data-placeholder="{% if LANGUAGE_CODE == 'ja' %}タイプを選択{% else %}Select Type{% endif %}"
                            id="balance-sheet-type" onchange="bs_type(this)"
                        >   <option></option>
                            <option value="bs_snapshot" {% if view_filter.sub_view_type == 'bs_snapshot' %}selected{% endif %}> {% if LANGUAGE_CODE == 'ja' %}スナップショット{% else %}Snapshot{% endif %} </option>
                            <option value="bs_this_year" {% if view_filter.sub_view_type == 'bs_this_year' %}selected{% endif %}> {% if LANGUAGE_CODE == 'ja' %}今年 (タイムライン){% else %}This Year (Timeline){% endif %} </option>
                            <option value="bs_last_year" {% if view_filter.sub_view_type == 'bs_last_year' %}selected{% endif %}> {% if LANGUAGE_CODE == 'ja' %}去年 (タイムライン){% else %}Last Year (Timeline){% endif %} </option>
                            <option value="bs_custom_date"
                                {% if view_filter.view_type == 'balance_sheet'%}
                                    {% if view_filter.sub_view_type and ' - ' in view_filter.sub_view_type %}selected{% endif %}
                                {% endif %}
                            > {% if LANGUAGE_CODE == 'ja' %}カスタム日付範囲{% else %}Custom Date Range{% endif %} </option>
                        </select>

                    </div>

                    <div id="journal-balance-sheet-custom-date-range" class="{% if view_filter.view_type == 'balance_sheet' and ' - ' in view_filter.sub_view_type %}{% else %}d-none{% endif %} mt-4 min-w-200px">
                        <span class="py-0 mb-3 border-0 fs-5 fw-bolder text-active-primary ms-0">
                            {% if LANGUAGE_CODE == 'ja'%}
                            カスタム日付範囲
                            {% else %}
                            Custom Date Range
                            {% endif %}
                        </span>

                        <input class="form-control form_date" type="text" name="sub_view_type_date"
                            placeholder="{% if LANGUAGE_CODE == 'ja' %}日付範囲を選択{% else %}Select Date Range{% endif %}"
                            id="balance-sheet-custom-date-range" value="{{view_filter.sub_view_type|convert_date_range_by_lang:LANGUAGE_CODE}}"
                        />

                    </div>

                    <div id="journal-payable-receivable-date-range" class="{% if view_filter.view_type == 'payable_list' or view_filter.view_type == 'receivable_list' %}{% else %}d-none{% endif %} mt-4 min-w-200px">
                        <span class="py-0 mb-3 border-0 fs-5 fw-bolder text-active-primary ms-0">
                            {% if LANGUAGE_CODE == 'ja'%}
                            日付範囲
                            {% else %}
                            Date Range
                            {% endif %}
                        </span>

                        {% with view_filter.sub_view_type|string_dict_to_dict as sub_view_type %}
                        <input class="form-control form_date" type="text" name="sub_view_type_date_payable_receivable"
                            placeholder="{% if LANGUAGE_CODE == 'ja' %}日付範囲を選択{% else %}Select Date Range{% endif %}"
                            id="payable-receiveable-custom-date-range"
                            {% if sub_view_type and sub_view_type.date_range %}
                                value="{{sub_view_type.date_range|convert_date_range_by_lang:LANGUAGE_CODE}}"
                            {% endif %}
                        />
                        {% endwith %}
                    </div>
                    {% endif %}

                    <div id="column-field" class="mt-4 min-w-200px">
                        <span class="py-0 mb-3 border-0 fs-5 fw-bolder text-active-primary ms-0">
                            {% if LANGUAGE_CODE == 'ja'%}
                            カラム
                            {% else %}
                            Columns 
                            {% endif %}
                        </span>

                        <input class="form-control" type="text" name="column"
                            placeholder="{% if LANGUAGE_CODE == 'ja' %}ビュー{% else %}View{% endif %}"
                            id="column"
                        />

                    </div>



                    {% comment %} {% if object_type|in_list:'purchaseorder' %} {% endcomment %}
                    <div class="mt-4 min-w-200px">
                        <span class="py-0 mb-3 border-0 fs-5 fw-bolder text-active-primary ms-0">
                            {% if LANGUAGE_CODE == 'ja'%}
                            レコード表示数
                            {% else %}
                            Number of Display Records
                            {% endif %}
                        </span>

                        <select class="bg-white border min-h-40px form-select form-select-solid select2-this" data-control="select2" name="pagination" placeholder="{% if LANGUAGE_CODE == 'ja' %}ページネーション{% else %}Pagination{% endif %}">

                            <option value="25" {% if view_filter.pagination == 25 %} selected {% endif %} > 25 </option>
                            <option value="50" {% if view_filter.pagination == 50 %} selected {% endif %} > 50 </option>
                            <option value="100" {% if view_filter.pagination == 100 %} selected {% endif %} > 100 </option>


                        </select>

                    </div>
                    {% comment %} {% endif %} {% endcomment %}
                    {% if object_type|in_list:'task,purchaseorder,campaigns,slips,bill,journal,estimates,delivery_slips,invoices,receipts,expense' %}

                    <div id="select-sort" class="mt-5">
                        <span class="py-0 mb-3 border-0 fs-5 fw-bolder text-active-primary ms-0">
                            {% if LANGUAGE_CODE == 'ja'%}
                            並び替え
                            {% else %}
                            Sort By
                            {% endif %}
                        </span>

                        <select
                            name="order-by"
                            class="bg-white border min-h-40px form-select form-select-solid select2-this"
                            data-control="select2"
                            data-allow-clear="true"
                            data-placeholder="{% if LANGUAGE_CODE == 'ja' %}並び替え{% else %}Order By{% endif %}"
                            onchange="toggleSortMethodVisibility(this)"
                            >
                            <option value=""></option>
                            {% if object_type == 'campaigns' %}
                                {% comment %} Empty Because Campaigns doesn't have id {% endcomment %}
                            {% elif object_type == 'purchaseorder' %}
                                <option value="id_po" {% if view_filter.sort_order_by == 'id_po' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja' %}発注ID{% else %}Purchase Order ID{% endif %}</option>
                            {% elif object_type == 'billing' %}
                                <option value="id_bill" {% if view_filter.sort_order_by == 'id_bill' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja' %}請求書ID{% else %}Bill ID{% endif %}</option>
                            {% elif object_type == 'journal' %}
                                <option value="id_journal" {% if view_filter.sort_order_by == 'id_journal' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja' %}アカウント取引ID{% else %}Account Trans ID{% endif %}</option>
                            {% elif object_type == 'estimates' %}
                                <option value="id_est" {% if view_filter.sort_order_by == 'id_est' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja' %}見積ID{% else %}Estimates ID{% endif %}</option>
                            {% elif object_type == 'delivery_slips' %}
                                <option value="id_ds" {% if view_filter.sort_order_by == 'id_ds' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja' %}納品書ID{% else %}Delivery Slip ID{% endif %}</option>
                            {% elif object_type == 'invoices' %}
                                <option value="id_inv" {% if view_filter.sort_order_by == 'id_inv' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja' %}請求書ID{% else %}Invoice ID{% endif %}</option>
                            {% elif object_type == 'receipts' %}
                                <option value="id_rcp" {% if view_filter.sort_order_by == 'id_rcp' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja' %}領収書ID{% else %}Receipt ID{% endif %}</option>
                            {% elif object_type == 'task'%}
                                <option value="manual_sort" {% if view_filter.sort_order_by == 'manual_sort' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja' %}マニュアル{% else %}Manual{% endif %}</option>
                            {% endif %}

                            {% for column in columns %}
                                {% if not 'id' in column and id_field != column and column != 'usage_status' and column != 'line_item' and column != 'partner' %}
                                    {% if column|split:'|'|length > 1 and '_platform|' not in column %} {% comment %} Handle value such as customer|contact|name {% endcomment %}
                                   
                                        {% with column_display=column|split:'|'|search_custom_field_object_order_customer:request %}
                                            <option value="{{column}}" {% if view_filter.sort_order_by == column %}selected{% endif %}>{{column_display.name}}</option>
                                        {% endwith %}        
                                    
                                    {% else %}
                                        {% with args=column|add:'|'|add:object_type %}
                                            {% with column_display=args|get_column_display:request %}
                                                {% if column_display.type != 'production_line' %}
                                                    <option value="{{column_display.id|stringify}}" {% if view_filter.sort_order_by == column_display.id|stringify %}selected{% endif %}>{{column_display.name}}</option>
                                                {% endif %}
                                            {% endwith %}
                                        {% endwith %}
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                        </select>

                        <div class="mt-5" id="sort-method-section">
                            <span class="py-0 mb-3 border-0 fs-5 fw-bolder text-active-primary ms-0">
                                {% if LANGUAGE_CODE == 'ja'%}
                                並び替え方法
                                {% else %}
                                Sort Method
                                {% endif %}
                            </span>

                            <select
                                name="sort-method"
                                class="bg-white border min-h-40px form-select form-select-solid select2-this"
                                data-control="select2"
                                data-allow-clear="true"
                                data-placeholder="{% if LANGUAGE_CODE == 'ja' %}並び替え方法{% else %}Sort Method{% endif %}"
                                data-hide-search="true"
                                >
                                <option value=""></option>
                                <option value="asc" {% if view_filter.sort_order_method == 'asc' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja' %}昇順{% else %}Ascending{% endif %}</option>
                                <option value="desc" {% if view_filter.sort_order_method == 'desc' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja' %}降順{% else %}Descending{% endif %}</option>
                            </select>
                        </div>
                    </div>

                    {% endif%}


                    <div id="advanced-filter" class="mt-5">
                        {% comment %} Disable First since the Function not available yet {% endcomment %}
                        {% if object_type != constant.TYPE_OBJECT_USER_MANAGEMENT %}
                        <div class="mb-2 fs-4 justify-content-start fw-bolder">
                            <span>
                                {% if LANGUAGE_CODE == 'ja' %}フィルター{% else %}Filters{% endif %}
                            </span>

                            <select id="filter-select-manage" class="bg-white border min-h-40px form-select form-select-solid w-100"
                            data-control="select2"
                            hx-get="{% host_url 'commerce_view_setting' host 'app' %}"
                            hx-target="#data-selector-content-manage"
                            hx-trigger="filterChanged"
                            hx-vals='{"type":"advance-filter", "data_filter":"{{column}}", "object_type":"{{object_type}}"}'
                            hx-swap="beforeend"
                            >
                                <option value=''></option>
                                {% for column in columns %}
                                    {% if object_type == 'purchaseorder' and column == 'item_price' or object_type == 'purchaseorder' and column == 'item_price_without_tax' or column == 'usage_status' %}
                                    {% else %}
                                            {% if 'customer' in column and column|split:'|'|length > 1 %}
                                                {% with column_display=column|split:'|'|search_custom_field_object_order_customer:request %}
                                                    <option value="{{column}}">
                                                        {{column_display.name}}
                                                {% endwith %}  
                                            {% else %}
                                            {% with args=column|add:'|'|add:object_type %}
                                                {% with column_display=args|get_column_display:request %}
                                                    {% if column_display.type != 'production_line' %}
                                                    <option value="{{column}}">
                                                        {{column_display.name}}
                                                    {% endif %}
                                                {% endwith %}
                                            {% endwith %}
                                            {% endif %}
                                        </option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                        </div>
                        {% endif %}
                    </div>

                    <div id="data-selector-content-manage" ></div>
                    {% for predefined_data_filter in view_filter.filter_value %}
                    <div
                        hx-get="{% host_url 'commerce_view_setting' host 'app' %}"
                        hx-target="#data-selector-content-manage"
                        hx-trigger="load"
                        hx-vals='{"type":"advance-filter", "data_filter":"{{ predefined_data_filter|safe }}", "predefined_data_filter":"{{view_filter.filter_value|get_attr:predefined_data_filter}}", "object_type":"{{object_type}}"}'
                        hx-swap="beforeend"
                    >
                    </div>
                    {% endfor %}
                    {% if object_type == 'commerce_inventory_transaction'%}
                    <div id="select-item-stock-ledger" class="mt-5 d-none">
                        <div class="mb-2 fs-4 justify-content-start fw-bolder">
                            <div class=>
                                {% if LANGUAGE_CODE == 'ja' %}商品{% else %}Item{% endif %}
                            </div>
                            <select id="inventory-item" class="bg-white border min-h-40px form-select form-select-solid w-100 select2-this"  data-control="select2" name="inventory_item" placeholder="{% if LANGUAGE_CODE == 'ja' %}商品{% else %}Item{% endif %}">
                                {% for item in iventory_items%}
                                    <option value="{{item.id}}" {% if item_id == item.id|to_str %} selected {%endif%}> {% get_object_display item 'commerce_items' %} </option>
                                {% endfor%}
                            </select>
                        </div>
                        <div class="pt-2 mb-2 fs-4 justify-content-start fw-bolder">
                            <div class=>
                                {% if LANGUAGE_CODE == 'ja' %}日付範囲{% else %}Date Range{% endif %}
                            </div>
                            <div class="me-2">
                                <div class="max-w-150px">
                                <input {% if view_filter.view_type == 'stock_ledger' or view_filter.view_type == 'fifo_ledger' %}required{% endif %} id="transaction-date-range" class="form-control d-flex" name="transaction_date_range"
                                        {% if transaction_date_range%}
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            value="{% local_time_range  transaction_date_range workspace.timezone '%Y年%m月%d日' %}"
                                            {% else %}
                                            value="{% local_time_range  transaction_date_range workspace.timezone '%Y-%m-%d' %}"
                                            {% endif %}
                                        {%endif%}
                                    />
                                </div>
                            </div>
                            <script>
                                $('#transaction-date-range').attr('autocomplete', 'off');
                                $('#transaction-date-range').daterangepicker({
                                    singleDatePicker: false, // Enable range selection

                                    {% if data_type == "date" %}
                                    timePicker: false, // Disable time picker for date only
                                    {% elif data_type == "date_time" %}
                                    timePicker: true, // Enable time picker for date_time
                                    {% endif %}

                                    autoUpdateInput: false,
                                    showDropdowns: true,
                                    drops: "auto",
                                    locale: {
                                        {% if LANGUAGE_CODE == 'ja' %}
                                            cancelLabel: 'クリア',
                                            format: 'YYYY年MM月DD日 HH:mm', // Japanese date format
                                            separator: ' 〜 ',
                                            applyLabel: '選択',
                                            cancelLabel: 'キャンセル',
                                            fromLabel: 'From',
                                            toLabel: 'To',
                                            customRangeLabel: 'カスタム範囲',
                                            daysOfWeek: ['日', '月', '火', '水', '木', '金', '土'],
                                            monthNames: [
                                                '1月', '2月', '3月', '4月', '5月', '6月',
                                                '7月', '8月', '9月', '10月', '11月', '12月'
                                            ],
                                        {% else %}
                                            format: "Y-M-DD HH:mm",
                                        {% endif %}
                                        separator: " - ", // Define the separator for the range
                                    }
                                });

                                $('#transaction-date-range').on('apply.daterangepicker', function(ev, picker) {
                                    {% if LANGUAGE_CODE == 'ja' %}
                                        $(this).val(picker.startDate.format('YYYY年MM月DD日') + ' - ' + picker.endDate.format('YYYY年MM月DD日'));
                                    {% else %}
                                        $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
                                    {% endif %}
                                });

                                $('#transaction-date-range').on('cancel.daterangepicker', function(ev, picker) {
                                    $(this).val('');
                                });
                            </script>

                        </div>
                    </div>
                    {% endif %}

                    <div class="mb-8"></div>

                    {% if download_view %}
                        {% if object_type == constant.TYPE_OBJECT_EXPENSE or object_type == constant.TYPE_OBJECT_BILL or object_type == constant.TYPE_OBJECT_INVOICE or object_type == constant.TYPE_OBJECT_ESTIMATE or object_type == constant.TYPE_OBJECT_DELIVERY_NOTE or object_type == "receipts" or object_type == constant.TYPE_OBJECT_SLIP %}
                            <button name="download_zip" type="submit" class="btn btn-dark">
                                {% if object_type == constant.TYPE_OBJECT_EXPENSE %}
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    経費ファイルをダウンロード
                                    {% else %}
                                    Download Expenses Files
                                    {% endif %}
                                {% elif object_type == "billing" %}
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    請求ファイルをダウンロード
                                    {% else %}
                                    Download Billing Files
                                    {% endif %}
                                {% else %}
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    PDFをダウンロード
                                    {% else %}
                                    Download PDF Files
                                    {% endif %}
                                {% endif %}
                            </button>
                            <button name="download_csv" type="submit" class="btn btn-dark">
                                {% if LANGUAGE_CODE == 'ja'%}
                                CSVをダウンロード
                                {% else %}
                                Download CSV File
                                {% endif %}
                            </button>
                        {% else %}
                            <button name="download" type="submit" class="btn btn-dark">
                                {% if LANGUAGE_CODE == 'ja'%}
                                ダウンロード
                                {% else %}
                                Download
                                {% endif %}
                            </button>
                        {% endif %}
                    {% else %}

                        <div class="mb-8 form-check form-switch form-check-custom form-check-solid ">
                            <input
                                id="set-private-view" 
                                name="set-private-view" 
                                class="form-check-input" 
                                type="checkbox"
                                {% if view_filter %}
                                    {% if view_filter.view.is_private %}
                                        checked
                                    {% else %}
                                        disabled
                                    {% endif %}
                                {% endif %}
                            >
                            <label 
                                class="form-check-label fw-semibold text-gray-700 ms-3" 
                                for="set-private-view" 
                                data-bs-toggle="tooltip" 
                                data-bs-placement="top" 
                                title="{% if LANGUAGE_CODE == 'ja' %}公開ビューからプライベートビューへの変更はできません。{% else %}Converting a public view to a private view is not allowed.{% endif %}">
                                {% if LANGUAGE_CODE == 'ja' %}
                                プライベートビュー
                                {% else %}
                                Private View
                                {% endif %}
                            </label>
                        </div>
                        
                        <button name="update-view-button" type="submit" class="btn btn-dark w-100">
                            {% if LANGUAGE_CODE == 'ja'%}
                            更新
                            {% else %}
                            Update
                            {% endif %}
                        </button>
                    {% endif %}
                </form>

                {% if view and not download_view %}

                    {% if view.title and view.title != "main" %}
                    <div class="mt-4">
                        <div class="fv-rowd-flex flex-column">
                            <form method="POST" action="{% host_url 'commerce_view_setting' host 'app' %}">
                                {% csrf_token %}
                                {% if module %}
                                    <input type="hidden" name="module" value="{{module}}" />
                                {% endif %}
                                <input type="hidden" name="view_id" {% if view.id %} value="{{view.id}}" {% endif %} />
                                <input hidden name="object_type" value="{{object_type}}" />

                                <div class="border-0">
                                    <button type="submit" name="delete-view" class="btn btn-danger">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        削除
                                        {% else %}
                                        Delete
                                        {% endif %}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    {% endif %}

                {% endif %}

            </div>

        </div>

    </div>
</div>




<script>
    $('.select2-this').select2();
    $('#filter-select-manage').select2({
        placeholder: "{% if LANGUAGE_CODE == 'ja' %}フィルターを選択{% else %}Select Filters{% endif %}"
    })
    
    $('#filter-select-manage').on('select2:select', function (e) {
        var selectElement = $(this).closest('select').get(0);
        var currentHxVals = this.getAttribute('hx-vals');
        var currentHxValsObject = JSON.parse(currentHxVals);
        currentHxValsObject.data_filter = this.value
        this.setAttribute('hx-vals', JSON.stringify(currentHxValsObject));
        selectElement && selectElement.dispatchEvent(new Event('filterChanged'));

        this.value = '';
        $('#filter-select-manage').val(null).trigger('change');
    });
    $('#sort-select-manage').select2({
        placeholder: "{% if LANGUAGE_CODE == 'ja' %}並べ替えを選択{% else %}Select Sorts{% endif %}"
    })

    function onChangeSortSelect(element) {
        var currentHxVals = element.getAttribute('hx-vals');
        var currentHxValsObject = JSON.parse(currentHxVals);
        currentHxValsObject.data_sort = element.value
        element.setAttribute('hx-vals', JSON.stringify(currentHxValsObject));
        htmx.trigger('#sort-select-manage', 'sortChanged');

        element.value = ''
        $('#sort-select-manage').select2({
            placeholder: "{% if LANGUAGE_CODE == 'ja' %}並べ替えを選択{% else %}Select Sorts{% endif %}"
        })
    }

    // Columns Tagify
    var input_item_elem = document.querySelector("#column");
    var tagify = new Tagify(input_item_elem, {
        whitelist: [
        {% for column in columns %}
            {% if column|split:'|'|length > 1 and '_platform|' not in column %}
                {% with column_display=column|split:'|'|search_custom_field_object_order_customer:request %}
                    { "value": "{{column_display.name}}", "id": "{{column_display.id}}" },
                {% endwith %}
            {% elif not 'id' in column and id_field != column and not column == 'usage_status' or 'id' in column and '_platform|' in column %}
                {% with args=column|add:'|'|add:object_type %}
                    {% with column_display=args|get_column_display:request %}
                    {% if column_display.type != 'production_line' %}
                        { "value": "{{column_display.name}}", "id": "{{column_display.id}}" },
                    {% endif %}
                    {% endwith %}
                {% endwith %}
            {% endif %}
        {% endfor %}
        ],
        maxTags: {{ columns|safe|length }},
        searchKeys: ['value'],
        enforceWhitelist: true,
        dropdown: {
            maxItems: {{ columns|safe|length }},           // <- mixumum allowed rendered suggestions
            classname: "tagify__inline__suggestions", // <- custom classname for this dropdown, so it could be targeted
            enabled: 0,             // <- show suggestions on focus
            closeOnSelect: false    // <- do not hide the suggestions dropdown once an item has been selected
        },
        originalInputValueFormat: valuesArr => valuesArr.map(item => item.id).join(','), // Include item.id in the submitted values
    });

    {% with value_map_label='journal|category'|get_custom_property_object_by_request_list:request|get_attr:'value'|string_list_to_list%}
        // Columns Category
        var input_item_elem_ = document.querySelector("#count_category");
        var tagify_ = new Tagify(input_item_elem_, {
            whitelist: [
                {% for main_category in value_map_label %}
                    {% for child_category in main_category.choices %}
                        { "value": "{{child_category|get_journal_category_property_translated:request}}", "id": "{{child_category.value}}" },
                    {% endfor %}
                {% endfor %}
            ],
            maxTags: {{ value_map_label|safe|length }},
            searchKeys: ['value'],
            enforceWhitelist: true,
            dropdown: {
                maxItems: {{ value_map_label|safe|length }},           // <- mixumum allowed rendered suggestions
                classname: "tagify__inline__suggestions", // <- custom classname for this dropdown, so it could be targeted
                enabled: 0,             // <- show suggestions on focus
                closeOnSelect: false    // <- do not hide the suggestions dropdown once an item has been selected
            },
            originalInputValueFormat: valuesArr => valuesArr.map(item => item.id).join(','), // Include item.id in the submitted values
        });

        //Add Tags Category
        {% if view_filter.view_type == 'payable_list' or view_filter.view_type == 'receivable_list' %}
            {% with view_filter.sub_view_type|string_dict_to_dict as sub_view_type %}
                {% if sub_view_type.account_type %}
                    {% for column in sub_view_type.account_type %}
                        {% for main_category in value_map_label %}
                            {% for child_category in main_category.choices %}
                                {% if child_category.value == column %}
                                    tagify_.addTags([{ value: "{{child_category|get_journal_category_property_translated:request}}", id: "{{column}}" }]);
                                {% endif %}
                            {% endfor %}
                        {% endfor %}
                    {% endfor %}
                {% else %}
                    {% for column in view_filter.sub_view_type|safe|string_list_to_list %}
                        {% for main_category in value_map_label %}
                            {% for child_category in main_category.choices %}
                                {% if child_category.value == column %}
                                    tagify_.addTags([{ value: "{{child_category|get_journal_category_property_translated:request}}", id: "{{column}}" }]);
                                {% endif %}
                            {% endfor %}
                        {% endfor %}
                    {% endfor %}
                {% endif %}
            {% endwith %}
        {% endif %}

    {% endwith %}

    {% if view_filter %}
        {% for column in view_filter.column|safe|string_list_to_list %}
            {% if column|split:'|'|length > 1 and '_platform|' not in column %}
                {% with column_display=column|split:'|'|search_custom_field_object_order_customer:request %}
                tagify.addTags([{ value: "{{column_display.name}}", id: "{{column_display.id}}" }]);
                {% endwith %}
            {% elif not 'id' in column and id_field != column and column != 'checkbox' and column != 'usage_status' or 'id' in column and '_platform|' in column %}
                {% with args=column|add:'|'|add:object_type %}
                    {% with column_display=args|get_column_display:request %}
                        tagify.addTags([{ value: "{{column_display.name}}", id: "{{column_display.id}}" }]);
                    {% endwith %}
                {% endwith %}
            {% endif %}
        {% endfor %}

    {% else %}

        {% for column in default_columns|safe|string_list_to_list %}
            {% if not 'id' in column and id_field != column and not column == 'usage_status'  or 'id' in column and '_platform|' in column %}
                {% with args=column|add:'|'|add:object_type %}
                    {% if column|split:'|'|length > 1 and '_platform|' not in column %}
                        {% with column_display=column|split:'|'|search_custom_field_object_order_customer:request %}
                            tagify.addTags([{ value: "{{column_display.name}}", id: "{{column_display.id}}" }]);
                        {% endwith %}
                    {% else %}
                        {% with column_display=args|get_column_display:request %}
                            tagify.addTags([{ value: "{{column_display.name}}", id: "{{column_display.id}}" }]);
                        {% endwith %}
                    {% endif %}
                {% endwith %}
            {% endif %}
        {% endfor %}
    {% endif %}

    // using 3-party script "dragsort"
    // https://github.com/yairEO/dragsort
    var dragsort = new DragSort(tagify.DOM.scope, {
        selector:'.'+tagify.settings.classNames.tag,
        callbacks: {
            dragEnd: onDragEnd
        }
    })

    function onDragEnd(elm){
        tagify.updateValueByDOMTags()
    }


    $('.form_date').daterangepicker({
        singleDatePicker: false,
        drops: 'auto',
        autoUpdateInput: true,
        locale: {
            {% if LANGUAGE_CODE == 'ja' %}
            cancelLabel: 'クリア',
            format: 'YYYY年MM月DD日', // Japanese date format
            separator: ' 〜 ',
            applyLabel: '選択',
            cancelLabel: 'キャンセル',
            fromLabel: 'From',
            toLabel: 'To',
            customRangeLabel: 'カスタム範囲',
            daysOfWeek: ['日', '月', '火', '水', '木', '金', '土'],
            monthNames: [
                '1月', '2月', '3月', '4月', '5月', '6月',
                '7月', '8月', '9月', '10月', '11月', '12月'
            ],
            {% else %}
            format: "Y-M-DD",
            cancelLabel: 'Clear',
            {% endif %}
        }
    });
    {% if object_type == constant.TYPE_OBJECT_JOURNAL %}
    function bs_type(elm){
        var value = elm.value;

        if (value == 'bs_custom_date'){
            var journal_selector = document.getElementById('journal-balance-sheet-custom-date-range');
            journal_selector.classList.remove('d-none')
        }
        else{
            var journal_selector = document.getElementById('journal-balance-sheet-custom-date-range');
            journal_selector.classList.add('d-none')

        }
    }
    {% endif %}
    function select_onchange(elm){
        var value = elm.value;
        var status_selector = document.getElementById('status-selector');
        var item_selection = document.getElementById('select-item-stock-ledger');
        var transaction_date_range = document.getElementById('transaction-date-range');
        var advanced_filter = document.getElementById('advanced-filter');
        var view_page = document.getElementById('object_type');
        var column_field = document.getElementById('column-field');


        if (status_selector){
            if (value == 'kanban'){
                status_selector.classList.remove('d-none')
            }else{
                status_selector.classList.add('d-none')
            }
        }


        if (view_page && view_page.value == 'commerce_inventory_transaction'){
            if (value == 'stock_ledger' || value == 'fifo_ledger'){
                item_selection.classList.remove('d-none')
                transaction_date_range.setAttribute('required', '')
                column_field.classList.add('d-none')
                advanced_filter.classList.add('d-none')

            } else {
                item_selection.classList.add('d-none')
                transaction_date_range.removeAttribute('required')
                advanced_filter.classList.remove('d-none')
                column_field.classList.remove('d-none')

            }
        } else if (view_page.value == 'commerce_inventory' ) {
            var column_field = document.getElementById('column-field')
            if (value == 'forecast' ){
                column_field.classList.add('d-none')
            }else{
                column_field.classList.remove('d-none')
            }
        } else if (view_page.value == '{{constant.TYPE_OBJECT_TASK}}' ) {
            var column_field = document.getElementById('sub-view-type')
            if (value == 'gantt_chart_with_production_line' ){
                column_field.classList.remove('d-none')
            }else{
                column_field.classList.add('d-none')
            }

        } else if (view_page.value == 'journal' ) {
            var column_field = document.getElementById('column-field')
            if (value == 'journal' || value == 'profit_and_loss' || value == 'balance_sheet' || value == 'payable_list' || value == 'receivable_list'){
                column_field.classList.add('d-none')
            }
            else{
                column_field.classList.remove('d-none')
            }

            if (value == 'balance_sheet'){
                var journal_selector = document.getElementById('journal-balance-sheet-selector');
                journal_selector.classList.remove('d-none')
            }
            else{
                var journal_selector = document.getElementById('journal-balance-sheet-selector');
                journal_selector.classList.add('d-none')
            }

            {% with value_map_label='journal|category'|get_custom_property_object_by_request_list:request|get_attr:'value'|string_list_to_list%}
                if (value == 'payable_list' || value == 'receivable_list'){
                    var column_list_field = document.getElementById('journal-column-field');
                    column_list_field.classList.remove('d-none');
                    if (prev_view_field_val != value){
                        if (value == 'payable_list'){
                            tagify_.removeAllTags()
                            //Add Tags Category
                            {% for column in "['accounts_payable','accounts_payable_other']"|safe|string_list_to_list %}
                                {% for main_category in value_map_label %}
                                    {% for child_category in main_category.choices %}
                                        {% if child_category.value == column %}
                                            tagify_.addTags([{ value: "{{child_category|get_journal_category_property_translated:request}}", id: "{{column}}" }]);
                                        {% endif %}
                                    {% endfor %}
                                {% endfor %}
                            {% endfor %}
                            prev_view_field_val = value;
                        }
                        else if (value == 'receivable_list'){
                            tagify_.removeAllTags()
                            //Add Tags Category
                            {% for column in "['accounts_receivable','accounts_receivable_other']"|safe|string_list_to_list %}
                                {% for main_category in value_map_label %}
                                    {% for child_category in main_category.choices %}
                                        {% if child_category.value == column %}
                                            tagify_.addTags([{ value: "{{child_category|get_journal_category_property_translated:request}}", id: "{{column}}" }]);
                                        {% endif %}
                                    {% endfor %}
                                {% endfor %}
                            {% endfor %}
                            prev_view_field_val = value;
                        }
                    }
                }
            {% endwith %}
            else{
                var column_list_field = document.getElementById('journal-column-field');
                column_list_field.classList.add('d-none');
            }
        }


    }

    function delete_form(elm){
        elm.parentElement.parentElement.remove()
    }

    {% if object_type == 'campaigns' %}
    $('#view-field').select2().on('select2:select', function(){
        if (this.value == 'calendar') {
            document.getElementById('column-field').classList.add('d-none')
        } else {
            document.getElementById('column-field').classList.remove('d-none')
        }
    })
        {% if view_filter.view_type == 'calendar' %}
            document.getElementById('column-field').classList.add('d-none')
        {% endif %}
    {% endif %}

    var prev_view_field_val = document.getElementById('view-field').value;

    function toggleSortMethodVisibility(element) {
        var sortMethodSection = document.getElementById('sort-method-section');
        if (element.value === 'manual_sort') {
            sortMethodSection.classList.add('d-none');
        } else {
            sortMethodSection.classList.remove('d-none');
        }
    }
</script>
