{% extends 'base.html' %}
{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% block content %}

<style>
    .invoice-item:hover .invoice-selection{
        border-width: 1px !important;
    }
    
    .dataTables_scrollHead {
        position: sticky !important;
        z-index: 3;
        background-color: white;
    }
</style>

<div class="d-flex align-items-end mb-2 align-items-center" style="background-color: #FAFAFA !important;">
    {% include 'data/common/module-header-tabs.html' %}
    {% include 'data/common/view-menu-new-design.html' %}
</div>
{% if permission == 'hide' %}
    {% include 'data/static/no-access-page.html' %}
{% else %}
<div class="mt-0 mb-10 tw-pl-2">
    {% include 'data/common/permission-action-warning-message.html' %}
    {% include 'data/common/view-menu.html' %}
    <div id="data-table">
        {% include 'data/invoice/invoices-table.html' %}
    </div>
    
    <!-- Bulk Action Forms -->
    <form method="POST" action="{% host_url 'invoices' host 'app' %}">
        {% csrf_token %}
        <input name='flag_all' id='flag_all' class="flag_all flag_all_invoice" type="checkbox" hidden>
        {% if view_id %}
        <input type="hidden" value="{{view_id}}" name="view_id">
        {% endif %}
        
        <!-- Bulk Archive Modal -->
        <div class="modal fade" tabindex="-1" id="manage_delete_bulk">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header pb-0 border-0 justify-content-end">
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                        </div>
                    </div>
                    <div class="modal-body border-0 px-8 py-3 pb-5">
                        <div class="text-center pb-4 pb-sm-6">
                            <h4 class="fw-bolder text-danger">
                                {% if LANGUAGE_CODE == 'ja'%}
                                選択した請求書をアーカイブしますか？
                                {% else %}
                                Archive Selected Invoices?
                                {% endif %}
                            </h4>
                            <div class="text-muted">
                                {% if LANGUAGE_CODE == 'ja'%}
                                アーカイブした請求書は、アーカイブビューで確認できます。
                                {% else %}
                                Archived invoices can be viewed in the archived view.
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="modal-footer border-0">
                        <button name="bulk_delete_invoices" type="submit" class="btn btn-danger">
                            {% if LANGUAGE_CODE == 'ja'%}
                            アーカイブ
                            {% else %}
                            Archive
                            {% endif %}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bulk Permanent Delete Modal -->
        <div class="modal fade" tabindex="-1" id="manage_permanent_delete_bulk">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header pb-0 border-0 justify-content-end">
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                        </div>
                    </div>
                    <div class="modal-body border-0 px-8 py-3 pb-5">
                        <div class="text-center pb-4 pb-sm-6">
                            <h4 class="fw-bolder text-danger">
                                {% if LANGUAGE_CODE == 'ja'%}
                                選択した請求書を完全に削除しますか？
                                {% else %}
                                Permanently Delete Selected Invoices?
                                {% endif %}
                            </h4>
                            <div class="text-muted">
                                {% if LANGUAGE_CODE == 'ja'%}
                                この操作は元に戻すことができません。請求書と関連するすべてのデータが完全に削除されます。
                                {% else %}
                                This action cannot be undone. Invoices and all related data will be permanently deleted.
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer border-0">
                        <button name="bulk_permanent_delete_invoices" type="submit" class="btn btn-danger">
                            {% if LANGUAGE_CODE == 'ja'%}
                            削除
                            {% else %}
                            Delete
                            {% endif %}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bulk Restore Modal -->
        <div class="modal fade" tabindex="-1" id="manage_restore_bulk">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header pb-0 border-0 justify-content-end">
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                        </div>
                    </div>
                    <div class="modal-body border-0 px-8 py-3 pb-5">
                        <div class="text-center pb-4 pb-sm-6">
                            <h4 class="fw-bolder text-success">
                                {% if LANGUAGE_CODE == 'ja'%}
                                選択した請求書を有効化しますか？
                                {% else %}
                                Activate Selected Invoices?
                                {% endif %}
                            </h4>
                            <div class="text-muted">
                                {% if LANGUAGE_CODE == 'ja'%}
                                有効化された請求書は通常のビューに表示されます。
                                {% else %}
                                Activated invoices will appear in the normal view.
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="modal-footer border-0">
                        <button name="bulk_restore_invoices" type="submit" class="btn btn-success">
                            {% if LANGUAGE_CODE == 'ja'%}
                            有効化
                            {% else %}
                            Activate
                            {% endif %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
    
    <!-- Bulk Update Form (for bulk actions from commerce-view-menu) -->
    <form id="bulk-update" method="POST" action="{% host_url 'bulk_updates_objects' host 'app' %}" enctype="multipart/form-data">
        {% csrf_token %}
        <input hidden name='view_id' value='{{view_id}}'>
        <input hidden name='module' value='{{module}}'>
        <input hidden name='items_' id="selected_items_" value=''>
        <input hidden name='flag_all' id="flag_all_bulk_update" type="checkbox" />
        <input hidden name='object_type' id="object_type" value="{{object_type}}"/>
    </form>
    
    <!-- Bulk Action Buttons (similar to items.html pattern) -->
    {% if permission|check_permission:'archive' %}
    <div class="d-none" id="bulk-action-buttons" style="position: fixed; bottom: 20px; right: 20px; z-index: 1000;">
        <div class="d-flex flex-column">
            {% if request.GET.status == 'archived' %}
            {# Show only Activate button on archived page #}
            <button class="py-1 rounded-1 btn btn-sm btn-light-success fw-bold mt-2 mb-1 me-2" onclick="check_permission_action(event, 'archive', 'manage_restore_bulk')">
                {% if LANGUAGE_CODE == 'ja'%}
                有効化
                {% else %}
                Activate
                {% endif %}
            </button>
            {# Show Delete button instead of Archive on archived page #}
            <button class="btn btn-sm btn-light-danger py-1 rounded-1 fw-bold mt-2 mb-1 me-2" onclick="check_permission_action(event, 'archive', 'manage_permanent_delete_bulk')">
                {% if LANGUAGE_CODE == 'ja'%}
                削除
                {% else %}
                Delete
                {% endif %}
            </button>
            {% else %}
            {# Show only Archive button on normal page (hide Activate) #}
            <button class="btn btn-sm btn-light-danger py-1 rounded-1 fw-bold mt-2 mb-1 me-2" onclick="check_permission_action(event, 'archive', 'manage_delete_bulk')">
                {% if LANGUAGE_CODE == 'ja'%}
                アーカイブ
                {% else %}
                Archive
                {% endif %}
            </button>
            {% endif %}
        </div>
    </div>
    {% endif %}
    
    {% if contact_id %}
        <span class="d-none">
            {% with contact_id|get_contact_obj as contact %}
                {% include 'data/partials/partial-load-contact-company-drawer.html' with contact=contact open='open' %}
            {% endwith %}
        </span>
    {% elif company_id %}
        <span class="d-none">
            {% with company_id|get_company_obj as company %}
                {% include 'data/partials/partial-load-contact-company-drawer.html' with company=company open='open' %}
            {% endwith %}
        </span>
    {% endif %}
</div>
{% endif %}



{% endblock %}

{% block js%}
 
<script>
    function isNumberKey(evt) {
        var charCode = (evt.which) ? evt.which : evt.keyCode;
        var inputValue = evt.target.value;
      
        var hasPeriod = inputValue.indexOf('.') !== -1;
        var hasComma = inputValue.indexOf(',') !== -1;
      
        // Allow numeric keys (0-9), period (.) if not already present, and comma (,) if not already present
        if (
          (charCode >= 48 && charCode <= 57) ||  // 0-9
          (charCode === 46 && !hasComma && !hasPeriod) ||  // . (allow only if not already present and no comma present)
          (charCode === 44 && !hasPeriod && !hasComma)     // , (allow only if not already present and no period present)
        ) {
          return true;
        } else {
          return false;
        }
      }
      document.addEventListener("htmx:beforeRequest", function(event) {
        if (event.target.id === "billings_wizard_button") {
            document.getElementById("manage-full-drawer-content").innerHTML = ''
            // You can modify event properties or run any custom logic here
        }
    });
    
    function selectAllInView() {
        const totalItems = {{paginator.count}}
        document.getElementById('total-item-selected').innerText = totalItems;
        var items = document.getElementsByClassName('check_input')
        for (var i = 0; i < items.length; i++) {
            items[i].checked = true;
        }
        document.getElementById('flag_all').checked = true
        toggleBulkActionButtons(); // Show bulk action buttons when items are selected
    }
    
    // Function to show/hide bulk action buttons based on checkbox selection
    function toggleBulkActionButtons() {
        const checkedBoxes = document.querySelectorAll('.check_input:checked');
        const bulkActionButtons = document.getElementById('bulk-action-buttons');
        
        if (checkedBoxes.length > 0) {
            bulkActionButtons.classList.remove('d-none');
        } else {
            bulkActionButtons.classList.add('d-none');
        }
    }
    
    // Add event listeners to checkboxes to toggle bulk action buttons
    document.addEventListener('DOMContentLoaded', function() {
        // Listen for checkbox changes
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('check_input')) {
                toggleBulkActionButtons();
            }
        });
        
        // Initial check on page load
        toggleBulkActionButtons();
    });
    
    // Functions needed for bulk update form (from objects-bulk-edit-modal.html)
    function save_items(){
        var totals = check_inputs = document.getElementsByClassName('check_input')
        var checks = ''
        for (var i = 0; i < totals.length; i++) {
            if (totals[i].checked === true)
                {
                    if(checks){
                        checks += ';' + totals[i].value
                    }else{
                        checks = totals[i].value
                    }
                }
        }
        document.getElementById('selected_items_').value = checks;
    }
    
    // Function to handle the bulk update form submission for archive/active actions
    function submitBulkUpdateForm(bulkType) {
        // Create the form data
        var form = document.getElementById('bulk-update');
        var input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'bulk_type';
        input.value = bulkType;
        form.appendChild(input);
        
        // Set selected items
        save_items();
        
        // Check if we're selecting all items
        var flagAllCheckbox = document.getElementById('flag_all');
        if (flagAllCheckbox && flagAllCheckbox.checked) {
            document.getElementById('flag_all_bulk_update').checked = true;
        }
        
        // Submit the form
        form.submit();
    }
</script>

<script>
    // Keyboard shortcut for opening the 'Create New' drawer (n key)
    document.addEventListener('keydown', function(event) {
    // Ignore if input, textarea, or contenteditable is focused
    const active = document.activeElement;
    if (active && (active.tagName === 'INPUT' || active.tagName === 'TEXTAREA' || active.isContentEditable)) {
        return;
    }

    // Check if the key pressed is 'n'
    if (event.key.toLowerCase() === 'n') {
        event.preventDefault(); // Prevent default 'n' behavior
        
        // Find the 'Create New' button
        const newButton = document.querySelector('.view_form_trigger'); 
        
        if (newButton) {
            newButton.click(); // Simulate click to open the drawer
        }
    }
    });
</script>


<script>
    function check_permission_action(event, permission_type, ...args){

        let source = args.length > 0 ? args[0] : null;
        
        const checkInputs = document.querySelectorAll('.check_input:checked');

        console.log(checkInputs)
        console.log(permission_type)
        let members = "{{group_members}}"
        members = members.split(',')
        const user_id = '{{request.user.id}}'
        const permission = '{{permission}}';
        const permission_list = permission.split('|');
        let scope = ''
        permission_list.forEach(p => {
            if (p.includes(permission_type)) {
                p_split = p.split('_');
                scope = p_split[0]
            }
        })

        let msg = '';
        let denied = false;
        for (let i = 0; i < checkInputs.length; i++) {
            var owner_id = checkInputs[i].dataset.owner;
            if (owner_id){
                if (scope == 'user'){
                    if (owner_id.toString() !== user_id.toString()) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分のアイテムのみ編集または削除することができます。";
                        {% else %}
                        msg = "Action denied. You are only allowed to edit or delete your own items.";
                        {% endif %}                    
                        checkInputs[i].click()
                        denied = true;
                        console.log(user_id)
                        console.log(owner_id)
                    }
                } else if (scope == 'team'){
                    if (!members.includes(owner_id.toString())) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分または自分のチームに割り当てられたアイテムのみ編集または削除できます。";
                        {% else %}
                        msg = "Action denied. You can only edit or delete items assigned to you or your team.";
                        {% endif %}
                        checkInputs[i].click()
                        denied = true;
                    }
                } 
            }
        }
        if (denied) {
            event.preventDefault();
            event.stopImmediatePropagation();
            document.getElementById('permissionActionWarning').innerHTML = msg;
            setTimeout(() => {
                document.getElementById('permissionActionWarning').innerHTML = '';
            }, 4000);
            msg = ''
        } else if (source){
            const modalEl = document.getElementById(source);
            const modal = bootstrap.Modal.getOrCreateInstance(modalEl);
            modal.show();
        }
        
    }
</script>


{% endblock %}