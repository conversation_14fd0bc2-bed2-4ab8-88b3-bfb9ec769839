
{% extends 'base.html' %}
{% load tz %}
{% load custom_tags %}
{% load i18n %}
{% load hosts %}
{% load humanize %}
{% get_current_language as LANGUAGE_CODE %}
{% block content %}

<style>
    .message_thread:focus {
        background-color:#f1ede5;
    }

    .profile-img-col {
        width:30px !important;
        max-width:30px !important;
        padding: 0px;
        padding-left: 4px;
    }

    .profile-img-container {
        height: 25px;
        width: 25px;
        border-radius: 15px;
        margin-right: 5px;
        margin-bottom: 2px;
        display: inline-flex;
        justify-content:center;
        align-items:center;
        overflow: hidden;
    }

    .profile-img {
        width:100%;
        height:auto;
    }

    .search-wrapper {
        display: flex;
        align-items: center;
        position: relative;
        width: 24px;
        transition: width 0.4s ease-in-out, margin-right 0.4s ease-in-out, opacity 0.4s ease-in-out;
    }

    .search-wrapper.expanded {
        width: 200px; /* New width when expanded */
        margin-right: -0.5rem !important;
    }

    .search-wrapper input {
        display: none;
        width: 0;
        padding: 0;
        opacity: 0;
        transition: width 0.4s ease-in-out, margin-right 0.4s ease-in-out, opacity 0.4s ease-in-out;
    }

    .search-wrapper.expanded input {
        display: block;
        width: 100%;
        opacity: 1;
    }
    

    .search-icon-view {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
    }

    /* Tooltip container */
    .hover-tooltip {
        position: relative;
        display: inline-block;
    }

    /* Tooltip text */
    .hover-tooltip .hover-tooltip-text {
        visibility: hidden;
        width: 80px;
        background-color: #555;
        color: #fff;
        text-align: center;
        padding: 3px 0;
        border-radius: 8px;

        /* Position the hover-tooltip text */
        position: absolute;
        z-index: 1;
        top: 50%;
        right: 105%;
        transform: translateY(-50%);

        /* Fade in hover-tooltip */
        opacity: 0;
        transition: opacity 0.5s;
    }

    /* Show the hover-tooltip text when you mouse over the hover-tooltip container */
    .hover-tooltip:hover .hover-tooltip-text {
        visibility: visible;
        opacity: 0.9;
    }
</style>
{% if page_type == 'all'  %}
<div class="d-flex d-flex align-items-center" style="background-color: #FAFAFA !important;">
    {% include 'data/common/module-header-tabs.html' %}

    <div class="w-50 d-flex justify-content-end tw-mr-5" id="conversation-view-container">
        <div class="{% include "data/utility/table-button.html" %}">
            <button id='view-sync-items' type="button" class="max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 btn py-1 rounded-1 tw-bg-gray-200 hover:tw-bg-gray-300 justify-content-center px-1 conversation-sync-wizard-button"
                style="width: 130px"
                hx-get="{% host_url 'sync_conversation_drawer' host 'app' %}?menu_key={{menu_key}}"
                hx-target="#conversation-sync-drawer-content"
                hx-swap="innerHTML">
                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-download" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                        <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708z"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    インポート
                    {% else %}
                    Import
                    {% endif %}
                </span>
            </button>
        </div>
        <div class="{% include "data/utility/table-button.html" %}">
            <button class="w-100px align-items-center d-flex btn btn-primary btn-md py-1 rounded-1 create-message-button" type="button"
                hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                hx-vals='{"drawer_type":"create-message"}'
                hx-target="#inbox-drawer-content"
                hx-trigger="click"
                style="height: 32px;">
                <span class="svg-icon svg-icon-4">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"/>
                        <rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"/>
                        <rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"/>
                    </svg>
                </span>
        
                <span class="fs-7 ps-1 fw-bolder w-85px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    新規
                    {% else %}
                    New
                    {% endif %}
                </span>
            </button> 
        </div>
    </div>
</div>
<div class="w-100 tw-pl-2">
    <div class="d-flex flex-column flex-lg-row">
        <div class="w-100">
            <div class="mb-5">
                {% comment %} Views {% endcomment %}
                <div class="{% include "data/utility/tab-pane.html" %}" role="tabpanel" style="z-index:4 !important; top: 4%!important">
                    <div class="{% include "data/utility/table-nav.html" %}">
                        <div class="{% include "data/utility/table-content-2.html" %}" id="conversation-view-container-1">
                            <div class="w-100 d-flex align-items-center">
                                <div class="{% include "data/utility/view-menu-nav-item.html" %}">
                                    <a class="{% include "data/utility/view-menu-default.html" %}" 
                                        type="button"
                                        href = "{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">
                                        {% if LANGUAGE_CODE == 'ja'%}ビュー{% else %}View{% endif %}

                                    </a>
                                    {% if not view_id %}
                                    <div class="w-20px nav-item justify-content-center d-flex fs-6 text-gray-900">
                                        <button type="button" class="{% include "data/utility/view-plus-link.html" %} create-view-settings-button"
                                            hx-vals='{"module": "{{menu_key}}", "page":"conversation","type":"update_view"}'
                                            hx-get="{% url 'conversation_view_drawer' %}" 
                                            hx-target="#manage-view-settings-drawer"
                                            hx-trigger="click"
                                            hx-swap="innerHTML">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                                <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                            </svg>
                                        </button>
                                    </div>
                                    {% endif %}
                                </div>

                                {% comment %} Put Items here {% endcomment %}
                                {% include 'data/projects/partial-view-menu.html' %}

                                <div class="nav-item fs-6 text-gray-900">
                                    <button type="button" class="{% include "data/utility/view-plus-link.html" %} create-view-settings-button" 
                                        hx-vals='{"page": "conversation","type":"create_view"}'
                                        hx-get="{% url 'conversation_view_drawer' %}" 
                                        hx-target="#manage-view-settings-drawer"
                                        hx-trigger="click"
                                        hx-swap="innerHTML">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                            <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            

                            <div class="d-flex me-2 ms-5">
                                <div class="mb-2 search-wrapper {% if search_q %}expanded {% endif %} hover-tooltip">
                                    <span class="search-wrapper-tooltip hover-tooltip-text">
                                        {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}
                                    </span>
                                    <div class="d-flex align-items-center">
                                        <form id="filter-form-search" method="get" class="w-100">
                                            <div class="d-flex mb-0 position-relative align-items-center" style="height: 26px;">
                                                <span class="svg-icon svg-icon-3 search-icon-view" onclick="toggleSearch()">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                        <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                                                        <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                                                    </svg>
                                                </span>
                                                <input
                                                    id="base-search-input" type="text" name="q" class="form-control bg-white ps-12"
                                                    value="{% if search_q %}{{ search_q }}{% else %}""{% endif %}"
                                                    placeholder="{% if LANGUAGE_CODE == 'ja' %}メッセージ検索{% else %}Search Message{% endif %}"
                                                    onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                                                >
                                                <input type="hidden" name="page_type" value="all">
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="thread-bulk-action-container" class="d-none">
                            <span class="me-10">
                                <button class="py-1 rounded-1 btn btn-sm btn-light fw-bold mt-2 mb-1" onclick="selectAllThreads()">
                                    <span>
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        すべて選択
                                        {% else %}
                                        Select All
                                        {% endif %}
                                    </span>
                                </button>
                                <button class="py-1 rounded-1 btn btn-sm btn-light fw-bold mt-2 mb-1" onclick="deselectAllThreads()">
                                    <span>
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        選択を解除
                                        {% else %}
                                        Deselect All
                                        {% endif %}
                                    </span>
                                </button>
                
                                <script>
                                    function selectAllThreads() {
                                        selectTaskInputs = document.getElementsByClassName('thread-selection')
                                        for (var i = 0; i < selectTaskInputs.length; i++) {
                                            selectTaskInputs[i].checked = true;
                                        }
                                    }
                                    function deselectAllThreads() {
                                        selectTaskInputs = document.getElementsByClassName('thread-selection')
                                        for (var i = 0; i < selectTaskInputs.length; i++) {
                                            selectTaskInputs[i].checked = false;
                                        }
                                        document.getElementById('thread-bulk-action-container').classList.add('d-none')
                                        document.getElementById('conversation-view-container').classList.remove('d-none')
                                        document.getElementById('conversation-view-container-1').classList.remove('d-none')
                                    }
                                </script>
                            </span>
                            <button class="py-1 rounded-1 btn btn-sm btn-light-success fw-bold mt-2 mb-1" data-bs-toggle="modal" data-bs-target="#manage_restore_bulk">
                                {% if LANGUAGE_CODE == 'ja'%}
                                有効化
                                {% else %}
                                Activate
                                {% endif %}
                            </button>
                            <button class="btn btn-sm btn-light-danger py-1 rounded-1 fw-bold mt-2 mb-1" data-bs-toggle="modal" data-bs-target="#manage_delete_bulk">
                                {% if LANGUAGE_CODE == 'ja'%}
                                アーカイブ
                                {% else %}
                                Archive 
                                {% endif %}
                            </button>
                            <script>
                                document.body.addEventListener("hideTaskBulkActionBtn", function(evt){
                                    document.getElementById('task-bulk-action-container').classList.add('d-none')
                                    document.getElementById('task-view-contianer').classList.remove('d-none')
                                })
                            </script>
                        </div>
                    </div>
                </div>
                {% comment %} End of Views {% endcomment %}
            </div>

            <form method="POST" action="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}" id="contacts-form">
                {% csrf_token %}
                {% if messages %}
                <div class="p-5">
                    {% for message in messages %}
                        <div id='message-panel' class="bg-{{ message.tags }} {% if message.tags == 'error' %}bg-danger{% endif %}  w-100 px-10 py-3 rounded text-white d-flex align-items-center justify-content-between mb-5">
                            {{message}}
                            <button id="close-message" type="button" class="btn btn-sm btn-icon explore-btn-dismiss me-n5">
                                <span class="svg-icon svg-icon-2 svg-icon-white">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="black"></rect>
                                        <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
                                    </svg>
                                </span>
                            </button>
                        </div>
                    {% endfor %}
                </div>
                {% endif %}
        
                <div class="" >
                    <div class="border rounded-2">
                        
                        <div class='d-flex p-0 h-100'>
        
                            <div id="message-thread" class='w-100 h-100 py-5'> 
        
                                <div class="h-100 w-100" style="border-right:1px solid #eee !important" >
                                    <div class="d-flex flex-column position-relative border-0 rounded-0  h-75">
                                        <div class="h-100">   
                                            {% if message_threads %}
                                                {% for message_thread in message_threads %}
                                                <div class="d-flex">
                                                {% comment %} Checkbox {% endcomment %}
                                                    <div class="text-nowrap p-4">
                                                        <input style="" id="thread-selection-{{message_thread.id}}" class="form-check-input cursor-pointer thread-selection" type="checkbox" name="checkbox" value="{{message_thread.id}}"/>
                                                        <script>
                                                            checkbox = document.getElementById('thread-selection-{{message_thread.id}}')
                                                            checkbox.addEventListener('change', function() {
                                                                if (this.checked) {
                                                                    document.getElementById('thread-bulk-action-container').classList.remove('d-none')
                                                                    document.getElementById('conversation-view-container').classList.add('d-none')
                                                                    document.getElementById('conversation-view-container-1').classList.add('d-none')
                                                                } else {
                                                                    taskSelections = document.getElementsByClassName('thread-selection')
                                                                    for (let i = 0; i < taskSelections.length; i++) {
                                                                        const element = taskSelections[i];
                                                                        if (element.checked) {
                                                                            return
                                                                        }
                                                                    }
                                                                    document.getElementById('thread-bulk-action-container').classList.add('d-none')
                                                                    document.getElementById('conversation-view-container').classList.remove('d-none')
                                                                    document.getElementById('conversation-view-container-1').classList.remove('d-none')
                                                                }
                                                            })
                                                        </script>
                                                    </div>
                                                    {% comment %} Checkbox {% endcomment %}
                                                    <div class="border-bottom py-3 px-3 message_thread cursor-pointer media position-relative {% if active_thread == message_thread %}bg-light{% endif %} w-100" tabindex="1">
                                                        {% if message_thread.usage_status == 'archived' %}
                                                        <div class="blurry-text">
                                                            DELETED {% comment %} blurry text doesnt works for big or long text {% endcomment %}
                                                        </div>
                                                        {% else %}
                                                        <div class="d-flex justify-content-between w-100">
                                                            {% with messages=message_thread.message_set.all %}
                                                                <div class="w-100">
                                                                    <a  
                                                                        {% if messages|length == 1 %}
                                                                            {% with message=messages|first %}
                                                                                {% if message.status == 'draft' %}
                                                                                    hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                                                                                    hx-vals='{"drawer_type":"manage-message", "message_id" : "{{message.id}}"}'
                                                                                    hx-target="#inbox-drawer-content"
                                                                                    class="stretched-link create-message-button"
                                                                                {% endif %}
                                                                            {% endwith %}
                                                                        {% endif %}
                                                                        hx-get="{% host_url 'message_detail' message_thread.id host 'app' %}"  
                                                                        hx-target="#message-detail"
                                                                        class="stretched-link message_thread_button"
                                                                        hx-trigger="click"
                                                                        hx-swap="innerHTML"
                                                                        id="message-thread-button-{{message_thread.id}}"
                                                                        
                                                                        >
                                                                        {% if message_thread.message_type == 'call' %}
                                                                            <div class="text-gray-800 fw-bolder fs-4"><i class="fa fa-phone fs-3"></i> {% if LANGUAGE_CODE == 'ja' %}に{% else %}To{% endif %} {{message_thread.platform_id}}</div>
                                                                        {% elif message_thread.message_type == 'chatbot' %}
                                                                            <div class="d-flex w-100">
                                                                                <div class="tw-w-[20px] tw-mr-2">
                                                                                    <span class="menu-icon me-1 text-gray-800">
                                                                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-robot" viewBox="0 0 16 16">
                                                                                            <path d="M6 12.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5M3 8.062C3 6.76 4.235 5.765 5.53 5.886a26.58 26.58 0 0 0 4.94 0C11.765 5.765 13 6.76 13 8.062v1.157a.933.933 0 0 1-.765.935c-.845.147-2.34.346-4.235.346-1.895 0-3.39-.2-4.235-.346A.933.933 0 0 1 3 9.219zm4.542-.827a.25.25 0 0 0-.217.068l-.92.9a24.767 24.767 0 0 1-1.871-.183.25.25 0 0 0-.068.495c.55.076 1.232.149 2.02.193a.25.25 0 0 0 .189-.071l.754-.736.847 1.71a.25.25 0 0 0 .404.062l.932-.97a25.286 25.286 0 0 0 1.922-.188.25.25 0 0 0-.068-.495c-.538.074-1.207.145-1.98.189a.25.25 0 0 0-.166.076l-.754.785-.842-1.7a.25.25 0 0 0-.182-.135Z"/>
                                                                                            <path d="M8.5 1.866a1 1 0 1 0-1 0V3h-2A4.5 4.5 0 0 0 1 7.5V8a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1v1a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-1a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1v-.5A4.5 4.5 0 0 0 10.5 3h-2zM14 7.5V13a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V7.5A3.5 3.5 0 0 1 5.5 4h5A3.5 3.5 0 0 1 14 7.5"/>
                                                                                        </svg>
                                                                                    </span>
                                                                                </div>
                                                                                <div class="text-gray-800 fw-bolder fs-4">{{message_thread.meta_data.user_name}} {% if message_thread.meta_data.pic %}{% if LANGUAGE_CODE == 'ja' %}に{% else %}To{% endif %} {{message_thread.meta_data.pic.first_name}}{% endif %}
                                                                                </div>
                                                                            </div>
                                                                            {% if message_thread.last_message_date %}
                                                                                <div class="text-gray-800 fw-bolder fs-7">{% local_time message_thread.last_message_date selected_timezone %}</div> 
                                                                            {% endif %}
                                                                        {% elif message_thread.message_type == 'email' %}
                                                                            <div class="text-gray-800 fw-bolder fs-4">
                                                                                {% if message_thread.contacts.all %}
                                                                                {% for contact in message_thread.contacts.all %}
                                                                                        {{contact.name}}
                                                                                    {% endfor %}
                                                                                {% else %}
                                                                                    {{message_thread.channel.name}}
                                                                                {% endif %}
                                                                            </div>
                                                                            {% if message_thread.title %}
                                                                                <div class="fs-6 text-gray-500 fw-bolder">{{message_thread.title|truncatechars:120}}</div>  
                                                                            {% endif %}    
                                                                            <div class="text-gray-800 fw-bolder fs-7">{% local_time message_thread.last_message_date selected_timezone %}</div> 
                                                                        {% elif message_thread.message_type == 'sms' %}
                                                                            <div class="text-gray-800 fw-bolder fs-4">
                                                                                {% if message_thread.contacts.all %}
                                                                                    {{message_thread.contacts.all.first.name}}
                                                                                {% else %}
                                                                                    {{message_thread.channel.name}} - Send SMS
                                                                                {% endif %}
                                                                            </div>
                                                                            {% if message_thread.platform_id %}
                                                                            <div class="fs-6 text-gray-500 fw-bolder">
                                                                                {% if LANGUAGE_CODE == 'ja'%}
                                                                                に送る：
                                                                                {% else %}
                                                                                Send To: 
                                                                                {% endif %}
                                                                                {{message_thread.platform_id|truncatechars:120}}
                                                                            </div>      
                                                                            {% endif %}
                                                                            <div class="text-gray-800 fw-bolder fs-7">{% local_time message_thread.last_message_date selected_timezone %}</div> 
                                                                        {% else %}
                                                                            <div class="text-gray-800 fw-bolder fs-4 tw-flex">
                                                                                {% if message_thread.message_type == 'line' %}
                                                                                <div class="tw-w-[20px] tw-mr-2">
                                                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="#4CC764">
                                                                                        <path d="M311 196.8v81.3c0 2.1-1.6 3.7-3.7 3.7h-13c-1.3 0-2.4-.7-3-1.5l-37.3-50.3v48.2c0 2.1-1.6 3.7-3.7 3.7h-13c-2.1 0-3.7-1.6-3.7-3.7V196.9c0-2.1 1.6-3.7 3.7-3.7h12.9c1.1 0 2.4 .6 3 1.6l37.3 50.3V196.9c0-2.1 1.6-3.7 3.7-3.7h13c2.1-.1 3.8 1.6 3.8 3.5zm-93.7-3.7h-13c-2.1 0-3.7 1.6-3.7 3.7v81.3c0 2.1 1.6 3.7 3.7 3.7h13c2.1 0 3.7-1.6 3.7-3.7V196.8c0-1.9-1.6-3.7-3.7-3.7zm-31.4 68.1H150.3V196.8c0-2.1-1.6-3.7-3.7-3.7h-13c-2.1 0-3.7 1.6-3.7 3.7v81.3c0 1 .3 1.8 1 2.5c.7 .6 1.5 1 2.5 1h52.2c2.1 0 3.7-1.6 3.7-3.7v-13c0-1.9-1.6-3.7-3.5-3.7zm193.7-68.1H327.3c-1.9 0-3.7 1.6-3.7 3.7v81.3c0 1.9 1.6 3.7 3.7 3.7h52.2c2.1 0 3.7-1.6 3.7-3.7V265c0-2.1-1.6-3.7-3.7-3.7H344V247.7h35.5c2.1 0 3.7-1.6 3.7-3.7V230.9c0-2.1-1.6-3.7-3.7-3.7H344V213.5h35.5c2.1 0 3.7-1.6 3.7-3.7v-13c-.1-1.9-1.7-3.7-3.7-3.7zM512 93.4V419.4c-.1 51.2-42.1 92.7-93.4 92.6H92.6C41.4 511.9-.1 469.8 0 418.6V92.6C.1 41.4 42.2-.1 93.4 0H419.4c51.2 .1 92.7 42.1 92.6 93.4zM441.6 233.5c0-83.4-83.7-151.3-186.4-151.3s-186.4 67.9-186.4 151.3c0 74.7 66.3 137.4 155.9 149.3c21.8 4.7 19.3 12.7 14.4 42.1c-.8 4.7-3.8 18.4 16.1 10.1s107.3-63.2 146.5-108.2c27-29.7 39.9-59.8 39.9-93.1z"/>
                                                                                    </svg>
                                                                                </div>
                                                                                {% endif %}
                                                                                {{message_thread.channel.name}}
                                                                                <div class="tw-w-[10px] tw-mx-1">
                                                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                                                                        <path d="M406.6 374.6l96-96c12.5-12.5 12.5-32.8 0-45.3l-96-96c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L402.7 224l-293.5 0 41.4-41.4c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-96 96c-12.5 12.5-12.5 32.8 0 45.3l96 96c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L109.3 288l293.5 0-41.4 41.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0z"/>
                                                                                    </svg>
                                                                                </div>
                                                                                {{message_thread.contacts.all.first.name}}
                                                                            </div>
                                                                            <div class="text-gray-800 fw-bolder fs-7">{% local_time message_thread.last_message_date selected_timezone %}</div> 
                                                                        {% endif %}
                                                                        <div class="fs-6 text-gray-700">{{message_thread|latest_message_body|truncatechars:50}}</div>      
                                                                        <div class="mt-2">
                                                                            {% include 'data/inbox/thread-status-badge.html' %}

                                                                            {% include 'data/inbox/thread-assignee-badge.html' %}
                                                                        </div>
                                                                    </a>
                                                                </div>
                                                            {% endwith %}
                                                        </div>
                                                        {%endif%}
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            {% else %}
                                            <div class="mt-5 ms-4">
                                                <div class="">
                                                    <h3>
                                                        {% if LANGUAGE_CODE == 'ja'%}
                                                        メッセージがありません。
                                                        {% else %}
                                                        No Message Yet
                                                        {% endif %}
                                                    </h3>
                                                    <p>
                                                        {% if LANGUAGE_CODE == 'ja'%}
                                                        まだメッセージが作られていません。
                                                        {% else %}
                                                        We haven't captured any message yet
                                                        {% endif %}
                                                    </p>
                                                </div>
                                            </div>
                                            {% endif %}        
                                
                                        </div>
                                    </div>
                                </div>
                            </div>
        
                        </div>
                    </div>
                    <div class="mb-10 py-2 d-flex justify-content-between align-items-center" style="height:5%">
                        <div class="current ps-4">
                            {% if LANGUAGE_CODE == 'ja'%}
                            {{paginator_item_begin}}–{{paginator_item_end}} の {{message_count}} 件のメッセージ
                            {% else %}
                            Viewing {{paginator_item_begin}}–{{paginator_item_end}} of {{message_count}} messages
                            {% endif %}
                        </div>
            
                        <div>
                            {% if message_threads %}
                                {% if page_content.has_previous %}
                                <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" 
                                href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page_type={{page_type}}&page=1"
                                >&laquo; {% if LANGUAGE_CODE == 'ja'%}最初{% else %}First{% endif %}</a>
            
                                <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" 
                                href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page_type={{page_type}}&page={{ page_content.previous_page_number }}"
                                >{% if LANGUAGE_CODE == 'ja'%}前{% else %}Previous{% endif %}</a>
                                {% endif %}
                                
                                {% if page_content.has_next %}
                                <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" 
                                href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page_type={{page_type}}&page={{ page_content.next_page_number }}"
                                >{% if LANGUAGE_CODE == 'ja'%}次{% else %}Next{% endif %}</a>
                                <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" 
                                href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page_type={{page_type}}&page={{ page_content.paginator.num_pages }}"
                                > {% if LANGUAGE_CODE == 'ja'%}最後{% else %}Last{% endif %} &raquo;</a>
                                {% endif %}		
                            {% endif %}
                        </div>
                    </div>

                </div>
                <div class="modal fade" tabindex="-1" id="manage_restore_bulk">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header pb-0 border-0 justify-content-end">
                                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                                </div>
                            </div>
                            <div class="modal-body pb-0">
                                <div class="mb-13 text-center">
                                    <h3 class="modal-title">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        一括有効化の確認
                                        {% else %}
                                        Bulk Activate Confirmation
                                        {% endif %}
                                    </h3>
                                </div>
                                <div class="border-bottom">
                                    <div class="fv-rowd-flex flex-column mb-8">
                                        <label class="{% include 'data/utility/form-label.html' %}">
                                            <span class="">
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                これらのメッセージを有効化してもよろしいですか?
                                                {% else %}
                                                Are you sure to activate these messages?
                                                {% endif %}
                                            </span>
                                        </label>
                                   
                                    </div>
                                </div>
                            </div>
                            
                            <input name='flag_all' class="flag_all" hidden></input>
            
                            <div class="modal-footer border-0">
                                <button name="bulk_restore_conversations" type="submit" class="btn btn-success">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    有効化
                                    {% else %}
                                    Activate
                                    {% endif %}
                                </button>
                                <a data-bs-dismiss="modal" class="btn border bg-gray-200">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    キャンセル
                                    {% else %}
                                    Cancel
                                    {% endif %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div> 

                <div class="modal fade" tabindex="-1" id="manage_delete_bulk">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header pb-0 border-0 justify-content-end">
                                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                                </div>
                            </div>
                            <div class="modal-body pb-0">
                                <div class="mb-13 text-center">
                                    <h3 class="modal-title">
                                        
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        一括アーカイブの確認
                                        {% else %}
                                        Bulk Archive Confirmation
                                        {% endif %}
                                    
                                    </h3>
                                </div>
                                <div class="border-bottom">
                                    <div class="fv-rowd-flex flex-column mb-8">
                                        <label class="{% include 'data/utility/form-label.html' %}">
                                            <span class="">
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                これらのメッセージをアーカイブしてもよろしいですか?
                                                {% else %}
                                                Are you sure to archive these messages?
                                                {% endif %}
                                            </span>
                                        </label>
                                
                                    </div>
                                </div>
                            </div>
                            
                            <input name='flag_all' class="flag_all" hidden></input>
            
                            <div class="modal-footer border-0">
                                <button name="bulk_delete_conversations" type="submit" class="btn btn-danger">
                                    
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    アーカイブ
                                    {% else %}
                                    Archive
                                    {% endif %}
                                
                                </button>
                                <a data-bs-dismiss="modal" class="btn border bg-gray-200">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    キャンセル
                                    {% else %}
                                    Cancel
                                    {% endif %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div> 

            </form>
        </div>
    </div>
    
</div>

{% include 'data/javascript/toggleSearch.html' %}



{% elif page_type == 'scheduled' or page_type == 'draft' %}
{% include 'data/inbox/scheduled_message.html' %}
{% elif page_type == 'templates' %}
    {% include 'data/inbox/templates.html' %}
{% endif %}

{% endblock %}

{% block js %}
    {% if id %}
    <script>
        $(document).ready(function(){
            console.log("message-thread-button-{{id}}")
            htmx.trigger('#message-thread-button-31dee678-c380-4820-a908-ba6ae375d396', 'click')
        })
    </script>
    {% endif %}
    <script src="https://cdn.jsdelivr.net/npm/rrweb@latest/dist/record/rrweb-record.min.js"></script>
    
    <script>

    let events = [];
    rrwebRecord({
    emit(event) {
        // push event into the events array
        events.push(event);
    },
    });

    // this function will send events to the backend and reset the events array
    function save() {
    const body = JSON.stringify({ events });
        events = [];
        fetch("{% host_url 'recorded_events' host 'app' %}", {
            method: 'POST',
            headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token }}',
            },
            body,
        });
    }

    // save events every 10 seconds
    {% comment %} setInterval(save, 10 * 1000); {% endcomment %}
</script>

<script>
    {% comment %} CALL {% endcomment %}
    var ongoingCallToastElm = document.getElementById('ongoing-call-toast');
    var ongoingCallToast = bootstrap.Toast.getOrCreateInstance(ongoingCallToastElm);
    var incomingCallToastElm = document.getElementById('incoming-call-toast');
    var incomingCallToast = bootstrap.Toast.getOrCreateInstance(incomingCallToastElm);
    var incomingCallIntegration = document.getElementById('incoming_call_integration_id')
    var incomingCallEnabler = document.getElementById('incoming-call-enabler')
    var incomingCallDisabler = document.getElementById('incoming-call-disabler')
    var acceptIncomingCallButtons = document.getElementsByClassName('acceptIncomingCallButton')
    var rejectIncomingCallButtons = document.getElementsByClassName('rejectIncomingCallButton')
    var muteIncomingCallButton = document.getElementById('muteIncomingCallButton')
    var micEnableIncomingCallButton = document.getElementById('micEnableIncomingCallButton')
    var micDisableIncomingCallButton = document.getElementById('micDisableIncomingCallButton')
    
    var device = new Twilio.Device('')

    device.on('registered', () => {
        console.log('The device is ready to receive calls.')
        incomingCallEnabler.classList.add('d-none')
        incomingCallDisabler.classList.remove('d-none')
        incomingCallIntegration.setAttribute('readonly', true)
        incomingCallIntegration.classList.add('form-select-solid')
        select2IncomingCallIntegrationContainer = document.getElementById('select2-incoming_call_integration_id-container').parentElement
        select2IncomingCallIntegrationContainer.classList.add('form-select-solid')
    })
    
    device.on('incoming', function(incoming_call) {
        console.log('Incoming call ringing.')
        console.log(incoming_call.parameters)
        from_ = incoming_call.parameters.From
        incomingCallToastElm.getElementsByClassName('from-text')[0].innerHTML = from_ 
        incomingCallToast.show()

        incoming_call.on("accept", acceptedIncomingCall);
        incoming_call.on("cancel", cancelledIncomingCall);
        incoming_call.on("disconnect", disconnectedIncomingCall);
        incoming_call.on("reject", rejectedIncomingCall);

        acceptIncomingCallButtons.forEach(function(elm) {
            elm.onclick = () => {
                console.log("Accepting incoming call.");
                incoming_call.accept();
            };
            document.getElementById('oncall-status-updater').dispatchEvent(new Event('oncall'))
        })
        rejectIncomingCallButtons.forEach(function(elm) {
            elm.onclick = () => {
                console.log("Rejecting incoming call ...");
                if (incoming_call.status() == "open"){
                    incoming_call.disconnect();
                } else {
                    incoming_call.reject();
                }
                incomingCallToast.hide()
                acceptIncomingCallButtons.forEach(function(elm) {
                    elm.classList.remove('d-none');
                })
            };
        })

        muteIncomingCallButton.onclick = function() {
            if (incoming_call.isMuted()) {
                incoming_call.mute(false)
                micEnableIncomingCallButton.classList.remove('d-none');
                micDisableIncomingCallButton.classList.add('d-none');
            } else {
                incoming_call.mute(true)
                micEnableIncomingCallButton.classList.add('d-none');
                micDisableIncomingCallButton.classList.remove('d-none');
            }
        }

        function acceptedIncomingCall(){
            console.log('Incoming call accepted.')
            acceptIncomingCallButtons.forEach(function(elm) {
                elm.classList.add('d-none');
            })
            muteIncomingCallButton.classList.remove('d-none');
        }

        function cancelledIncomingCall(){
            console.log('Incoming call cancelled.')
            incomingCallToast.hide()
            acceptIncomingCallButtons.forEach(function(elm) {
                elm.classList.remove('d-none');
            })
            muteIncomingCallButton.add('d-none');
            micEnableIncomingCallButton.classList.remove('d-none');
            micDisableIncomingCallButton.classList.add('d-none');
        }

        function disconnectedIncomingCall(){
            console.log('Incoming call disconnected.')
            incomingCallToast.hide()
            acceptIncomingCallButtons.forEach(function(elm) {
                elm.classList.remove('d-none');
            })
            muteIncomingCallButton.add('d-none');
            micEnableIncomingCallButton.classList.remove('d-none');
                micDisableIncomingCallButton.classList.add('d-none');
            document.getElementById('offcall-status-updater').dispatchEvent(new Event('offcall'))
        }

        function rejectedIncomingCall(){
            console.log('Incoming call rejected.')
            incomingCallToast.hide()
            muteIncomingCallButton.add('d-none');
            micEnableIncomingCallButton.classList.remove('d-none');
            micDisableIncomingCallButton.classList.add('d-none');
        }
    });

    device.on('unregistered', () => {
        console.log('The device is no longer able to receive calls.')
        if (device.state == "unregistered") {
            incomingCallDisabler.classList.add('d-none')
            incomingCallEnabler.classList.remove('d-none')
            incomingCallIntegration.setAttribute('readonly', false)
            incomingCallIntegration.classList.remove('form-select-solid')
            select2IncomingCallIntegrationContainer = document.getElementById('select2-incoming_call_integration_id-container').parentElement
            select2IncomingCallIntegrationContainer.classList.remove('form-select-solid')
        }
    })

    document.body.addEventListener("enableReceivingCall", function(evt){
        if (evt.detail.token){
            console.log(device.state)
            console.log(evt.detail.token)
            device.updateToken(evt.detail.token)
            device.register();
        } else {
            console.log('Failed to update device token.')
        }
    })

    document.body.addEventListener("disableReceivingCall", function(evt){
        console.log(device.state)   
        if (device.state == "registered") {
            if (evt.detail.status != "active-oncall") {
                console.log("unregistering")
                device.unregister();
                device.updateToken('')
            }
        } else {console.log('The device is already unregistered')}
    })
</script>

{% endblock js %}
    


