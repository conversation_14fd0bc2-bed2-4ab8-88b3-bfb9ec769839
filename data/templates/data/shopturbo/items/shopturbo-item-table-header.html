{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}

{% for shopturbo_items_column in shopturbo_items_columns %}
    <th {% if shopturbo_items_column == 'checkbox'%}
        class="{% include "data/utility/column-checkbox.html" %}"
        {% elif shopturbo_items_column == 'item_id' %}
        class="{% include "data/utility/column-id.html" %}"
        {% elif shopturbo_items_column == 'dropdown' %}
        class="min-w-40px w-40px"
        {% else %}
        class=""
        {% endif %}>
        {% if shopturbo_items_column != 'checkbox' and shopturbo_items_column != 'dropdown' %}
            {% if shopturbo_items_column|is_uuid %}
                {% with channel_column=shopturbo_items_column|search_custom_field_object_items:request %}
                    {% if channel_column.type == 'image' or channel_column.type == 'image_group' %}
                        <div style="min-width: 200px; max-width: 210px;">
                            {{channel_column.name}}
                        </div>
                    {% else %}
                        {{channel_column.name}}
                    {% endif %}
                {% endwith %}
            {% elif "child" in shopturbo_items_column and "|" in shopturbo_items_column %}
                {% with channel_column=shopturbo_items_column|search_custom_field_object_items_child:request %}
                    {{channel_column.name|display_column_items:request}} | 
                    {% if LANGUAGE_CODE == 'ja' %}子オブジェクト{% else %}Child Objects{% endif %}
                {% endwith %} 
            {% else %}
                {% with column_value=shopturbo_items_column|display_column_items:request %}
                    {{column_value}}
                {% endwith %}
            {% endif %}
        {% endif %}
    </th>
    {% if shopturbo_items_column == 'item_id' %}
    <th class="" style="width: 20px;">
    </th>
    {% endif %}
{% endfor %}