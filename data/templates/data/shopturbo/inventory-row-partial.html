{% load tz %}
{% load custom_tags %}
{% load i18n %}
{% load hosts %}
{% load humanize %}
{% load formatting_tags %}
{% get_current_language as LANGUAGE_CODE %}

    <!-- DEBUG: Processing row_type={{ row_type }} for inventory={{ inventory.id }} -->
    <script>console.log('DEBUG TD: Processing row_type={{ row_type }}');</script>
    {% if "checkbox" == row_type %}
        <td class="{% include "data/utility/column-checkbox.html" %}">
            <input style="" id="inventory-selection-{{inventory.id}}" class="form-check-input cursor-pointer inventory-selection check_input" type="checkbox" name="checkbox" data-owner="{{inventory.owner.user.id}}" value="{{inventory.id}}" onclick="checking_checkbox(this,event)"/>
            
            {% comment %} DO NOT ADD BELOW SCRIPT INSIDE OF <TD> !!!! {% endcomment %}
            <script>
                checkbox = document.getElementById('inventory-selection-{{inventory.id}}')
                checkbox.addEventListener('change', function() {
                    if (this.checked) {
                        document.getElementById('inventory-bulk-action-container').classList.remove('tw-hidden')
                        document.getElementById('inventory-view-container').classList.add('tw-hidden')
                        document.getElementById('inventory-view-container-1').classList.add('tw-hidden')
                    } else {
                        taskSelections = document.getElementsByClassName('inventory-selection')
                        for (let i = 0; i < taskSelections.length; i++) {
                            const element = taskSelections[i];
                            if (element.checked) {
                                return
                            }
                        }
                        document.getElementById('inventory-bulk-action-container').classList.add('tw-hidden')
                        document.getElementById('inventory-view-container').classList.remove('tw-hidden')
                        document.getElementById('inventory-view-container-1').classList.remove('tw-hidden')
                    }
                })
            </script>
        </td>

    {% elif "name" == row_type %}
        <td class="fw-bolder">

                <a id="profile_wizard_button" class="fw-bolder text-center mb-0 text-dark text-hover-primary fw-bolder cursor-pointer manage-full-wizard-button inventory_{{inventory.id}}"
                    hx-get="{% url 'shopturbo_load_drawer' %}"
                    hx-vals = '{"drawer_type":"inventory-manage", "item_id":"{{inventory.id}}", "view_id": "{{view_id}}", "module":"{{menu_key}}", "page": "{{page}}"}'
                    hx-target="#manage-full-drawer-content"
                    hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"
                    hx-trigger="click"
                >
                {{inventory.name}}
                </a>
        </td>
    {% elif "warehouse" == row_type %}
        <td class="fw-bold">
            {{inventory.warehouse.id|get_inventory_warehouse_name:request}}

        </td>
    {% elif "date" == row_type %}
        <td class="fw-bold">
                {% date_format inventory.date 1 %}
        </td>
    {% elif "inventory_id" == row_type %}
    <script>console.log('DEBUG TD: inventory_id - generating 2 TD elements');</script>
    <td class="{% include "data/utility/column-id.html" %}" style="border-right: 1px solid !important; border-right-color: rgb(234, 234, 234) !important;">
        <span class="d-none manage-full-wizard-button inventory_{{inventory.id}}_activity"
            hx-get="{% url 'shopturbo_load_drawer' %}"
            hx-vals = '{"tab":"activity", "drawer_type":"inventory-manage", "inventory_id":"{{inventory.id}}", "view_id": "{{view_id}}", "module":"{{module}}", "page": "{{page}}" }'
            hx-target="#manage-full-drawer-content"
            hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"
            hx-trigger="click"
            hx-on::before-request="document.getElementById('manage-full-drawer-content').innerHTML = '';"
            >
        </span>
        <a id="profile_wizard_button" class="{% include "data/utility/table-link.html" %} manage-full-wizard-button view_form_trigger{{inventory.id}} inventory_{{inventory.id}}"
            hx-get="{% url 'shopturbo_load_drawer' %}"
            hx-vals = '{"drawer_type":"inventory-manage", "inventory_id":"{{inventory.id}}", "view_id": "{{view_id}}", "module":"{{module}}", "page": "{{page}}" }'
            hx-target="#manage-full-drawer-content"
            hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"
            hx-trigger="click"
            hx-on::before-request="document.getElementById('manage-full-drawer-content').innerHTML = '';"
        >
        {{ inventory.inventory_id|stringformat:"04d" }}
        </a>
    </td>
    <td class="" style="width: 20px;">
        <!-- DEBUG TD: inventory_id extra column (2nd TD) -->
    </td>

    {% elif "owner" == row_type %}
    <td class="fw-bold">
        {% if inventory.owner and inventory.owner.user %}
            {% with inventory.owner.user as user %}
            {% if user.verification.profile_photo %}
            <img class="w-20px rounded-circle me-2" src="{{user.verification.profile_photo.url}}" style="object-fit: cover !important;aspect-ratio: 16 / 16;" />
            {% else %}
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.28548 15.0861C7.34369 13.1814 9.35142 12 11.5304 12H12.4696C14.6486 12 16.6563 13.1814 17.7145 15.0861L19.3493 18.0287C20.0899 19.3618 19.1259 21 17.601 21H6.39903C4.87406 21 3.91012 19.3618 4.65071 18.0287L6.28548 15.0861Z" fill="currentColor"/>
                    <rect opacity="0.3" x="8" y="3" width="8" height="8" rx="4" fill="currentColor"/>
                </svg>    
            {% endif %}
            <span >
                {{user.first_name}}
            </span>
            {% endwith %}
        {% endif %}
    </td>
    {% elif "number_available" == row_type %}
    <td class="fw-bold">
        {{ inventory.number_available }}

    </td>

    {% elif "items" == row_type or 'item' == row_type %}
    <td class="fw-bold">
            {% comment %}
            <style>
                #loading-item-{{inventory.id|remove_dash}}{
                    display:none;
                }
                .htmx-request #loading-item-{{inventory.id|remove_dash}}{
                    display:inline-block;
                }
                .htmx-request#loading-item-{{inventory.id|remove_dash}}{
                    display:inline-block;
                }
            </style>
            <div hx-get="{% host_url 'get_inventory_item' inventory.id host 'app' %}"
                hx-trigger="load"
                hx-target="this"
                hx-swap="outerHTML"
                hx-vals='{"source":"source-inventory", "module":"{{menu_key}}"}'
                hx-indicator="#loading-item-{{inventory.id|remove_dash}}">
                <div id="loading-item-{{inventory.id|remove_dash}}">
                    <div class="spinner-border text-primary spinner-border-sm" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
            {% endcomment %}

            {% with inventory.item.all as items %}
                {% if items %}
                    {% for item in items %}
                        <a id="profile_wizard_button_{{item.id}}" class="{% include "data/utility/table-link-shopturbo.html" %} item_{{item.id}}" 
                            hx-get="{% url 'shopturbo_load_drawer' %}" 
                            hx-vals = '{"drawer_type":"item-manage", "item_id":"{{item.id}}","source":"{{source}}","view_id":"None" }'
                            hx-target="#shopturbo-drawer-content"
                            hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"  
                            hx-trigger="click"
                        >
                            {% get_object_display item 'commerce_items' %} 
                        </a>
                    {% endfor %}
                {% endif %}
            {% endwith %}
    </td>

    {% elif "inventory_value" == row_type %}
    <td class="fw-bold">
            {% if inventory.item.all %}
                {% with currency=inventory.item.all.0.currency %}
                    {% for CURRENCY in CURRENCY_MODEL %}
                        {% if CURRENCY.0 == currency %}
                            {{ CURRENCY.2 }}
                        {% endif %}
                    {% endfor %}

                    {{inventory.inventory_value|use_thousand_separator_string_with_currency:currency}}
                {% endwith %}
            {% endif %}
    </td>

    {% elif "total_inventory" == row_type %}
    <td class="fw-bold">
            {% comment %} Debug: Add logging to track inventory calculation {% endcomment %}
            {% if current_inventory %}
                {% with calculated_amount=inventory|get_and_update_inventory_amount_display %}
                    {% comment %} Debug info: inventory.id={{ inventory.id }}, current_inventory=True, calculated_amount={{ calculated_amount }}, raw_total_inventory={{ inventory.total_inventory }} {% endcomment %}
                    {{ calculated_amount|use_thousand_separator }}
                {% endwith %}
            {% else %}
                {% with future_amount=inventory|get_inventory_future_amount_display %}
                    {% comment %} Debug info: inventory.id={{ inventory.id }}, current_inventory=False, future_amount={{ future_amount }}, raw_total_inventory={{ inventory.total_inventory }} {% endcomment %}
                    {{ future_amount|use_thousand_separator }}
                {% endwith %}
            {% endif%}

    </td>

    {% elif "status" == row_type %}

    <td class="fw-bold">
        {% translate_lang inventory.get_status_display LANGUAGE_CODE %}
    </td>

    {% elif "inventory_status" == row_type %}

    <td class="fw-bold">
        {% with status=inventory.inventory_status %}
            {% if status == 'available' %}
                {% if LANGUAGE_CODE == 'ja' %}
                    販売可能
                {% else %}
                    Available
                {% endif %}
            {% elif status == 'committed' %}
                {% if LANGUAGE_CODE == 'ja' %}
                    確定済み
                {% else %}
                    Committed
                {% endif %}
            {% elif status == 'unavailable' %}
                {% if LANGUAGE_CODE == 'ja' %}
                    利用不可
                {% else %}
                    Unavailable
                {% endif %}
            {% else %}
                {{ status }}
            {% endif %}
        {% endwith %}
    </td>

    {% elif "updated_at" == row_type %}
    <td class="fw-bold">
            {% date_format inventory.updated_at 1 %}
    </td>

    {% elif "created_at" == row_type %}
    <td class="fw-bold">
            {% date_format inventory.created_at 1 %}
    </td>
    {% elif "-inventory id" in row_type|lower %}
    <td class="fw-bold text-center">
            <div class="fw-bolder">
                {% if inventory.status != 'archived' %}
                {{inventory.id|inventory_platform_name:row_type}}
                {% endif %}
            </div>
    </td>
    {% elif "-sku" in row_type|lower and not 'item__' in row_type %}
    <td class="fw-bold text-center">
            <div class="fw-bolder">
                {% if inventory.status != 'archived' %}
                {{inventory.id|inventory_platform_name:row_type}}
                {% endif %}
            </div>
    </td>
    {% elif "-variant id" in row_type|lower and not 'item__' in row_type %}
    <td class="fw-bold text-center">
            <div class="fw-bolder">
                {% if inventory.status != 'archived' %}
                {{inventory.id|inventory_platform_name:row_type}}
                {% endif %}
            </div>
    </td>
    {% elif "platform_ids" in row_type|lower %}
    <td class="fw-bolder">
        <div class="">
                {% for platform_id in inventory|get_inventory_platforms %}
                    <div class="fw-bold mb-1">
                        <span class="badge bg-gray-500">{{platform_id}}</span>
                    </div>
                {% endfor %}
        </div>
    </td>
    {% elif "unit_price" == row_type %}
        <td class="fw-bold">
            <div class="">
                {% if inventory.unit_price %}
                    {% if inventory.currency %}
                        {{ inventory.currency|convert_currency_to_symbol }}
                    {% endif %}
                    {{inventory.unit_price|use_thousand_separator_string_with_currency:inventory.currency}}
                {% endif %}
            </div>
        </td>
    {% elif 'item__supplier' in row_type %}
        <td class="fw-bold">
            {% if item__supplier == row_type %}
                {% include 'data/partials/partial-customer-handler.html' with column=row_type|cut:"item__" object=inventory.item.all.first with_no_td=True %}
            {% else %}
                {% include 'data/partials/partial-customer-handler.html' with column=row_type|cut:"item__supplier__" object=inventory.item.all.first with_no_td=True %}
            {% endif %}
        </td>
    {% elif 'item__' in row_type %}
    <td class="fw-bold">
        <div class="">
            {{inventory|object_field_value:row_type}}
        </div>
    </td>
    {% elif row_type|is_uuid or '|item_custom_field' in row_type %}
    <td>
            {% if '|item_custom_field' in row_type %}
                {% comment %} ITEM {% endcomment %}
                {% with first_item=inventory.item.all.first %}
                    {% if first_item %}
                        {% include "data/shopturbo/items/custom-prop-cell-partial.html" with item=first_item row_type=row_type %}
                    {% endif %}
                {% endwith %}
            {% else %}
                {% with CustomFieldName=row_type|search_custom_field_object_inventory:request %}

                    {% if CustomFieldName.type == 'image_group' %}
                        <div class="ms-2">
                            {% if CustomFieldName.id|get_value_custom_field_inventory_objects:inventory.id %}
                                {% with customfieldvalue=CustomFieldName.id|get_value_custom_field_inventory_objects:inventory.id %}
                                    <div class="d-flex my-4" >
                                        {% for customfieldvaluefile in customfieldvalue.shopturboinventoryvaluefile_set.all|dictsortreversed:"created_at" %}
                                            {% if forloop.counter < 4 %}
                                                <a href="{{customfieldvaluefile.file.url}}" class="me-2">
                                                    <img width="auto" height="30" src="{{customfieldvaluefile.file.url}}" />
                                                </a>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                {% endwith %}
                            {% endif %}
                        </div>
                    {% elif CustomFieldName.type == 'formula' %}
     
                            <div id="inventory-formula-value-{{inventory.id}}-{{CustomFieldName.id}}" 
                                hx-post="{% url 'get_formula_result' %}" 
                                hx-vals='{"obj_id":"{{inventory.id}}","custom_field_id":"{{CustomFieldName.id}}","object_type":"commerce_inventory"}'
                                hx-target="#inventory-formula-value-{{inventory.id}}-{{CustomFieldName.id}}"
                                hx-trigger="load"
                                hx-swap="outerHTML"
                            >
                                <div class="fw-bold">
                                    <div class="spinner-border text-primary spinner-border-sm" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>

                  
                    {% elif CustomFieldName.id|get_value_custom_field_inventory:inventory.id %}
                        
                        {% if CustomFieldName.type == 'number'%}
                            <div class="fw-bold">
                            {% if CustomFieldName.number_format == '%' %}
                                {{ CustomFieldName.id|get_value_custom_field_inventory:inventory.id }} {{CustomFieldName.number_format|upper}}
                            {% elif CustomFieldName.number_format == 'number' %}
                                {{ CustomFieldName.id|get_value_custom_field_inventory:inventory.id }}
                            {% else %}
                                {{CustomFieldName.number_format|upper|get_currency_symbol}} {{ CustomFieldName.id|get_value_custom_field_inventory:inventory.id|use_thousand_separator_string_with_currency:CustomFieldName.number_format }}
                            {% endif %}
                            </div>
                        {% elif CustomFieldName.type == 'image' %}
                            <div class="ms-2">
                                <a target="_blank" href="{{CustomFieldName.id|get_value_custom_field_inventory:inventory.id}}">
                                <div class="symbol symbol-lg-100px symbol-100px">
                                    <img style="object-fit: cover;" alt="Pic" src="{{CustomFieldName.id|get_value_custom_field_inventory:inventory.id}}" />
                                </div>
                                </a>
                            </div>
                        {% elif CustomFieldName.type == 'user' %}
                            <div class="fw-bolder">
                                {% with user_id=CustomFieldName.id|get_value_custom_field_inventory:inventory.id %}
                                    {% with user=user_id|convert_user_id_to_user %}
                                        {% if user %}
                                            {{user.first_name}}
                                        {% endif %}
                                    {% endwith %}
                                {% endwith %}
                            </div>

                        {% elif CustomFieldName.type == 'date' %}
                            
                            <div class="fw-bold">
                                {% with custom_field_value=CustomFieldName.id|get_value_custom_field_inventory_objects:inventory.id %}
                                {% date_format custom_field_value.value_time %}
                                {% endwith %}
                            </div>
                        {% elif CustomFieldName.type == 'tag' %}
                            <div class="">
                                {% with custom_field_value=CustomFieldName.id|get_value_custom_field_inventory_objects:inventory.id %}
                                    {% for tag in custom_field_value.value|get_json %}
                                    <div class="fw-bold mb-1">
                                        <span class="badge bg-gray-500">{{tag.value}}</span>
                                    </div>
                                    {% endfor %}
                                {% endwith %}
                            </div>
                        {% elif CustomFieldName.type == 'date_time' %}
                            <div class="fw-bold">
                                {% with custom_field_value=CustomFieldName.id|get_value_custom_field_inventory_objects:inventory.id %}
                                    {% date_format custom_field_value.value_time 1 %}
                                {% endwith %}
                            </div>
                        {% else %}
                            <div class="fw-bold">
                                {% with custom_field_value=CustomFieldName.id|get_value_custom_field_inventory:inventory.id %}
                                    {% include "data/common/custom_field/row-partial-list.html" with CustomFieldName=CustomFieldName obj_id=custom_field_value value=custom_field_value object_type='commerce_inventory' %}
                                {% endwith %}
                            </div>
                        {% endif %}
                    {% endif %}
                {% endwith %}
            {% endif %}
    </td>
    {% else %}
    {% include "data/shopturbo/items-row-partial.html" with shopturbo_items_columns=row_type|object_to_list item=inventory.item.all.first %}
    {% endif %}


