{% load tz %}
{% load custom_tags %}
{% load i18n %}
{% load hosts %}
{% load humanize %}
{% get_current_language as LANGUAGE_CODE %}

{% for row_type in shopturbo_items_columns %}

    {% if "checkbox" == row_type %}
    <td class="{% include "data/utility/column-checkbox.html" %}">
        <div class="{% if index and index != '1' %}d-none{% endif %}">
            <input style="" id="item-selection-{{item.id}}" class="form-check-input cursor-pointer item-selection check_input" data-owner="{{item.owner.user.id}}" type="checkbox" name="checkbox" value="{{item.id}}" onclick="checking_checkbox(this,event,object_type)" {% is_item_selected item.id selected_ids as is_selected %}{% if is_selected %}checked{% endif %}/>
        </div>
    </td>

    {% elif "dropdown" == row_type %}
        {% if child_id %}
            {% if is_others %}
                <td class="w-40px">
                </td>
            {% else %}
                <td id="item-dropdown-{{child_id}}-{{parent_group_id}}" class="w-40px"
                    hx-get="{% url 'shopturbo_cascade_dropdown_parent' id=child_id %}"
                    hx-vals='{"view_id": {% if view_id %}"{{view_id}}"{% else %}"{{view_filter.view.id}}"{% endif %}, "child_level_type": "2", {% if parent_group_id %}"parent_group": "{{parent_group_id}}"{% endif %}}'
                    hx-target="#item-dropdown-{{child_id}}-{{parent_group_id}}"
                    hx-trigger="load"
                    hx-swap="outerHTML">
                </td>
            {% endif %}
        {% else %}
            <td id="item-dropdown-{{item.id}}" class="w-40px dropdown" onclick="toggleChild(this,'{{item.id}}')"
                hx-get="{% host_url 'shopturbo_item_group_row' host 'app' %}"
                hx-vals='js:{"current_length": 0, "parent_group_id": "{{item.id}}", "view_id": "{{view_filter.view.id}}","module": "{{menu_key}}"}'
                hx-trigger="click once"
                hx-swap="afterend"
                hx-target="#row-{{item.id}}">
                <div class="tw-ml-5">
                    <svg class="fa-arrow-down open" width="16" height="16" viewBox="0 0 448 512" fill="currentColor">
                        <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                    </svg>
                </div>
            </td>
        {% endif %}

    {% elif "name" == row_type %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bolder min-w-50px">
        {% endif %}
                <a id="profile_wizard_button" class="{% if index and index != '1' %}d-none{% endif %} text-center mb-0 text-dark text-hover-primary fw-bolder cursor-pointer manage-full-wizard-button item_{{item.id}}"
                    hx-get="{% url 'shopturbo_load_drawer' %}"
                    hx-vals = '{"drawer_type":"item-manage", "item_id":"{{item.id}}", "view_id":"{{view_id}}", "page": "{{page}}", "module": "{{menu_key}}" }'
                    hx-target="#manage-full-drawer-content"
                    hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"
                    hx-trigger="click"
                >
                {% if item.name %}
                    {% with custom_property_obj="name"|get_custom_property_object:item %}
                        {% if custom_property_obj %}
                            {% with value_map_label=custom_property_obj|get_attr:'value'|string_list_to_list %}
                                {{item.name|truncate_long_text:value_map_label.max_text_length}}
                            {% endwith %}
                        {% else %}
                            {{item.name|truncate_long_text:25}}
                        {% endif %}
                    {% endwith %}
                {% endif %}
            </a>
        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}

    {% elif "item_id" == row_type %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="{% include "data/utility/column-id.html" %}" style="border-right: 1px solid !important; border-right-color: rgb(234, 234, 234) !important;">
        {% endif %}
                {% if item.item_id %}
                <div class="d-flex align-items-center {% if index and index != '1' %}d-none{% endif %}">
                    <span class="d-none manage-full-wizard-button item_{{item.id}}_activity"
                        hx-get="{% url 'shopturbo_load_drawer' %}"
                        hx-vals = '{"tab":"activity", "drawer_type":"item-manage", "item_id":"{{item.id}}", "view_id":"{{view_id}}", "page": "{{page}}", "module": "{{menu_key}}" }'
                        hx-target="#manage-full-drawer-content"
                        hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"
                        hx-trigger="click"
                        hx-on::before-request="document.getElementById('manage-full-drawer-content').innerHTML = '';"
                        >
                    </span>
                    <button class="view_form_trigger{{item.id}} align-items-center d-flex btn btn-dark-outline btn-md manage-full-wizard-button py-1 text-dark text-hover-primary fw-bolder px-1 item_{{item.id}}" type="button"
                        hx-get="{% url 'shopturbo_load_drawer' %}"
                        hx-vals = '{"drawer_type":"item-manage", "item_id":"{{item.id}}", "view_id":"{{view_id}}", "page": "{{page}}", "module": "{{menu_key}}" }'
                        hx-target="#manage-full-drawer-content"
                        hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"
                        hx-on::before-request="document.getElementById('manage-full-drawer-content').innerHTML = '';"
                        >
                            {{ item.item_id|stringformat:"04d" }}
                    </button>
                    {% if property_sets %}
                    <div class="dropdown">
                        <button type="button" class="btn btn-primary-outline text-dark text-hover-primary px-1 py-0 dropdown-toggle" data-bs-toggle="dropdown">
                            <span class="d-inline-flex align-items-center justify-content-center" style="width: 20px; height: 20px;">
                                <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                                    <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                                </svg>
                            </span>
                        </button>
                        <ul class="dropdown-menu">
                            {% for set in property_sets %}
                            <li>
                                <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden manage-full-wizard-button" type="button"
                                    hx-get="{% url 'shopturbo_load_drawer' %}"
                                    hx-vals = '{"drawer_type":"item-manage", "item_id":"{{item.id}}", "view_id":"{{view_id}}", "page": "{{page}}", "set_id": "{{set.id}}", "module": "{{menu_key}}"}'
                                    hx-target="#manage-full-drawer-content"
                                    hx-indicator=".loading-drawer-spinner"
                                    style="border-radius: 0.475rem 0 0 0.475rem;"
                                    >
                                    {% if set.name %}
                                        {{ set.name}}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default{% endif %}
                                    {% endif %}
                                </button>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            {% if as_sub_cell %}
            </div>
            {% else %}
            </td>
            <td class="" style="width: 20px;">
            </td>
            {% endif %}
    {% elif "owner" == row_type %}
    <td class="fw-bold">
        {% if item.owner and item.owner.user %}
            {% with item.owner.user as user %}
            {% if user.verification.profile_photo %}
            <img class="w-20px rounded-circle me-2" src="{{user.verification.profile_photo.url}}" style="object-fit: cover !important;aspect-ratio: 16 / 16;" />
            {% else %}
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.28548 15.0861C7.34369 13.1814 9.35142 12 11.5304 12H12.4696C14.6486 12 16.6563 13.1814 17.7145 15.0861L19.3493 18.0287C20.0899 19.3618 19.1259 21 17.601 21H6.39903C4.87406 21 3.91012 19.3618 4.65071 18.0287L6.28548 15.0861Z" fill="currentColor"/>
                    <rect opacity="0.3" x="8" y="3" width="8" height="8" rx="4" fill="currentColor"/>
                </svg>    
            {% endif %}
            <span >
                {{user.first_name}}
            </span>
            {% endwith %}
        {% endif %}
    </td>
    {% elif "supplier" in row_type and not view_type %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bolder">
        {% endif %}
            {% if supplier == row_type %}
                {% include 'data/partials/partial-customer-handler.html' with column=row_type object=item with_no_td=True %}
            {% else %}
                {% include 'data/partials/partial-customer-handler.html' with column=row_type|cut:"supplier__" object=item with_no_td=True %}
            {% endif %}
        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}
    {% elif "platform" == row_type %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bolder">
        {% endif %}
            {% if item|get_value_of_attribute:row_type %}

                <a id="profile_wizard_button" class="{% include "data/utility/table-link.html" %} manage-full-wizard-button"
                hx-get="{% url 'shopturbo_load_drawer' %}"
                hx-vals = '{"drawer_type":"item-manage", "item_id":"{{item.id}}", "module": "{{menu_key}}" }'
                hx-target="#manage-full-drawer-content"
                hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"
                hx-trigger="click"
            >
            {{ item|get_value_of_attribute:row_type }}
            </a>
            {% endif %}
        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}

    {% elif "description" == row_type %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bold mw-sm-500px " >
        {% endif %}

                {% if item.description %}
                    {{item.description|truncate_long_text}}
                {% else %}
                    <div>----</div>
                {% endif %}
        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}

    {% elif "child-item" == row_type %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bold mw-sm-500px " >
        {% endif %}
            {% for item_child in item.child_item.all %}
                <div>
                    <a id="profile_wizard_button" class="{% include "data/utility/table-link.html" %} manage-full-wizard-button item_{{item_child.id}}"
                        hx-get="{% url 'shopturbo_load_drawer' %}"
                        hx-vals = '{"drawer_type":"item-manage", "item_id":"{{item_child.id}}", "view_id":"{{view_id}}", "module": "{{menu_key}}" }'
                        hx-target="#manage-full-drawer-content"
                        hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"
                        hx-trigger="click"
                        >
                        {% get_object_display item_child 'commerce_items' %}
                    </a>
                </div>
            {% endfor %}
        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}

    {% elif "price" == row_type and not view_type %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bold">
        {% endif %}
                {% if item.variation_switcher %}
                    {% with price=item|shopturbo_price_range %}
                        {% if price != None %}
                            {{ item.currency|convert_currency_to_symbol }}
                            {% if price.highest_price == price.lowest_price %}
                                {{price.highest_price|use_thousand_separator_string_with_currency:item.currency}}
                            {% else %}
                                {{price.lowest_price|use_thousand_separator_string_with_currency:item.currency}} - {{price.highest_price|use_thousand_separator_string_with_currency:item.currency}}
                            {% endif %}
                        {% endif %}
                    {% endwith %}
                {% else %}

                    {% with price=item|get_item_price:"volume_price" %}
                        {% if not price %}
                            {% with currency=item|get_item_price:"currency" price=item|get_item_price:"price" %}
                                {% if currency and price %}
                                    {{ currency|convert_currency_to_symbol }}{{price|use_thousand_separator_string_with_currency:currency}}
                                {% endif %}
                            {% endwith %}
                        {% elif item|is_default_vormula_price %}
                            {% with object=item|get_item_price:"object" %}
                                {% for volume_price in object.shopturbo_item_volume_price.all %}
                                <div>
                                    {% if LANGUAGE_CODE == 'ja' %}最小数量 {% else %}Min:{% endif %} {{volume_price.minimum}}
                                    {% if LANGUAGE_CODE == 'ja' %}最大数量 {% else %}Max:{% endif %} {{volume_price.maximum}}
                                    {% if LANGUAGE_CODE == 'ja' %}価格 {% else %}Price:{% endif %} {% with currency=item|get_item_price:"currency" %}{{volume_price.price|use_thousand_separator_string_with_currency:currency}}{% endwith %}
                                </div>
                                {% endfor %}
                            {% endwith %}
                        {% endif %}
                    {% endwith %}
                {% endif %}

        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}



    {% elif "currency" == row_type %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bold">
        {% endif %}
        
        {% if item.variation_switcher %}
            {% with price=item|shopturbo_price_range %}
                {% if price != None %}
                    {{ item.currency }}
                {% endif %}
            {% endwith %}
        {% else %}
            {% with price=item|get_item_price:"volume_price" %}
                {% if not price %}
                    {% with currency=item|get_item_price:"currency" price=item|get_item_price:"price" %}
                        {% if currency and price %}
                            {{ currency }}
                        {% endif %}
                    {% endwith %}
                {% endif %}
            {% endwith %}
        {% endif %}

        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}


    {% elif "purchase_price" == row_type %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bold">
        {% endif %}
                {% if item.purchase_price != None %}

                    {% with currency=item|get_item_purchase_price:"currency" %}
                        {% if currency %}
                            {{ currency|convert_currency_to_symbol }}{{item.purchase_price|use_thousand_separator_string_with_currency:currency}}
                        {% endif %}
                    {% endwith %}
                {% endif %}
        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}

    {% elif "purchase_price_currency" == row_type %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bold">
        {% endif %}
        {% if item.purchase_price != None %}
            {% with currency=item|get_item_purchase_price:"currency" %}
                {% if currency %}
                    {{ currency }}
                {% endif %}
            {% endwith %}
        {% endif %}
        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}

    {% elif "tax" == row_type and not view_type %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bold">
        {% endif %}
                {% if item.variation_switcher %}
                    {% if item.tax %}
                        {{item.tax}}
                    {% else %}
                        0 %
                    {% endif %}
                {% else %}
                    {% if item|get_item_price:"tax" %}
                        {{item|get_item_price:"tax"}}%
                    {% else %}
                        0 %
                    {% endif %}
                {% endif %}
        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}

    {% elif "status" == row_type %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bold">
        {% endif %}
            {% translate_lang item.get_status_display LANGUAGE_CODE %}
        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}

    {% elif "currency" == row_type %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bold">
        {% endif %}

            {{ item.currency|upper }}

        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}

    {% elif "created_at" == row_type %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bold">
        {% endif %}
                {% date_format item.created_at 1 %}
        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}

    {% elif "updated_at" == row_type %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bold">
        {% endif %}
                {% date_format item.updated_at 1 %}
        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}

    {% elif "-item id" in row_type|lower %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bold">
        {% endif %}
            <div class="">
                {{item.id|item_platform_name:row_type}}
            </div>
        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}

    {% elif "-sku" in row_type|lower %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bold">
        {% endif %}
            <div class="">
                {{item.id|item_platform_name:row_type}}
            </div>
        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}

    {% elif "-variant id" in row_type|lower %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bold">
        {% endif %}
            <div class="">
                {{item.id|item_platform_name:row_type}}
            </div>
        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}

    {% elif "inventory__" in row_type|lower %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bold">
        {% endif %}
            <div class="fw-bold">

                {% with row_type|cut:"inventory__"|cut:"_amount"|cut:"_inventory" as inventory_col %}
                    {{item|get_item_inventory_amount:inventory_col}}
                {% endwith %}

            </div>
        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}

    {% elif "platform_ids" in row_type|lower %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bolder">
        {% endif %}
            <div class="">
                    {% for platform_id in item|get_item_platforms %}
                        <div class="fw-bold mb-1">
                            <span class="badge bg-gray-500">{{platform_id}}</span>
                        </div>
                    {% endfor %}
            </div>
        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}

    {% elif "| child" in row_type %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bolder text-nowrap">
        {% endif %}
            {% with child_properties=item|checking_property_child %}
                {% for child_property in child_properties %}
                    <div class="fw-bold">
                        <a class="{% include "data/utility/table-link.html" %} manage-full-wizard-button item_{{item_.id}}"
                            hx-get="{% url 'shopturbo_load_drawer' %}"
                            hx-vals = '{"drawer_type":"item-manage", "item_id":"{{child_property.id}}", "view_id":"{{view_id}}", "page": "{{page}}", "module": "{{menu_key}}" }'
                            hx-target="#manage-full-drawer-content"
                            hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"
                            hx-trigger="click"
                        >
                            {% get_object_display child_property 'commerce_items' %}
                        </a>
                    </div>
                {% endfor %}
            {% endwith %}
        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}
    {% elif 'component_quantity' in row_type %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bold">
        {% endif %}
            <div class="fw-bold">
            {% if parent_group_id %}
                {% with component_quantity=row_type|split:'|'|last %}
                {% with parent_group_id_component_id=parent_group_id|join_id:item.id %}
                    {{ component_quantity|get_component_quantity:parent_group_id_component_id }}
                {% endwith %}
                {% endwith %}
            {% endif %}
            </div>
        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}
    
    {% elif "warehouse_inventory_amount" == row_type %}
        {% if as_sub_cell %}
        <div>
        {% else %}
        <td class="fw-bold">
        {% endif %}
            <div hx-get="{% url 'warehouse_inventory_amount' %}?object_id={{item.id}}&page_group_type=commerce_items"
            hx-trigger="load"
            ></div>
        {% if as_sub_cell %}
        </div>
        {% else %}
        </td>
        {% endif %}



    {% else %}
        {% if view_type == 'price_table' %}
            {% include "data/shopturbo/item-price-view-row-detail.html" %}
        {% else %}
        {% comment %} Custom  {% endcomment %}
            {% if as_sub_cell %}
            <div>
            {% else %}
            <td>
            {% endif %}
                {% include "data/shopturbo/items/custom-prop-cell-partial.html" %}
            {% if as_sub_cell %}
            </div>
            {% else %}
            </td>
            {% endif %}
        {% endif %}
    {% endif %}

{% endfor %}