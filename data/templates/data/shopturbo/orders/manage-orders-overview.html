{% load tz %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}
{% get_holidays as HOLIDAYS %}


<div class="fv-rowd-flex flex-column mb-8">
    <div class="d-flex justify-content-between">
        <div>
            <label class="{% include 'data/utility/form-label.html' %}">
                <span class="">
                {% if LANGUAGE_CODE == 'ja'%}
                受注ID
                {% else %}
                Order ID
                {% endif %}
                </span>
            </label>
            <div class="mb-2">
                <span class="fw-bolder" >{% if order.order_id %}{{order.order_id|stringformat:"04d"}} {% else %}"Unknown"{% endif %}</span>        
            </div>
        </div>

 
    </div>
    
</div>
{% if order.status == 'archived' %}
    <div class="card-body mt-0 mb-4">
        <div class="fv-rowd-flex flex-column">
            <h3>
                {% if LANGUAGE_CODE == 'ja'%}
                このレコードはアーカイブされています。このレコードを表示して使用するにはレコードを有効化してください。
                {% else %}
                This record was archived. Please activate to see and use this record.
                {% endif %}
            </h3>
            {% if permission|check_permission:'archive' %}
            <form method="POST" action="{% host_url 'shopturbo_manage_orders' order.id host 'app' %}">
                {% csrf_token %}
                <input type="hidden" name="module" value="{{module}}">
                <input type="hidden" name="object_type" value="{{object_type}}">
                <input class="source-url-input" type="hidden" name="source_url" value="">

                <div class="border-0">
                    <button type="submit" name="restore-orders" class="btn btn-success">
                        {% if LANGUAGE_CODE == 'ja'%}
                        有効化
                        {% else %}
                        Activate
                        {% endif %}
                    </button>
                </div>
            </form>
            {% endif %}
        </div>
    </div>
    <div class="pe-none user-select-none" hx-get="{% url 'load_drawer_orders' %}" hx-vals='{"drawer_type":"orders", "section":"blurry" }' hx-trigger="load" hx-swap="innerHTML"></div>
{% else %}
    
    <form id='shopturbo_manage_form-{{form_id}}' method="POST" class="" action="{% host_url 'shopturbo_manage_orders' order.id host 'app' %}" onsubmit="return validateForm(event)" enctype="multipart/form-data">
        {% csrf_token %}

        <input type="hidden" name="view_id" {% if view_id %}value="{{view_id}}"{% endif %} />
        <input type="hidden" name="page" {% if page %}value="{{page}}"{% endif %} />
        <input hidden name="set_id" value="{{set_id}}">
        <input type="hidden" name="module" value="{{module}}">
        <input type="hidden" name="object_type" value="{{object_type}}">
        <input type="hidden" name="source_url" {% if source_url %} value="{{source_url}}"{% endif %} >
        
        {% for property in properties.list_all %}
            {% if property == 'customer' %}
                <div class="fv-rowd-flex flex-column mb-8">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span class="">
                            {% if LANGUAGE_CODE == 'ja'%}
                            顧客情報
                            {% else %}
                            Customer
                            {% endif %}
                        </span>
                    </label>
                    
                    <div class="mb-2">
                        <select id="select-contact_and_company_{{order.id}}" name="contact_and_company" class="bg-white form-select form-select-solid border h-40px" 
                        {% if LANGUAGE_CODE == 'ja'%}
                        data-placeholder="連絡先もしくは企業を選択"
                        {% else %}
                        data-placeholder="Select Contact or Company"
                        {% endif %}
                        data-allow-clear="true"
                        onchange="checkPriceTable(this.value); showCustomerPoints(this.value)"
                        > 
                            {% if order.contact %}
                                <option value="{{order.contact.id}}" selected>
                                    #{{order.contact.contact_id|stringformat:"04d"}} | {{order.contact|display_contact_name:LANGUAGE_CODE}} ({% if LANGUAGE_CODE == 'ja'%}連絡先{% else %}Contact{% endif %})
                                </option>
                            {% endif %}
                            {% if order.company %}
                                <option value="{{order.company.id}}" selected>
                                    #{{order.company.company_id|stringformat:"04d"}} | {{order.company.name}} ({% if LANGUAGE_CODE == 'ja'%}企業{% else %}Company{% endif %})
                                </option>
                            {% endif %}
                        </select>
                    </div>
                    <div id="customer-points" class="mt-2"></div>
                </div>
                <script>
                    function checkPriceTable(selectedValue) {
                        if (!selectedValue) return;
                        
                        $.ajax({
                            url: "{% host_url 'price_table_checking' host 'app' %}",
                            method: 'POST',
                            data: {
                                contact_or_company_id: selectedValue,
                                csrfmiddlewaretoken: getCsrfToken()
                            },
                            success: function(response) {
                                if (response.has_price_table) {
                                    console.log("check item")
                                    triggerItemChecker();
                                }
                            },
                            error: function(error) {
                                console.error('Error checking price table:', error);
                            }
                        });
                    }
                    
                    function showCustomerPoints(selectedValue) {
                        if (!selectedValue) return;
                        
                        $.ajax({
                            url: "{% host_url 'fetch_customer_points' host 'app' %}",
                            method: 'POST',
                            data: {
                                contact_id: selectedValue,
                                csrfmiddlewaretoken: getCsrfToken()
                            },
                            success: function(response) {
                                if (response.points) {
                                    $('#customer-points').html(`
                                        <label class="{% include 'data/utility/form-label.html' %}">
                                            ${response.name} {% if LANGUAGE_CODE == 'ja' %}(ポイント): {% else %}(Points): {% endif %}
                                        </label>
                                        <input type="text" name="customer_point" class="form-control" value="${response.points}">
                                    `);
                                } else {
                                    $('#customer-points').html('');
                                }
                            },
                            error: function(error) {
                                console.error('Error fetching customer points:', error);
                            }
                        });
                    }
                    
                    function getCsrfToken() {
                        return document.querySelector('[name=csrfmiddlewaretoken]').value;
                    }

                    $(document).ready(function() {
                        showCustomerPoints(document.getElementById('select-contact_and_company_{{order.id}}').value);
                    });
                </script>
                
            {% elif property|lower in association_label_list and property != 'customer'%}  {% comment %} customer already handled by above code {% endcomment %}
                {% comment %} htmx {% endcomment %}
                <div hx-get="{% url 'load_association_label_form' %}" 
                    hx-vals='{"obj_id":"{{order.id}}", "property":"{{property}}", "page_group_type":"commerce_orders" }' 
                    hx-trigger="load" hx-swap="outerHTML">
                </div>
                

            {% elif property == 'order_at' %}
                <div class="fv-rowd-flex flex-column mb-8">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span class="">
                            {% if LANGUAGE_CODE == 'ja'%}
                            受注日時
                            {% else %}
                            Order Time
                            {% endif %}
                        </span>
                    </label>
                    
                    <div class="mb-2">
                        
                        {% timezone workspace.timezone %}
                            {% if LANGUAGE_CODE == 'ja' %}
                            <input autocomplete="off" type="text" name='order_at' placeholder="受注日時" value="{{order.order_at|date:"Y年m月d日 H:i"}}" class="form-control " ></input>
                            {% else %}
                            <input autocomplete="off" type="text" name='order_at' placeholder="Order Time" value="{{order.order_at|date:"Y-m-d H:i"}}" class="form-control " ></input>
                            {% endif %}
                        {% endtimezone %}
                    
                    </div>
                </div>

                <script>
                    $(function() {
                        $('input[name="order_at"]').daterangepicker({
                        singleDatePicker: true,
                        timePicker: true, // Enable time picker
                        autoUpdateInput: false,
                        showDropdowns: true,
                        drops: "auto",
                        locale: {
                            {% if LANGUAGE_CODE == 'ja' %}
                            cancelLabel: 'クリア',
                            format: 'YYYY年MM月DD日 HH:mm', // Japanese date format
                            separator: ' 〜 ',
                            applyLabel: '選択',
                            cancelLabel: 'キャンセル',
                            fromLabel: 'From',
                            toLabel: 'To',
                            customRangeLabel: 'カスタム範囲',
                            daysOfWeek: ['日', '月', '火', '水', '木', '金', '土'],
                            monthNames: [
                                '1月', '2月', '3月', '4月', '5月', '6月',
                                '7月', '8月', '9月', '10月', '11月', '12月'
                            ],
                            {% else %}
                            format: "Y-M-DD HH:mm",
                            {% endif %}
                        },
                        isCustomDate: function(date) {
                            
                            var holidays = {{HOLIDAYS|safe}}
                            // Convert the holiday dates to moment objects for comparison
                            var holidayMoments = holidays.map(function(holiday) {
                                return moment(holiday);
                            });

                            // Check if the current date is a holiday
                            for (var i = 0; i < holidayMoments.length; i++) {
                                if (date.isSame(holidayMoments[i], 'day')) {
                                    return 'sunday-highlight';
                                }
                            }

                            // Add a custom style directly to Sundays
                            if (date.day() === 0 || date.day() === 6) { // 0 represents Sunday
                                return 'sunday-highlight';
                            }
                            
                            return '';
                    
                        }
                        });
                        $('input[name="order_at"]').on('apply.daterangepicker', function(ev, picker) {
                            {% if LANGUAGE_CODE == 'ja' %}
                            $(this).val(picker.startDate.format('YYYY年MM月DD日 HH:mm'));
                            {% else %}
                            $(this).val(picker.startDate.format('YYYY-MM-DD HH:mm'));
                            {% endif %}
                        });
                        {% comment %} $('input[name="order_at"]').on('cancel.daterangepicker', function(ev, picker) {
                            $(this).val('');
                        }); {% endcomment %}
                    }); 
                </script>

            {% elif property == 'owner' %}
                <div class="owner-form-{{object_type}} mb-5">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span class="min-w-100px">
                            {% if LANGUAGE_CODE == 'ja'%}
                            所有者
                            {% else %}
                            Owner 
                            {% endif %}
                        </span>
                    </label>
                    <select data-allow-clear="true" id="owner-input-{{object_type}}-{{form_id}}" class="h-40px form-select form-select-solid border bg-white select2-this" data-control="select2" name="owner" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}所有者{% else %}Owner{% endif %}">
                        <option></option>
                        {% if order.id and order.owner and order.owner.user %}
                            <option value="{{order.owner.user.username}}" selected>
                                {{order.owner.user.first_name}} - {{order.owner.user.email}}
                            </option>
                        {% endif %}
                        {% for member in permission|get_scope_from_permission:'edit'|get_users_by_scope:user %}
                            {% if not order.owner or member != order.owner.user %}
                            <option value="{{member.username}}">
                                {{member.first_name}} - {{member.email}}
                            </option>
                            {% endif %}
                        {% endfor %}
                    </select>

                    <script>
                        $('#owner-input-{{object_type}}-{{form_id}}').select2()
                    </script>
                </div>
            
            {% elif property == 'total_price' or property == 'line_item' %} {% comment %} Properties: total_price + items + tax {% endcomment %}
            
                <label class="{% include 'data/utility/form-label.html' %}">
                    <span class="required">
                    {% if LANGUAGE_CODE == 'ja'%}
                    商品項目
                    {% else %}
                    Line Items
                    {% endif %}
                    </span>
                </label>

                <div class="mb-6 form-check form-switch form-check-custom form-check-solid ">
                    <input id="item-checker-{{form_id}}" name="item-checker" class="form-check-input" type="checkbox" {% if order.order_type == 'manual_order' %} checked {% endif %} 
                        hx-get="{% host_url 'load_drawer_orders' host 'app' %}"
                        hx-target="#select-content-{{form_id}}"
                        hx-trigger="load-manual,change,click-virtual"
                        hx-indicator=".loading-spinner"
                        hx-vals='js:{"drawer_type":"orders","type":"select-content","order":"{{order.id}}", "item-checker":document.getElementById("item-checker-{{form_id}}").checked, "currency":document.getElementById("currency-select").value, "set_id": "{{set_id}}", "view_id":"{% if view_id %}{{view_id}}{% endif %}", "form_id":"{{form_id}}"}'
                        onchange="itemChangeHandler(this)"
                        hx-on::before-request="document.getElementById('select-content-{{form_id}}').innerHTML = ''"
                    >
                    <label class="form-check-label fw-semibold text-gray-700 ms-3" for="allowchanges">
                        {% if LANGUAGE_CODE == 'ja' %}
                        クイックエントリー
                        {% else %}
                        Quick Entry
                        {% endif %}
                        </label>
                </div>
                    
                <div id="currency-{{form_id}}" class="fv-rowd-flex flex-column mb-8 {% if order.order_type != 'item_order' %} d-none {% endif %} ">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span class="min-w-100px">
                            {% if LANGUAGE_CODE == 'ja'%}
                            通貨
                            {% else %}
                            Currency
                            {% endif %}
                        </span>
                    </label>
                    
                    <div class="mb-2">
                        <select id="currency-select" name="currency" class="bg-white form-select form-select-solid border h-40px select2-this" 
                        {% if LANGUAGE_CODE == 'ja'%}
                        data-placeholder="通貨の選択"
                        {% else %}
                        data-placeholder="Select Currency"
                        {% endif %}
                        >  
                        {% if workspace.currencies %}
                            <optgroup
                                data-group-id="default"
                                {% if LANGUAGE_CODE == 'ja'%}
                                label="デフォルト通貨"
                                {% else %}
                                label="Default Currency"
                                {% endif %}
                            >
                                {% for currency in workspace.currencies|string_list_to_list %}
                                    {% if forloop.counter == 1 %}
                                        <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>                         
                                    {% elif order.currency == currency %}
                                        <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>                         
                                    {% else %}
                                        <option value="{{currency}}">{{currency|get_currencies_text_symbol}}</option>                         
                                    {% endif %}
                                {% endfor %}

                            </optgroup>
                            {% endif %}

                            <optgroup
                                data-group-id="all"
                                {% if LANGUAGE_CODE == 'ja'%}
                                label="すべての通貨"
                                {% else %}
                                label="All Currency"
                                {% endif %}
                            >
                            {% for currency in currency_model %}
                                {% if not currency.0 in workspace.currencies|string_list_to_list %}
                                        {% if order.currency == currency.0%}
                                        <option value="{{currency.0}}" selected >{{currency.1}}</option>
                                        {% elif not order.currency and not workspace.currencies and LANGUAGE_CODE == 'en' and currency.0 == 'USD' %}
                                        <option value="{{currency.0}}" selected >{{currency.1}}</option>
                                        {% elif not order.currency and not workspace.currencies and LANGUAGE_CODE == 'ja' and currency.0 == 'JPY' %}
                                        <option value="{{currency.0}}" selected >{{currency.1}}</option>
                                        {% else %}
                                        <option value="{{currency.0}}">{{currency.1}}</option>
                                        {% endif %}
                                {% endif %}
                            {% endfor %}
                            </optgroup>

                        </select>
                    </div>
                </div>
                
                <script>
                    $(document).ready(function() {
                        var currency_container = document.getElementById('currency-{{form_id}}');

                        if (currency_container) {
                            // Find the closest previous or next sibling that is #item-checker
                            var itemChecker = currency_container.previousElementSibling?.querySelector('#item-checker-{{form_id}}') ||
                                            currency_container.nextElementSibling?.querySelector('#item-checker-{{form_id}}');
                            console.log(itemChecker)
                            if (itemChecker) {
                                
                                let newHxVals = {
                                    'drawer_type': 'orders',
                                    'type': 'select-content',
                                    "order":"{{order.id}}",
                                    {% if order.order_type == 'manual_order' %} 
                                    'item-checker': true,  // Update checkbox state
                                    {% else %}
                                    'item-checker': false,  // Update checkbox state
                                    {% endif %}
                                    'currency': document.getElementById('currency-select').value,
                                     "set_id": "{{set_id}}", 
                                     "view_id":"{% if view_id %}{{view_id}}{% endif %}",
                                     "form_id": "{{form_id}}"
                                };

                                itemChecker.setAttribute('hx-vals', `js:${JSON.stringify(newHxVals)}`);

                                htmx.trigger(itemChecker, 'load-manual');
                            }
                        }

                    });
                    $('#currency-select').on('change', function (e) {
                        itemChangeHandler(document.getElementById('item-checker-{{form_id}}'))
                        document.getElementById('item-checker-{{form_id}}').dispatchEvent(new Event('click-virtual'));
                        
                        registered_discount = document.getElementById('order-tab-registered-discounts-{{form_id}}')
                        if (registered_discount){
                            registered_discount.click()
                        }
                    })
                    
                </script>

                {% comment %} Loading indicator {% endcomment %}
                <div class="loading-spinner ms-2 mb-2">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>

                <div id="select-content-{{form_id}}"></div>

                <div class="d-flex justify-content-end">
                    <div class="pb-6">
                        <label class="{% include 'data/utility/form-label.html' %} justify-content-end">
                            <span >
                                {% if LANGUAGE_CODE == 'ja'%}
                                税抜き合計金額:
                                {% else %}
                                Total Price Without Tax:
                                {% endif %}
                            </span>
                            <span class="ps-3 pe-1"  id = 'total_price_without_tax-{{form_id}}'>
                                0
                            </span>
                            <span id = "calculate_currency_1">
                                
                            </span>
                        </label>
                        <label class="{% include 'data/utility/form-label.html' %} justify-content-end">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}
                                合計金額：
                                {% else %}
                                Total Price:
                                {% endif %}
                            </span>
                            <span class="ps-3 pe-1" id = 'total_price-{{form_id}}'>
                                0
                            </span>
                            <span id = "calculate_currency_2">
                                
                            </span>
                        </label>
                    </div>
                </div>
            
            {% elif property == 'delivery_status' %}
                <div class="fv-rowd-flex flex-column mb-8">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span class="min-w-100px">
                            {% if LANGUAGE_CODE == 'ja'%}
                            ステータス
                            {% else %}
                            Status
                            {% endif %}
                        </span>
                    </label>
                    
                    
                    <div class="mb-2">
                        <select name="delivery_status" class="bg-white form-select form-select-solid border h-40px select2-this" 
                        {% if LANGUAGE_CODE == 'ja'%}
                        data-placeholder="ステータス"
                        {% else %}
                        data-placeholder="Status"
                        {% endif %}
                        data-allow-clear="true"
                        >  

                        <option></option>
                        {% if 'delivery_status'|get_custom_property_object:order %}
                            {% with value_map_label='delivery_status'|get_custom_property_object:order|get_attr:'value'|string_list_to_list %}
                                {% for value, label in value_map_label.items %}
                                    <option value="{{value}}" {% if order.delivery_status == value %}selected{% endif %}>
                                        {{label}}
                                    </option>
                                {% endfor %}
                            {% endwith %}
                        {% else %}
                            {% for delivery_status in SHOPTURBO_ORDER_DELIVERY_STATUS %}
                                <option value="{{delivery_status.0}}" 
                                    {% if order.delivery_status ==  delivery_status.0%}
                                    selected
                                    {% endif %}
                                >
                                    {{delivery_status.0|display_status_orders:request}}
                                </option>
                            {% endfor %}
                        {% endif %}
                        </select>
                    </div>
                </div>

            {% elif 'memo' in property|lower %}
                <div class="fv-rowd-flex flex-column mb-8">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span>
                            {% if LANGUAGE_CODE == 'ja'%}
                            備考
                            {% else %}
                            Notes
                            {% endif %}
                        </span>
                    </label>

                    <div class="notes-container">
                        <textarea 
                            wrap="off" 
                            placeholder="{% translate_lang "Bank account, etc." LANGUAGE_CODE %}"
                            class="rounded form-control w-100 autosize-this"
                            style="resize: none; overflow: hidden;"
                            name="memo"
                        >{% if order.id %}{% if order.memo %}{{order.memo}}{% endif %}{% else %}{% if value_app_setting_note %}{{value_app_setting_note}}{% endif %}{% endif %}</textarea>
                    </div>
                </div>
            {% elif '-order id' in property|lower %}
                {% with property_lower=property|lower %}
                    {% with value_order_name=order.id|order_platform_name:property_lower %}
                        {% with value_order_id=order.id|order_platform_order_id:property_lower %}
                            {% with channel_column=property|search_channel_objects:request %}
                                {% if value_order_name %}
                                <div class="fv-rowd-flex flex-column mb-8">
                                    <label class="{% include 'data/utility/form-label.html' %}">
                                        <span class="">
                                            {{ channel_column|slice:":-9" }}{% if LANGUAGE_CODE == 'ja'%} - 名{% else %} - Name{% endif %}
                                        </span>
                                    </label>
                                    <div class="mb-2">
                                        <input disabled name="{{property_lower}}" value="{{value_order_name}}" wrap="off" placeholder="ID" class="rounded form-control w-100">                  
                                    </div>
                                </div>
                                {% endif %}
                                <div class="fv-rowd-flex flex-column mb-8">
                                    <label class="{% include 'data/utility/form-label.html' %}">
                                        <span class="">
                                            {{ channel_column|slice:":-9" }}{% if LANGUAGE_CODE == 'ja'%} - 受注ID{% else %} - Order ID{% endif %}
                                        </span>
                                    </label>
                                    <div class="mb-2">
                                        <input disabled name="{{property_lower}}" value="{{value_order_id}}" wrap="off" placeholder="ID" class="rounded form-control w-100">                               
                                    </div>
                                </div>
                            {% endwith %}  
                        {% endwith %}  
                    {% endwith %}
                {% endwith %}

            {% elif '-auction id' in property|lower %}
                {% with property_lower=property|lower %}
                    {% with value_order_name=order.id|order_platform_name:property_lower %}
                        {% with value_order_id=order.id|order_platform_order_id:property_lower %}
                            {% with channel_column=property|search_channel_objects:request %}
                                {% if value_order_name %}
                                <div class="fv-rowd-flex flex-column mb-8">
                                    <label class="{% include 'data/utility/form-label.html' %}">
                                        <span class="">
                                            {{ channel_column|slice:":-9" }}{% if LANGUAGE_CODE == 'ja'%} - 名{% else %} - Name{% endif %}
                                        </span>
                                    </label>
                                    <div class="mb-2">
                                        <input disabled name="{{property_lower}}" value="{{value_order_name}}" wrap="off" placeholder="ID" class="rounded form-control w-100">                  
                                    </div>
                                </div>
                                {% endif %}
                                <div class="fv-rowd-flex flex-column mb-8">
                                    <label class="{% include 'data/utility/form-label.html' %}">
                                        <span class="">
                                            {{ channel_column|slice:":-9" }}{% if LANGUAGE_CODE == 'ja'%} - 受注ID{% else %} - Order ID{% endif %}
                                        </span>
                                    </label>
                                    <div class="mb-2">
                                        <input disabled name="{{property_lower}}" value="{{value_order_id}}" wrap="off" placeholder="ID" class="rounded form-control w-100">                               
                                    </div>
                                </div>
                            {% endwith %}  
                        {% endwith %}  
                    {% endwith %}
                {% endwith %}

            {% elif '-deal id' in property|lower %}
                {% with property_lower=property|lower %}
                    {% with value_order_name=order.id|order_platform_name:property_lower %}
                        {% with value_order_id=order.id|order_platform_order_id:property_lower %}
                            {% with channel_column=property|search_channel_objects:request %}
                                <div class="fv-rowd-flex flex-column mb-8">
                                    <label class="{% include 'data/utility/form-label.html' %}">
                                        <span class="{% include "data/utility/form-label.html" %}">
                                            {{ channel_column|slice:":-8" }}{% if LANGUAGE_CODE == 'ja'%} - 受注ID{% else %} - Order ID{% endif %} 
                                        </span>
                                        <div class="tw-w-[20px] tw-mr-2 mb-3 ms-3">
                                            {% with channel_account_id=property|search_channel_objects_account_id:request %}
                                            <a target="blank" href="https://app.hubspot.com/contacts/{{ channel_account_id }}/record/0-3/{{ value_order_id }}/">
                                                <?xml version="1.0" ?><!DOCTYPE svg  PUBLIC '-//W3C//DTD SVG 1.1//EN'  'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'><svg style="enable-background:new 0 0 512 512;" version="1.1" viewBox="0 0 512 512" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="_x31_68-hubspot"><g><path d="M266.197,216.109c-22.551,21.293-36.655,51.48-36.655,84.991c0,26.326,8.714,50.582,23.359,70.08    l-44.473,44.74c-3.953-1.438-8.176-2.245-12.579-2.245c-9.702,0-18.776,3.774-25.605,10.602    c-6.828,6.827-10.602,15.989-10.602,25.696c0,9.701,3.773,18.775,10.602,25.605c6.829,6.826,15.993,10.42,25.605,10.42    c9.703,0,18.777-3.505,25.695-10.42c6.829-6.83,10.602-15.994,10.602-25.605c0-3.774-0.538-7.369-1.707-10.873l44.923-45.102    c19.765,15.183,44.381,24.169,71.244,24.169c64.599,0,116.797-52.38,116.797-116.977c0-58.578-42.854-107.093-99.007-115.628    v-55.343c15.723-6.65,25.335-21.384,25.335-38.545c0-23.449-18.777-43.034-42.227-43.034c-23.448,0-41.956,19.585-41.956,43.034    c0,17.161,9.613,31.895,25.335,38.545v54.983c-13.655,1.887-26.593,6.019-38.362,12.219    c-24.796-18.778-105.565-76.997-151.746-112.126c1.078-3.953,1.798-8.085,1.798-12.397c0-25.875-21.113-46.898-47.078-46.898    c-25.875,0-46.898,21.023-46.898,46.898c0,25.965,21.023,46.988,46.898,46.988c8.805,0,16.98-2.606,24.078-6.828L266.197,216.109z     M346.606,363.095c-34.229,0-61.991-27.763-61.991-61.994c0-34.229,27.762-61.99,61.991-61.99c34.23,0,61.992,27.761,61.992,61.99    C408.599,335.332,380.837,363.095,346.606,363.095z" style="fill:#FF7A59;"/></g></g><g id="Layer_1"/></svg>
                                            </a>
                                            {% endwith %}
                                        </div>
                                    </label>
                                    <div class="mb-2">
                                        <input disabled name="{{property_lower}}" value="{% if value_order_name %}{{value_order_id}}{% elif value_order_id %}#{{value_order_name}}{% endif %}" wrap="off" placeholder="ID" class="rounded form-control w-100">                  
                                    </div>
                                </div>
                            {% endwith %}  
                        {% endwith %}  
                    {% endwith %}
                {% endwith %}
            
            {% else %}
                

                {% with CustomFieldName=OrdersCustomFieldMap|get_attr:property %}    
                    {% if CustomFieldName %}
                        <div class="fv-rowd-flex flex-column mb-8">
                            <div class="mb-4">
                                
                                <span class="{% include 'data/utility/form-label.html' %} {% if CustomFieldName.required_field %} required {% endif%}">
                                    {{CustomFieldName.name }}
                                </span>

                                {% include 'data/common/custom_field/custom-property.html' with CustomFieldName=CustomFieldName obj=order object_type=object_type %}

                            </div>
                        </div>
                    {% endif %}
                {% endwith %}

                

            {% endif %}

        {% endfor %}    
    
    </form>

        <div class="mb-4 d-flex flex-row gap-3">
            {% if permission|check_permission:'edit' %}
                <button id="update-item-orders-btn" name='update-item-orders' form="shopturbo_manage_form-{{form_id}}" type="submit" class="btn btn-dark flex-column-fluid h-40px"
                    >
                    {% if LANGUAGE_CODE == 'ja'%}
                    受注レコードを更新
                    {% else %}
                    Update Order
                    {% endif %}
                </button>
            {% endif %}

            <div>
                <form action="{% host_url 'create_pickup_list_from_order_form' host 'app' %}" method="POST">
                    {% csrf_token %}
                    <input type="hidden" name="order_ids" value="{{order.id}}">
                    <div class="btn-group w-100 min-w-250px" role="group">
                        <button class="flex-column-fluid btn btn-primary" type="submit">
                            {% if LANGUAGE_CODE == 'ja'%}
                            ダウンロード
                            {% else %}
                            Download
                            {% endif %} (PDF)
                        </button>

                        <button id="btnGroupDrop1" type="button" class="btn btn-light-primary dropdown-toggle p-2" data-bs-toggle="dropdown" aria-expanded="false">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#4CAF50" width="18px" height="18px"><path d="M0 0h24v24H0z" fill="none"/><path d="M6.3 9.3L12 15l5.7-5.7c.4-.4 1-.4 1.4 0s.4 1 0 1.4l-6 6c-.2.2-.5.3-.7.3s-.5-.1-.7-.3l-6-6c-.4-.4-.4-1 0-1.4s1-.4 1.4 0z"/></svg>
                        </button>
                        
                        <ul class="dropdown-menu" aria-labelledby="btnGroupDrop1">
                            {% for template in pdf_templates %}
                                <li>
                                    <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden text-click-white" 
                                        type="submit" name="template_select" value="{{template.id|to_str}}"
                                        >
                                        {% if template.name %}
                                            {{template.name}}
                                        {% else %}
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            {{template.master_pdf.name_ja}}
                                            {% else %}
                                            {{template.master_pdf.name_en}}
                                            {% endif %}
                                        {% endif %}
                                    </a>
                                </li>
                            {% endfor %}
                        </ul>
                    </div>
                </form>
            </div>
        </div>


        <div id="workflow-toast" class="toast fade hide position-fixed top-0 end-0 me-3 mt-3 z-index-3 bg-danger" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="3000" >
            <div class="toast-body h-50px d-flex align-items-center">
                <div class="w-75 ps-3" id="workflow-toast-message"></div>
                <button type="button" class="btn-close position-absolute end-0 me-3" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>

{% endif %}

<script>
    $(document).ready(function() {
        // Automatically trigger the item-checker on page load
        setTimeout(() => {
            var itemChecker = document.getElementById('item-checker-{{form_id}}');
            if (itemChecker) {
                htmx.trigger(itemChecker, 'load-manual');
            }
        }, 100);
    });
</script>
