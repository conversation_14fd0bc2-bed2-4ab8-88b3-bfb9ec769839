{% extends 'base.html' %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% block content %}

<style>
    table {
        width: auto;
    }
    
    td, th {
        white-space: nowrap;psci
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
</style>

<style>
    .search-wrapper {
        display: flex;
        align-items: center;
        position: relative;
        width: 24px;
        transition: width 0.4s ease-in-out, margin-right 0.4s ease-in-out, opacity 0.4s ease-in-out;
    }

    .search-wrapper.expanded {
        width: 200px; /* New width when expanded */
        margin-right: -0.5rem !important;
    }

    .search-wrapper input {
        display: none;
        width: 0;
        padding: 0;
        opacity: 0;
        transition: width 0.4s ease-in-out, margin-right 0.4s ease-in-out, opacity 0.4s ease-in-out;
    }

    .search-wrapper.expanded input {
        display: block;
        width: 100%;
        opacity: 1;
    }
    

    .search-icon-view {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
    }

    /* Tooltip container */
    .hover-tooltip {
        position: relative;
        display: inline-block;
    }

    /* Tooltip text */
    .hover-tooltip .hover-tooltip-text {
        visibility: hidden;
        width: 80px;
        background-color: #555;
        color: #fff;
        text-align: center;
        padding: 3px 0;
        border-radius: 8px;

        /* Position the hover-tooltip text */
        position: absolute;
        z-index: 1;
        top: 50%;
        right: 105%;
        transform: translateY(-50%);

        /* Fade in hover-tooltip */
        opacity: 0;
        transition: opacity 0.5s;
    }

    /* Show the hover-tooltip text when you mouse over the hover-tooltip container */
    .hover-tooltip:hover .hover-tooltip-text {
        visibility: visible;
        opacity: 0.9;
    }
</style>

{% include 'data/common/module-header-tabs.html' %}


{% if permission == 'hide' %}
    {% include 'data/static/no-access-page.html' %}
{% else %}
<div class="{% include "data/utility/table-container.html" %}">
    {% comment %} Views Part {% endcomment %}
    <div class="{% include "data/utility/tab-pane.html" %}" role="tabpanel" style="z-index:4 !important">
        <div class="{% include "data/utility/table-nav.html" %}">
            <div class="d-flex align-items-end justify-content-between w-100" id="inventory-view-container">
                <div class="w-50">
                    <div class="d-flex align-items-center">
                        <div class="nav-item me-1 d-flex align-items-center">
                            <a class="{% include "data/utility/view-menu-default.html" %}" 
                                type="button"
                                href = "{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">
                                {% if LANGUAGE_CODE == 'ja'%}
                                ビュー
                                {% else %}
                                View
                                {% endif %}
                            </a>
                            {% if not view_filter.view.title %}
                            <div class="text-gray-900 w-20px nav-item justify-content-center d-flex fs-6">
                                <button type="button" class="{% include "data/utility/view-plus-link.html" %} create-view-settings-button"
                                    hx-vals='{"module": "{{menu_key}}", "drawer_type":"shopturbo-view-settings","page": "inventory-transaction", "view_id":"{{view_filter.view.id}}"}'
                                    hx-get="{% url 'shopturbo_load_drawer' %}" 
                                    hx-indicator=".loading-drawer-spinner,.view-form"
                                    hx-target="#manage-contacts-view-settings-drawer"
                                    hx-trigger="click"
                                    hx-swap="innerHTML"
                                    >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                        <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                    </svg>
                                </button>
                            </div>
                            {% endif %}
                        </div>

                        {% comment %} Put Items here {% endcomment %}
                        {% include 'data/projects/partial-view-menu.html' %}

                        <div class="text-gray-900 nav-item fs-6">
                            <button type="button" class="{% include "data/utility/view-plus-link.html" %} create-view-settings-button" 
                                hx-vals='{"drawer_type":"shopturbo-view-settings","page": "inventory-transaction"}'
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-target="#manage-contacts-view-settings-drawer"
                                hx-swap="innerHTML">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="d-flex w-50 justify-content-end">
                    <div class="me-2">
                        <div class="mb-2 search-wrapper {% if search_q %}expanded {% endif %} hover-tooltip">
                            <span class="search-wrapper-tooltip hover-tooltip-text">
                                {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}
                            </span>
                            <div class="d-flex align-items-center">
                                <form id="filter-form-search" method="get" class="w-100">
                                    <div class="mb-0 d-flex position-relative" style="height: 26px;">
                                        <span class="svg-icon svg-icon-3 search-icon-view" onclick="toggleSearch()">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                                                <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                                            </svg>
                                        </span>
                                        <input
                                        id="base-search-input" type="text" name="q" class="bg-white form-control ps-12"
                                        value={% if search_q %} "{{ search_q }}" {% else %}""{% endif %}
                                        placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                                        onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                                        >
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="{% include "data/utility/table-button.html" %}">
                        <button class="{% include "data/utility/gray-header-button.html" %} create-view-settings-button" type="button"
                            hx-vals='{"drawer_type":"shopturbo-view-settings","page": "inventory-transaction", "view_id":"{{view_id}}", "download_view": true}'
                            hx-get="{% url 'shopturbo_load_drawer' %}" 
                            hx-target="#manage-contacts-view-settings-drawer"

                            >
                            <span class="svg-icon svg-icon-4">
                                <svg xmlns="http://www.w3.-listorg/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M21 22H3C2.4 22 2 21.6 2 21C2 20.4 2.4 20 3 20H21C21.6 20 22 20.4 22 21C22 21.6 21.6 22 21 22ZM13 13.4V3C13 2.4 12.6 2 12 2C11.4 2 11 2.4 11 3V13.4H13Z" fill="black"/>
                                    <path opacity="0.3" d="M7 13.4H17L12.7 17.7C12.3 18.1 11.7 18.1 11.3 17.7L7 13.4Z" fill="black"/>
                                </svg>
                            </span>
                            <span class="">
                                {% if LANGUAGE_CODE == 'ja'%}
                                ダウンロード
                                {% else %}
                                Download
                                {% endif %}
                            </span>
                        </button>    
                    </div>
                    
                    <button class="py-1 w-100px align-items-center d-flex btn btn-dark btn-md rounded-1 shopturbo-create-wizard-button" type="button"
                        hx-get="{% url 'shopturbo_load_drawer' %}" 
                        hx-vals = '{"drawer_type":"inventory", "type": "transactions"}'
                        hx-target="#shopturbo-create-drawer-content"
                        hx-indicator=".loading-drawer-spinner,#shopturbo-create-drawer-content" 
                        style="height: 26px;"
                        >
                        <span class="svg-icon svg-icon-4">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"/>
                                <rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"/>
                                <rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"/>
                            </svg>
                        </span>
                
                        <span class="fs-7 ps-1 fw-bolder w-85px">
                            {% if LANGUAGE_CODE == 'ja'%}
                            新規
                            {% else %}
                            New
                            {% endif %}
                        </span>
                    </button>    
                </div>
            </div>

            <div id="inventory-bulk-action-container" class="d-none">
                <span class="me-10">
                    <button class="py-1 mt-2 mb-1 rounded-1 btn btn-sm btn-light fw-bold" onclick="selectAllContacts()">
                        <span>
                            {% if LANGUAGE_CODE == 'ja'%}
                            すべて選択
                            {% else %}
                            Select All
                            {% endif %}
                        </span>
                    </button>
                    <button class="py-1 mt-2 mb-1 rounded-1 btn btn-sm btn-light fw-bold" onclick="deselectAllContacts()">
                        <span>
                            {% if LANGUAGE_CODE == 'ja'%}
                            選択を解除
                            {% else %}
                            Deselect All
                            {% endif %}
                        </span>
                    </button>
    
                    <script>
                        function selectAllContacts() {
                            selectTaskInputs = document.getElementsByClassName('inventory-selection')
                            for (var i = 0; i < selectTaskInputs.length; i++) {
                                selectTaskInputs[i].checked = true;
                            }
                        }
                        function deselectAllContacts() {
                            selectTaskInputs = document.getElementsByClassName('inventory-selection')
                            for (var i = 0; i < selectTaskInputs.length; i++) {
                                selectTaskInputs[i].checked = false;
                            }
                            document.getElementById('inventory-bulk-action-container').classList.add('d-none')
                            document.getElementById('inventory-view-container').classList.remove('d-none')
                        }
                    </script>
                </span>
                <button class="py-1 mt-2 mb-1 rounded-1 btn btn-sm btn-light-success fw-bold" data-bs-toggle="modal" data-bs-target="#manage_restore_bulk">
                    {% if LANGUAGE_CODE == 'ja'%}
                    有効化
                    {% else %}
                    Activate
                    {% endif %}
                </button>
                <button class="py-1 mt-2 mb-1 btn btn-sm btn-light-danger rounded-1 fw-bold" data-bs-toggle="modal" data-bs-target="#manage_delete_bulk">
                    {% if LANGUAGE_CODE == 'ja'%}
                    アーカイブ
                    {% else %}
                    Archive 
                    {% endif %}
                </button>

                <script>
                    document.body.addEventListener("hideTaskBulkActionBtn", function(evt){
                        document.getElementById('inventory-bulk-action-container').classList.add('d-none')
                        document.getElementById('inventory-view-contianer').classList.remove('d-none')
                    })
                </script>
            </div>

        </div>
    </div>
    {% comment %} End of Views {% endcomment %}



    <div class="mb-2 d-none" id="d-select-additional-options">
        <div class="px-5 pt-4 pb-3 text-center rounded status bg-light-dark">
            
            {% if LANGUAGE_CODE == 'ja'%}
            このページの全てのレコードが選択されました。 
            {% else %}
            All records on this page are selected. 
            {% endif %}

            <a onclick="toggleText()" 
                class="btn btn-dark" 
                data-bs-toggle="collapse" 
                id="select-additional-options-toggle" 
                role="button" 
                aria-expanded="false" 
                aria-controls="collapseExample">
                {% if LANGUAGE_CODE == 'ja'%}
                全てのレコードを選択する
                {% else %}
                Select all records
                {% endif %}

                </a>
        </div>
    </div>


    <form method="POST" id="order-form" class="">
        {% csrf_token %}
        <div class="py-10 pt-5">
            <table class="{% include "data/utility/table.html" %}">
                <thead class="{% include "data/utility/table-header.html" %}">
                    <tr class="align-middle">
                        <th style="border: 1px solid rgba(0, 0, 0, 0.3); border-right: none; text-align: center" rowspan="2"> {% if LANGUAGE_CODE == 'ja'%}日付{% else %}Date{% endif %}</th>
                        <th style="border: 1px solid rgba(0, 0, 0, 0.3); text-align: center;" rowspan="2"> {% if LANGUAGE_CODE == 'ja'%}商品ID{% else %}Item ID{% endif %}</th>
                        <th style="border: 1px solid rgba(0, 0, 0, 0.3); text-align: center;" rowspan="2"> {% if LANGUAGE_CODE == 'ja'%}入出庫ID{% else %}Transaction ID{% endif %}</th>
                        <th style="border: 1px solid rgba(0, 0, 0, 0.3); text-align: center;" rowspan="2"> {% if LANGUAGE_CODE == 'ja'%}摘要{% else %}Type{% endif %}</th>
                        <th style="border: 1px solid rgba(0, 0, 0, 0.3); text-align: center;" colspan="3"> {% if LANGUAGE_CODE == 'ja'%} 受入 {% else %}Stock In{% endif %}</th>
                        <th style="border: 1px solid rgba(0, 0, 0, 0.3); text-align: center;" colspan="3"> {% if LANGUAGE_CODE == 'ja'%} 払出 {% else %}Stock Out{% endif %}</th>
                        <th style="border: 1px solid rgba(0, 0, 0, 0.3); text-align: center;" colspan="3"> {% if LANGUAGE_CODE == 'ja'%} 残高 {% else %} Balance{% endif %}</th>
                    </tr>
                    <tr class="align-middle">
                        <th style="min-width:100px;border: 1px solid rgba(0, 0, 0, 0.3); text-align: center;"> {% if LANGUAGE_CODE == 'ja'%}数量{% else %}Quantity{% endif %}</th>
                        <th style="min-width:100px;border: 1px solid rgba(0, 0, 0, 0.3); text-align: center;"> {% if LANGUAGE_CODE == 'ja'%}単価{% else %}Unit Price{% endif %}</th>
                        <th style="min-width:100px;border: 1px solid rgba(0, 0, 0, 0.3); text-align: center;"> {% if LANGUAGE_CODE == 'ja'%}金額{% else %}Total Value{% endif %}</th>
                        <th style="min-width:100px;border: 1px solid rgba(0, 0, 0, 0.3); text-align: center;"> {% if LANGUAGE_CODE == 'ja'%}数量{% else %}Quantity{% endif %}</th>
                        <th style="min-width:100px;border: 1px solid rgba(0, 0, 0, 0.3); text-align: center;"> {% if LANGUAGE_CODE == 'ja'%}単価{% else %}Unit Price{% endif %}</th>
                        <th style="min-width:100px;border: 1px solid rgba(0, 0, 0, 0.3); text-align: center;"> {% if LANGUAGE_CODE == 'ja'%}金額{% else %}Total Value{% endif %}</th>
                        <th style="min-width:100px;border: 1px solid rgba(0, 0, 0, 0.3); text-align: center;"> {% if LANGUAGE_CODE == 'ja'%}数量{% else %}Quantity{% endif %}</th>
                        <th style="min-width:100px;border: 1px solid rgba(0, 0, 0, 0.3); text-align: center;"> {% if LANGUAGE_CODE == 'ja'%}単価{% else %}Unit Price{% endif %}</th>
                        <th style="min-width:100px;border: 1px solid rgba(0, 0, 0, 0.3); text-align: center;"> {% if LANGUAGE_CODE == 'ja'%}金額{% else %}Total Value{% endif %}</th>
                    </tr>
                </thead>
                <tbody class="fs-6">
                    <tr>
                        <td class="text-center fw-bold">
                            <span class="ps-5 pe-2">
                                {{from_date}}
                            </span>
                        </td>
                        <td class="text-left fw-bold text-nowrap special-col">
                            
                            
                        </td>
                        <td class="text-center fw-bold" style="border-right: 1px solid !important; border-right-color: rgb(234, 234, 234) !important;">
                            {% if LANGUAGE_CODE == 'ja'%}
                            期間開始
                            {% else %}
                            Beginning Balance
                            {% endif %}
                            
                        </td> 
                        <td class="text-center fw-bold">
                            
                        </td> 
                        <td class="text-center fw-bold">
                            
                        </td>
                        <td class="text-center fw-bold">
                            
                        </td>
                        <td class="text-center fw-bold">
                            
                        </td> 
                        <td class="text-center fw-bold">
                            
                        </td>
                        <td class="text-center fw-bold">
                            
                        </td>
                        <td class="text-center fw-bold">
                            
                        </td>

                        <td class="text-center fw-bold">
                           {{initial_amount}}
                        </td> 
                        <td class="text-center fw-bold">
                            {{initial_price}}
                        </td>
                        <td class="text-center fw-bold">
                            {{initial_total}}
                        </td>
                        
                    </tr>

                    {% for transaction in fifo_transactions %}
                    {% if transaction.transaction_id != 'initial' %}
                    <tr>
                        <td class="text-center">
                            <span class="ps-5 fw-bold pe-2">
                                {{transaction.date}}
                            </span>
                        </td>
                        <td class="text-center fw-bold text-nowrap">
                            {% if transaction.item_id %}
                            <a class="{% include "data/utility/table-link-shopturbo.html" %}"
                                hx-get="{% url 'shopturbo_load_drawer' %}"
                                hx-vals = '{"drawer_type":"item-manage", "item_id":"{{transaction.item_pk}}" }'
                                hx-target="#shopturbo-drawer-content"
                                hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"
                                hx-trigger="click"
                            >
                                #{{ transaction.item_id}} - 
                                {{ transaction.item_name|truncate_long_text}}
                            </a>
                            {% endif %}
                        </td>
                        <td class="text-center fw-bold text-nowrap special-col" style="border-right: 1px solid !important; border-right-color: rgb(234, 234, 234) !important;">
                            {% if transaction.transaction_id %}
                            <a id="profile_wizard_button" class="mb-0 text-center cursor-pointer text-dark text-hover-primary fw-bold shopturbo-manage-wizard-button" 
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-vals = '{"drawer_type":"inventory-transaction-manage", "transaction_id":"{{transaction.transaction_pk}}" }'
                                hx-target="#shopturbo-drawer-content"
                                hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"  
                                hx-trigger="click"
                            >
                                #{{ transaction.transaction_id|stringformat:"04d" }}
                            </a>
                            {% endif %}
                        </td>
                        <td class="text-center fw-bold" >
                            {% if transaction.transaction_type == 'in'%}
                                {% if LANGUAGE_CODE == 'ja'%}
                                仕入
                                {% else %}
                                Purchase
                                {% endif %}
                            {% elif transaction.transaction_type == 'out' %}
                                {% if LANGUAGE_CODE == 'ja'%}
                                販売
                                {% else %}
                                Sales
                                {% endif %}
                            {% endif %}
                        </td> 
                        <td class="text-center fw-bold">
                            {{transaction.input_quantity}}
                        </td> 
                        <td class="text-center fw-bold">
                            {{transaction.input_unit_price}}
                        </td>
                        <td class="text-center fw-bold">
                            {{transaction.input_amount}}
                        </td>
                        <td class="text-center fw-bold">
                            {{transaction.output_quantity}}
                        </td> 
                        <td class="text-center fw-bold">
                            {{transaction.output_unit_price}}
                        </td>
                        <td class="text-center fw-bold">
                            {{transaction.output_amount}}
                        </td>
                        <td class="text-center fw-bold">
                            {{transaction.balance_quantity}}
                        </td> 
                        <td class="text-center fw-bold">
                            {{transaction.balance_unit_price}}
                        </td>
                        <td class="text-center fw-bold">
                            {{transaction.balance_amount}}
                        </td>
                        
                    </tr>
                    {% endif %}
                    {% endfor %}
                    <tr>
                        <td class="text-center fw-bold">
                            <span class="ps-5 pe-2">
                                {{end_date}}
                            </span>
                        </td>
                        
                        <td class="text-left fw-bold text-nowrap special-col">
                        </td>
                        <td class="text-center fw-bold" style="border-right: 1px solid !important; border-right-color: rgb(234, 234, 234) !important;">
                            {% if LANGUAGE_CODE == 'ja'%}
                            合計
                            {% else %}
                            Total
                            {% endif %}
                            
                        </td> 
                        <td class="text-center fw-bold">
                            
                        </td> 
                        <td class="text-center fw-bold">
                            
                        </td>
                        <td class="text-center fw-bold">
                            
                        </td>
                        <td class="text-center fw-bold">
                            
                        </td> 
                        <td class="text-center fw-bold">
                            
                        </td>
                        <td class="text-center fw-bold">
                            
                        </td> 

                        <td class="text-center fw-bold">
                            
                        </td>
                        <td class="text-center fw-bold">
                            {% if total_quantity%}
                                {{total_quantity}}
                            {% else%}
                                0
                            {% endif%}
                        </td> 
                        <td class="text-center fw-bold">
                            {% if total_unit_price%}
                                {{total_unit_price | floatformat:2}}
                            {% else %}
                                0
                            {% endif%}
                            
                        </td>
                        <td class="text-center fw-bold">
                            {% if total_value%}
                                {{total_value | floatformat:2}}
                            {% else %}
                                0
                            {% endif%}
                        </td>                        
                    </tr>

                </tbody> 
            </table>
            

            <input name='flag_all' class="flag_all" hidden></input>
            
            <div class="modal fade" tabindex="-1" id="manage_delete_bulk">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="pb-0 border-0 modal-header justify-content-end">
                            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                            </div>
                        </div>
                        <div class="pb-0 modal-body">
                            <div class="text-center mb-13">
                                <h3 class="modal-title">
                                    
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    一括アーカイブの確認
                                    {% else %}
                                    Bulk Archive Confirmation
                                    {% endif %}
                                
                                </h3>
                            </div>
                            <div class="border-bottom">
                                <div class="{% include "data/utility/form-div.html" %}">
                                    <label class="mb-2 d-flex align-items-center fs-6 fw-bold">
                                        <span class="">
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            これらのインベントリをアーカイブしてもよろしいですか??
                                            {% else %}
                                            Are you sure to archive these inventory?
                                            {% endif %}
                                        </span>
                                    </label>
                            
                                </div>
                            </div>
                        </div>
                        
        
                        <div class="border-0 modal-footer">
                            <button name="bulk_delete_items" type="submit" class="btn btn-danger">
                                
                                {% if LANGUAGE_CODE == 'ja'%}
                                    アーカイブ
                                    {% else %}
                                    Archive
                                    {% endif %}
                                </button>
                            
                            </button>
                            <a data-bs-dismiss="modal" class="btn btn-dark">
                                {% if LANGUAGE_CODE == 'ja'%}
                                キャンセル
                                {% else %}
                                Cancel
                                {% endif %}
                            </a>
                        </div>
                    </div>
                </div>
            </div> 

            <div class="modal fade" tabindex="-1" id="manage_restore_bulk">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="pb-0 border-0 modal-header justify-content-end">
                            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                            </div>
                        </div>
                        <div class="pb-0 modal-body">
                            <div class="text-center mb-13">
                                <h3 class="modal-title">
                                    
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    一括有効化の確認
                                    {% else %}
                                    Bulk Activate Confirmation
                                    {% endif %}
                                
                                </h3>
                            </div>
                            <div class="border-bottom">
                                <div class="{% include "data/utility/form-div.html" %}">
                                    <label class="mb-2 d-flex align-items-center fs-6 fw-bold">
                                        <span class="">
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            これらの在庫を有効化してもよろしいですか?
                                            {% else %}
                                            Are you sure to activate these inventory?
                                            {% endif %}
                                        </span>
                                    </label>
                               
                                </div>
                            </div>
                        </div>
                        
        
                        <div class="border-0 modal-footer">
                            <button name="bulk_restore_items" type="submit" class="btn btn-success">
                                {% if LANGUAGE_CODE == 'ja'%}
                                有効化
                                {% else %}
                                Activate
                                {% endif %}
                            </button>
                            <a data-bs-dismiss="modal" class="btn btn-dark">
                                {% if LANGUAGE_CODE == 'ja'%}
                                キャンセル
                                {% else %}
                                Cancel
                                {% endif %}
                            </a>
                        </div>
                    </div>
                </div>
            </div> 

        </div>
    </form>

</div>


<script>

    $(document).ready(function() {
        $(".table-content").DataTable({
            scrollX:        true,
            scrollCollapse: true,
            fixedColumns:   {
                left: 3
            },
            ordering: false,
            searching: false,  // Hide the search bar
            paging: false,      // Hide pagination
            info: false,        // Hide the information text
            language: {
                emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
            }
        });
    });


    function toggleText() {
        var x = document.getElementById("select-additional-options-toggle");
        var toggle_data = x.getAttribute('toggle-data')
        if (toggle_data !== "true") {
                
                {% if LANGUAGE_CODE == 'ja'%}
                x.innerHTML = "選択を解除";
                {% else %}
                x.innerHTML = "Clear All";
                {% endif %}

          $(".flag_all").each(function(index, element) {
                element.value = true
            });

            x.setAttribute('toggle-data',"true")

        } else {

            x.setAttribute('toggle-data',"false")

            addcontactelem = document.getElementById("update-items");
            addcontactelem.classList.add("disabled");
            
            $('input[type=checkbox]').prop('checked', false);

            //Hide
            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.add("d-none")

            x.innerHTML = "Select All {{paginator.count}} contacts in this sections";
            
            $(".flag_all").each(function(index, element) {
                element.value = false
            });

        }
      }



    function select_all() {
        $('#selectAll').prop('checked', !$('#selectAll').prop('checked'));
        if ($('#selectAll').prop('checked') == true) {
            addcontactelem = document.getElementById("update-items");
            addcontactelem.classList.add("disabled");
            //downloadelem = document.getElementById("csv_download");
            //downloadelem.classList.add("disabled");
            
            $('input[type=checkbox]').prop('checked', false);

            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.add("d-none")

        } else {
            addcontactelem = document.getElementById("update-items");
            addcontactelem.classList.remove("disabled");
            //downloadelem = document.getElementById("csv_download");
            //downloadelem.classList.remove("disabled");

            $('input[type=checkbox]').prop('checked', true);

            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.remove("d-none")

        }
    }


    var contactChecked = {}
    var first_id_check_box = null;
    const checking_checkbox = (elem,event) => {
        if (elem.checked){
            contactChecked[elem.id] = elem.id;
            addcontactelem = document.getElementById("update-inventory");

            if (event.shiftKey) {

                if (first_id_check_box){
                    
                    var idList=[]
                    var all_checkboxes = document.querySelectorAll('input[type="checkbox"]');
                    all_checkboxes.forEach(function(checkbox) {
                        if (checkbox.id != "selectAll") {
                            idList.push(checkbox.id);
                        }
                    });


                    // Find the index of id1 and id2 in the idList
                    var index_first_id = idList.indexOf(first_id_check_box);
                    var index_shift_pressed_id = idList.indexOf(elem.id);
                    // If either id1 or id2 is not found in the idList, or they are next to each other, return null
                    if (index_first_id === -1 || index_shift_pressed_id === -1 || Math.abs(index_first_id - index_shift_pressed_id) === 1) {

                    } else {
                        // Determine the smaller and larger index
                        var minIndex = Math.min(index_first_id, index_shift_pressed_id);
                        var maxIndex = Math.max(index_first_id, index_shift_pressed_id);
                        // Extract the IDs between the indices
                        var idsBetween = idList.slice(minIndex + 1, maxIndex);

                        if (idsBetween){
                            
                            idsBetween.forEach(function(id) {
                                var checkbox = document.getElementById(id);
                                if (checkbox) {
                                    checkbox.checked = true;
                                }
                            });
                            
                        }

                    }

                }

            }
            else{
                var checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
                if (checkboxes.length > 0) {
                    addcontactelem.classList.remove("disabled");
                }
                
                first_id_check_box = elem.id
            }
        }
        else{
            delete contactChecked[elem.id];
            if (Object.keys(contactChecked).length == 0){
                addcontactelem = document.getElementById("update-inventory");
                
                var checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
                if (!checkboxes.length) {
                    addcontactelem.classList.add("disabled");
                }
            }
        }
    }
</script>
{% endif %}

{% include 'data/javascript/toggleSearch.html' %} 

{% endblock %}

