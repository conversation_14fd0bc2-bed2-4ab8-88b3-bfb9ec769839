{% extends 'base.html' %}
{% load i18n %}
{% load static %}
{% load humanize %}
{% load custom_tags %}
{% load hosts %}

{% block content %}
<style>
    .search-wrapper {
        display: flex;
        align-items: center;
        position: relative;
        width: 24px;
        transition: width 0.4s ease-in-out, margin-right 0.4s ease-in-out, opacity 0.4s ease-in-out;
    }

    .search-wrapper.expanded {
        width: 200px; /* New width when expanded */
        margin-right: -0.5rem !important;
    }

    .search-wrapper input {
        display: none;
        width: 0;
        padding: 0;
        opacity: 0;
        transition: width 0.4s ease-in-out, margin-right 0.4s ease-in-out, opacity 0.4s ease-in-out;
    }

    .search-wrapper.expanded input {
        display: block;
        width: 100%;
        opacity: 1;
    }


    .search-icon-view {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
    }

    /* Tooltip container */
    .hover-tooltip {
        position: relative;
        display: inline-block;
    }

    .hover-tooltip .hover-tooltip-text {
        visibility: hidden;
        width: 80px;
        background-color: #555;
        color: #fff;
        text-align: center;
        padding: 3px 0;
        border-radius: 8px;

        /* Position the hover-tooltip text */
        position: absolute;
        z-index: 1;
        top: 50%;
        right: 105%;
        transform: translateY(-50%);

        /* Fade in hover-tooltip */
        opacity: 0;
        transition: opacity 0.5s;
    }

    .hover-tooltip:hover .hover-tooltip-text {
        visibility: visible;
        opacity: 0.9;
    }

</style>

<div class="mb-10 mb-5 px-10">
    {% if permission == 'hide' %}
        <div class="d-flex justify-content-between align-items-center pt-5">
            <h1 class="mb-5">
                {% if LANGUAGE_CODE == 'ja'%}
                    ワークスペースのログ
                {% else %}
                    Workspace Logs
                {% endif %}
            </h1>
        </div>
        {% include 'data/static/no-access-page.html' %}
    {% else %}
    <div class="d-flex justify-content-between align-items-center pt-5">
        <h1 class="mb-5">
            {% if LANGUAGE_CODE == 'ja'%}
                ワークスペースのログ
            {% else %}
                Workspace Logs
            {% endif %}
        </h1>

        <div class="d-flex me-2 ms-5">
            <div class="{% include "data/utility/search-wrapper.html" %} ">
                <div class="d-flex align-items-center">
                    <form id="filter-form-search" method="get" class="w-100">
                        <div class="d-flex mb-0 position-relative align-items-center" style="height: 26px;">
                            <span class="svg-icon svg-icon-3 search-icon-view" onclick="toggleSearch()">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                                    <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                                </svg>
                            </span>
                            <input
                                id="base-search-input" type="text" name="q" class="form-control bg-white ps-12"
                                value="{% if search_q %}{{ search_q }}{% else %}""{% endif %}"
                                placeholder="{% if LANGUAGE_CODE == 'ja' %}ログを検索{% else %}Search Log{% endif %}"
                                onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                            >
                            <input type="hidden" name="setting_type" value="log">
                        </div>
                    </form>
                </div>
            </div>

            <button type="button" class="{% include "data/utility/gray-header-button.html" %} workspace-log-download-button">
                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M21 22H3C2.4 22 2 21.6 2 21C2 20.4 2.4 20 3 20H21C21.6 20 22 20.4 22 21C22 21.6 21.6 22 21 22ZM13 13.4V3C13 2.4 12.6 2 12 2C11.4 2 11 2.4 11 3V13.4H13Z" fill="black"/>
                        <path opacity="0.3" d="M7 13.4H17L12.7 17.7C12.3 18.1 11.7 18.1 11.3 17.7L7 13.4Z" fill="black"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    ダウンロード
                    {% else %}
                    Download
                    {% endif %}
                </span>
            </button>

        </div>



    </div>
    <div class="mt-0 mb-0" style="width: 100%; height: calc(100vh - 168px); overflow-y: scroll;">
        {% for app_log in app_logs %}

            {% if app_log|checking_log_related_application %}
                <div class="mb-3 fs-6 d-flex align-items-start">
                    <div class="me-5 text-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-info-circle-fill" viewBox="0 0 16 16">
                            <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16m.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2"/>
                        </svg>
                    </div>
                    <div>
                        {% if app_log.action == "create" or app_log.action == "delete" %}
                            <b>
                                {% if app_log.user %}
                                    {{app_log.user.first_name}}
                                {% else %}
                                    {% if LANGUAGE_CODE == 'ja' %}システム {% else %}System {% endif %}
                                {% endif %}


                                {% if app_log.action == "create" %}
                                    {% if LANGUAGE_CODE == 'ja' %}
                                    が
                                    {{app_log|display_app_instance_name:request}}
                                    を作成しました
                                    {% else %}
                                    created {{app_log|display_app_instance_name:request}}
                                    {% endif %}
                                {% elif app_log.action == "delete" %}
                                    {% if LANGUAGE_CODE == 'ja' %}
                                    削除されました {{app_log|display_app_instance_name:request}}
                                    {% else %}
                                    deleted {{app_log|display_app_instance_name:request}}
                                    {% endif %}
                                {% endif %}

                            </b>

                            {% if LANGUAGE_CODE == 'ja' %} (時刻) {% else %} at {% endif %}
                            <b>
                                {% local_time app_log.created_at workspace.timezone %}
                            </b>
                        {% else %}
                            <b>
                                {% if app_log.user %}
                                    {{app_log.user.first_name}}
                                {% else %}
                                    {% if LANGUAGE_CODE == 'ja' %}システム {% else %}System {% endif %}
                                {% endif %}

                                {% if LANGUAGE_CODE == 'ja' %}
                                が
                                {{app_log|display_app_instance_name:request}}
                                -
                                {% display_column app_log request%}

                                を編集しました:

                                {% else %}

                                edited:

                                {{app_log|display_app_instance_name:request}}

                                -
                                {% display_column app_log request%}

                                {% endif %}


                            ({{app_log.old_value}} -> {{app_log.new_value}})

                            {% if LANGUAGE_CODE == 'ja' %} (時刻) {% else %} at {% endif %}

                            <b>

                                {% local_time app_log.created_at workspace.timezone %}
                            </b>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        {% empty %}
            {% if LANGUAGE_CODE == 'ja' %}アクティビティが見つかりませんでした {% else %}No activity found{% endif %}
        {% endfor %}

        {% if app_logs %}
        <div class="{% include "data/utility/pagination.html" %}">
            {% if LANGUAGE_CODE == 'ja'%}
            {{paginator_item_begin}}–{{paginator_item_end}} の {{paginator.count}} 件
            {% else %}
            Viewing {{paginator_item_begin}}–{{paginator_item_end}} of {{paginator.count}} results
            {% endif %}

            <div>

                {% if page_content.has_previous %}
                    <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300  w-100px" href="{% host_url 'workspace_logs' host 'app' %}?page=1&{% query_transform %}">&laquo; {% if LANGUAGE_CODE == 'ja' %}最初 {% else %}First{% endif %}</a>
                    <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300  w-100px" href="{% host_url 'workspace_logs' host 'app' %}?page={{ page_content.previous_page_number }}&{% query_transform %}">{% if LANGUAGE_CODE == 'ja' %}前 {% else %}Previous{% endif %}</a>
                {% endif %}

                {% if page_content.has_next %}
                    <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300  w-100px" href="{% host_url 'workspace_logs' host 'app' %}?page={{ page_content.next_page_number }}&{% query_transform %}">{% if LANGUAGE_CODE == 'ja' %}次 {% else %}Next{% endif %}</a>
                    <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300  w-100px" href="{% host_url 'workspace_logs' host 'app' %}?page={{ page_content.paginator.num_pages }}&{% query_transform %}">{% if LANGUAGE_CODE == 'ja' %}最後 {% else %}Last{% endif %} &raquo;</a>
                {% endif %}
            </div>
        </div>
        {% endif %}

    </div>
    {%endif%}
</div>
{% endblock %}

{% include 'data/javascript/toggleSearch.html' %}

<script>
    $(document).ready(function() {
        KTDrawer.createInstances();
    });
</script>