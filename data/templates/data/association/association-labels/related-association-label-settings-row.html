{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% load tz %}
{% get_current_language as LANGUAGE_CODE %}

<tr {% if border_bottom %}class="cell-border-bottom"{% endif %}>
    <td class="me-2 w-8px align-items-center position-relative">
    </td>

    <td class="fw-bolder min-w-200px">
        

        <a class="text-dark text-hover-primary cursor-pointer fw-bolder view-settings-button" 
            hx-get="{% host_url 'manage_related_association_label' host 'app' %}"
            hx-vals='{"redirect_object_source":"{{object_source}}", "object_source":"{{association_label.object_source}}", "association_label_id": "{{association_label.id}}"}'
            hx-target="#view-settings-drawer" 
            hx-trigger="click">

            {% if not association_label.created_by_sanka %}
                {% if LANGUAGE_CODE == 'ja' %}
                    {{association_label.label_ja}} 
                {% else %}
                    {{association_label.label}}
                {% endif %}
            {% else %}
                {% with args=association_label.label|stringify|add:'|'|add:association_label.object_source %} 
                    {% with column_display=args|get_column_display:request %}
                        {{column_display.name}} ({% if LANGUAGE_CODE == 'ja' %}デフォルトから {% else %}Default from {% endif %} {{association_label.object_source|page_group_to_object_singular:request.LANGUAGE_CODE}})
                    {% endwith %}
                {% endwith %}
            {% endif %}
        </a>
    </td>

    <td class="min-w-200px">
        
        <div>
            {{association_label.object_source|page_group_to_object_singular:request.LANGUAGE_CODE}} {% if LANGUAGE_CODE == 'ja' %}<b>(ソース)</b>{% else %}<b>(Source)</b>{% endif %}
        </div>
    </td>

    <td>

        {% if association_label.created_at %}
            {% date_format association_label.created_at 1 %}
        {% elif not association_label.created_at %}
            {% date_format workspace.created_at 1 %}
        {% endif %}

    </td>

    <td>
        {% if not association_label.created_by_sanka %}
            {% if association_label.updated_at %}
                {% date_format association_label.updated_at 1 %}
            {% else %}
                -
            {% endif %}
        {% else %}
            -
        {% endif %}
    </td>
</tr>
