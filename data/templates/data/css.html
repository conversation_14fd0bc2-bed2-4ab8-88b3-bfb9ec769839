{% load i18n %}
{% load custom_tags %}
<style>


  {% if LANGUAGE_CODE == 'ja'%}
    html, body, h1, h2 {
      font-family: "Inter", 'Noto Sans JP', sans-serif !important;
    }
  {% else %}
    html, body, h1, h2 {
      font-family: "Inter", 'Open Sans', sans-serif !important;
    }
  {% endif %}

</style>
<style>

.select2-container--bootstrap5 .select2-selection--multiple:not(.form-select-sm):not(.form-select-lg) .select2-selection__choice .select2-selection__choice__display {
  font-size:10px !important
}
</style>

<style>

  {% comment %} Custom {% endcomment %}
  
  html {
    scroll-behavior: smooth;
  }

  .noimage{
    width: 39px;height: 39px;background: black;
    border: 1px solid #000;
  }
  @media (min-width: 992px) {
    .menu-title {
      color: #716D66;
    }

    .text-lg-white {
      color: white !important;
    }

  }
  
  {% comment %} Custom CSS  {% endcomment %}
  .bg-theme {
    background: #F9EAD7;
  }



  .card-footer{
    padding: 0 2.25rem; border-top: 1px dashed #E8E5DD
  
  }
  
  .dropzone {
    background: white;
    border:none
  }
  
  {% comment %} Menu Color {% endcomment %}
  .aside-menu .menu .menu-item.here > .menu-link,
  .aside-menu .menu .menu-item.here > .menu-link .menu-title,
  .aside-menu .menu .menu-item.here > .menu-link .menu-arrow:after,
  .aside-menu .menu .menu-item.here > .menu-link .menu-icon .svg-icon svg [fill]:not(.permanent):not(g)
  {
    color:#030303 !important;
    fill:#030303 !important
  }

  .svg-icon-white path {
    stroke: #fff !important;
  }

  .svg-icon-primary path {
    stroke: #472CF5 !important;
  }
  .svg-icon-gray-700 path {
    stroke: #494949 !important;
  }


  {% comment %} if .aside-menu is not under .setting-drawer change the text to green in css {% endcomment %}
  .aside-menu .menu .menu-item .menu-link.active {
    transition: color 0.2s ease, background-color 0.2s ease;
    background-color: #1b0f0b;
    color: #ffffff;
  }

  .aside-menu .menu .menu-item .menu-link, .aside-menu .menu .menu-item .svg-icon path {
    color: #494949;
    stroke: #494949; 
  }

  .aside-menu .menu .setting-drawer .menu-link.active {
    transition: color 0.2s ease, background-color 0.2s ease;
    background-color: transparent !important;
    color: #716D66 !important;
  }

  .setting-drawer:not .aside-menu .menu .menu-item .menu-link .menu-icon .svg-icon svg [fill]:not(.permanent):not(g),
  .aside-menu .menu .menu-item.hover:not(.here) > .menu-link:not(.disabled):not(.active):not(.here) .menu-icon .svg-icon svg [fill]:not(.permanent):not(g)
  {
    fill:#030303 !important
  }



  .aside-menu .menu .menu-item .menu-link:hover:not(.disabled):not(.active):not(.here) .menu-title {color: #472CF5 !important}
  .aside-menu .menu .menu-item .menu-link:hover:not(.disabled):not(.active):not(.here) .menu-icon .svg-icon svg [fill]:not(.permanent):not(g) {
    fill:#472CF5 !important
  }

  .aside-menu(.setting-drawer) .menu .menu-item .menu-link.active {background:red !important}
  .aside-menu .menu .menu-item .menu-link.active .menu-icon .svg-icon svg [fill]:not(.permanent):not(g) {
    fill:#fff !important
  }

  .aside-menu .menu .menu-item .menu-link.active .menu-title {color:#fff !important}

  .aside-menu .menu .menu-item.hover:not(.here) > .menu-link:not(.disabled):not(.active):not(.here) .menu-title {color:#211F30 !important}
  
  .aside-menu .menu .menu-item.here > .menu-link .menu-arrow:after,
  .aside-menu .menu .menu-item.hover:not(.here) > .menu-link:not(.disabled):not(.active):not(.here) .menu-arrow:after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 9' fill='%23968E7E'%3e%3cpath fill-rule='evenodd' clip-rule='evenodd' d='M5.93537 4.57889C6.03839 4.77912 6.0191 5.0363 5.87137 5.21403L2.87153 8.82282C2.68598 9.04603 2.36951 9.06026 2.16468 8.8546C1.95985 8.64893 1.94422 8.30126 2.12977 8.07804L4.80594 4.85863L2.15586 1.93583C1.96104 1.72096 1.96165 1.37314 2.15722 1.15895C2.35279 0.944757 2.66927 0.945311 2.86409 1.16018L5.85194 4.45551C5.8859 4.49296 5.91371 4.53459 5.93537 4.57889Z'/%3e%3c/svg%3e") !important;
  }
  .select2-dropdown {margin-left:10px}
  .select2-container--open .select2-dropdown--below {padding: 0 !important}
  .tagify__input { position: relative !important;}
  .tagify__input::before { position: absolute !important}
  .tagify__input::after { position: absolute !important} 


  /* Suggestions items */
  .tagify__dropdown.users-list .tagify__dropdown__item{
      padding: .5em .7em;
      display: grid;
      grid-template-columns: auto 1fr;
      gap: 0 1em;
      grid-template-areas: "avatar name"
                           "avatar email";
  }
  
  .tagify__dropdown.users-list .tagify__dropdown__item:hover .tagify__dropdown__item__avatar-wrap{
      transform: scale(1.2);
  }
  
  .tagify__dropdown.users-list .tagify__dropdown__item__avatar-wrap{
      grid-area: avatar;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      overflow: hidden;
      background: #EEE;
      transition: .1s ease-out;
  }
  
  .tagify__dropdown.users-list img{
      width: 100%;
      vertical-align: top;
  }
  
  .tagify__dropdown.users-list strong{
      grid-area: name;
      width: 100%;
      align-self: center;
  }
  
  .tagify__dropdown.users-list span{
      grid-area: email;
      width: 100%;
      font-size: .9em;
      opacity: .6;
  }
  
  .tagify__dropdown.users-list .tagify__dropdown__item__addAll{
      border-bottom: 1px solid #DDD;
      gap: 0;
  }
  
  
  #users-list .tagify__tag{
      white-space: nowrap;
  }
  
  #users-list .tagify__tag:hover .tagify__tag__avatar-wrap{
      transform: scale(1.6) translateX(-10%);
  }
  
  #users-list .tagify__tag .tagify__tag__avatar-wrap{
      width: 16px;
      height: 16px;
      white-space: normal;
      border-radius: 50%;
      background: silver;
      margin-right: 5px;
      transition: .12s ease-out;
  }
  
  #users-list .tagify__tag img{
      width: 100%;
      vertical-align: top;
      pointer-events: none;
  }
  

  {% comment %} select2 {% endcomment %}
  .select2-selection__rendered {
    color: #716D66 !important;
    padding-left: 0 !important;
  }
  .select2-container--bootstrap5 .select2-selection--multiple:not(.form-select-sm):not(.form-select-lg) .select2-selection__choice .select2-selection__choice__remove {
    {% comment %} margin-right:15px !important {% endcomment %}
  }
  .select2-dropdown {margin-left:0}
  .select2-container--bootstrap5 .select2-selection--multiple:not(.form-select-sm):not(.form-select-lg) .select2-selection__choice .select2-selection__choice__display {
    margin-left:10px !important;
    left: 10px;
    position: relative;
  }
  .select2-container--bootstrap5 .select2-selection__choice {
    padding-right:15px !important;
    background-color: rgba(248, 246, 242, 0.9) !important;
  }

  {% comment %} TextChange {% endcomment %}
  .animated-heading {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  span#words {
    font-weight: bolder;
  }

  .bg-dark {
    background-color: #1b0f0b !important;
  }

  .menu-state-title-primary .menu-item .menu-link.active {
      transition: color 0.2s ease, background-color 0.2s ease;
  }

  .menu-state-title-primary .menu-item .menu-link.active .menu-title {
      color: #1b0f0b !important;
  }

  .bg-hover-custom:hover {
  }
  .bg-hover-custom:hover .hover-custom-title {text-decoration:underline;}

  .bg-hover-custom:hover .bg-hover-custom-content {
    opacity: 80%;
  }

  .hover-opacity:hover {
    opacity: 80%;
  }
  
  .border-custom:hover {
    border-color: #716D66 !important;
  }

  .border-bottom-dashed {
    border-bottom: 1px dashed #EAEAEA !important;
  }

  .border-bottom-dotted {
    border-bottom: 1px dotted #3d3b3b !important;
  }

  .active .svg-icon-custom path {
    fill: #472CF5 !important;
  }

  .svg-icon-custom path {
    fill: #fff !important
  }

  a {
      color: #472CF5;
      text-decoration: none;
  }

  a:hover {
    color: #D1C9FF;
    text-decoration: underline;
}


  .bg-primary { 
    background-color: #472CF5 !important;
}

.bg-primary.hoverable:hover {
    background-color: #D1C9FF !important;
}



  .svg-icon.svg-icon-primary svg [fill]:not(.permanent):not(g) {
    transition: fill 0.3s ease;
    fill: #472CF5;
  }

  .form-check-input:checked {
    background-color: #472CF5;
    border-color: #472CF5;
  }

  .form-check.form-check-solid .form-check-input:checked {
    background-color: #472CF5;
}

.nav-line-tabs .nav-item .nav-link.active,
.nav-line-tabs .nav-item.show .nav-link,
.nav-line-tabs .nav-item .nav-link:hover:not(.disabled) {
    background-color: transparent;
    border: 0;
    border-bottom: 1px solid #472CF5;
    transition: color 0.2s ease, background-color 0.2s ease;
}

.btn-dark {
  background-color: #1b0f0b !important;
  border-color: #1b0f0b !important;
}

.btn-dark:hover {
  background-color: #827464 !important;
  border-color: #827464 !important;
}
.btn.btn-light-primary {
  color: #472CF5;
  border-color: #D1C9FF;
  background-color: #D1C9FF;
}

.btn.btn-light-primary i {
  color: #472CF5;
}

.daterangepicker .drp-calendar td.today,
.daterangepicker .drp-calendar td.today.active {
    background: #D1C9FF !important;
    color: #472CF5 !important;
    border-radius: 0.95rem;
}

.daterangepicker .drp-calendar td.in-range.available:not(.active):not(.off):not(.today) {
    background-color: #D1C9FF;
    color: #472CF5;
}

.daterangepicker .drp-calendar td:hover {
    background-color: #D1C9FF;
    color: #472CF5;
}

.daterangepicker .drp-calendar th.available:hover,
.daterangepicker .drp-calendar td.available:hover {
    border-radius: 0.95rem;
    background-color: #D1C9FF;
    color: #472CF5;
}



.btn-check:checked+.btn.btn-light-primary,
.btn-check:active+.btn.btn-light-primary,
.btn.btn-light-primary:focus:not(.btn-active),
.btn.btn-light-primary:hover:not(.btn-active),
.btn.btn-light-primary:active:not(.btn-active),
.btn.btn-light-primary.active,
.btn.btn-light-primary.show,
.show>.btn.btn-light-primary {
    color: #FFFFFF;
    border-color: #472CF5;
    background-color: #472CF5 !important;
}


.bg-light-primary {
  background-color: #D1C9FF;
}

.bg-light-primary.hoverable:hover {
  background-color: #D1C9FF;
}



  .btn.btn-primary {
    background-color: #472CF5 !important;
    border-color: #472CF5 !important;
  }

  .btn.btn-primary:hover {
    background-color: #472CF5 !important;
    border-color: #472CF5 !important;
  }

  .aside-menu .menu .menu-item.hover:not(.here)>.menu-link:not(.disabled):not(.active):not(.here),
  .aside-menu .menu .menu-item:not(.here) .menu-link:hover:not(.disabled):not(.active):not(.here) {
      transition: color 0.2s ease, background-color 0.2s ease;
      background-color: transparent;
      color: #472CF5;
  }
  
  .aside-menu .menu .menu-item.hover:not(.here)>.menu-link:not(.disabled):not(.active):not(.here) .menu-title,
  .aside-menu .menu .menu-item:not(.here) .menu-link:hover:not(.disabled):not(.active):not(.here) .menu-title {
      color: #472CF5;
  }
  
  .aside-menu .menu .menu-item.hover:not(.here)>.menu-link:not(.disabled):not(.active):not(.here) .menu-icon i,
  .aside-menu .menu .menu-item:not(.here) .menu-link:hover:not(.disabled):not(.active):not(.here) .menu-icon i {
      color: #472CF5;
  }
  
  .aside-menu .menu .menu-item.hover:not(.here)>.menu-link:not(.disabled):not(.active):not(.here) .menu-icon .svg-icon svg [fill]:not(.permanent):not(g),
  .aside-menu .menu .menu-item:not(.here) .menu-link:hover:not(.disabled):not(.active):not(.here) .menu-icon .svg-icon svg [fill]:not(.permanent):not(g) {
      transition: fill 0.3s ease;
      fill: #472CF5;
  }
  
  .select2-container--bootstrap5 .select2-dropdown .select2-results__option.select2-results__option--highlighted {
    background-color: #D1C9FF;
    color: #472CF5;
    transition: color 0.2s ease, background-color 0.2s ease;
}


  .select2-container--bootstrap5 .select2-dropdown .select2-results__option.select2-results__option--selected {
    background-repeat: no-repeat;
    background-position: center;
    background-color: transparent;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 11'%3e%3cpath fill='%23FFFFFF' d='M4.89557 6.49823L2.79487 4.26513C2.26967 3.70683 1.38251 3.70683 0.857309 4.26513C0.375593 4.77721 0.375593 5.57574 0.857309 6.08781L4.74989 10.2257C5.14476 10.6455 5.81176 10.6455 6.20663 10.2257L13.1427 2.85252C13.6244 2.34044 13.6244 1.54191 13.1427 1.02984C12.6175 0.471537 11.7303 0.471536 11.2051 1.02984L6.06096 6.49823C5.74506 6.83403 5.21146 6.83403 4.89557 6.49823Z'/%3e%3c/svg%3e");
    background-size: 0.8rem;
    background-position: center right 1.25rem;
    background-color: #472CF5;
    color: #FFFFFF;
    transition: color 0.2s ease, background-color 0.2s ease;
}

.text-hover-primary:hover {
  transition: color 0.2s ease, background-color 0.2s ease;
  color: #472CF5 !important;
}

.text-hover-primary:hover i {
  transition: color 0.2s ease, background-color 0.2s ease;
  color: #472CF5 !important;
}

.text-hover-primary:hover .svg-icon svg [fill]:not(.permanent):not(g) {
  transition: fill 0.3s ease;
  fill: #472CF5;
}

.text-hover-primary:hover .svg-icon svg:hover [fill]:not(.permanent):not(g) {
  transition: fill 0.3s ease;
}

.explore-btn-dismiss:hover .svg-icon svg [fill]:not(.permanent):not(g) {
  transition: fill 0.3s ease;
  fill: #472CF5;
}

.explore-btn-dismiss:hover .svg-icon svg:hover [fill]:not(.permanent):not(g) {
  transition: fill 0.3s ease;
}

.explore-btn-primary {
  border: 0;
  color: #ffffff;
  background-color: #472CF5;
}

.border-primary {
  border-color: #472CF5 !important;
}
.separator.separator-content.border-primary::before,
.separator.separator-content.border-primary::after {
    border-color: #472CF5 !important;
}


[dir=rtl] .select2-container--bootstrap5 .select2-dropdown .select2-results__option.select2-results__option--selected {
    background-position: center left 1.25rem;
}

.select2-container--bootstrap5 .select2-dropdown .select2-results__option.select2-results__option--disabled {
    color: #B5B0A1;
}

.select2-container--bootstrap5 .select2-dropdown .select2-results__option.select2-results__message {
    color: #8E887A;
}


  {% comment %} Feed {% endcomment %}
  .feed-body img {width:100% !important}
  .feed-body h3 {margin-top:10px !important; font-size:16px !important;}
  .feed-body h4 {font-size:14px !important}
  .feed-body img {width: 100%;}
  pre {white-space:pre-wrap !important}

  @media (min-width: 992px) {
      .header .header-brand, .aside, .aside .aside-menu {width:225px !important}
      .wrapper {padding-left: 225px !important}
      .header .header-brand, .header {height:0px !important;}
      {% if workspace.subscription != 'partner' and page_type in workspace_usage and not workspace|workspace_has_quota:page_type %}
      .aside {top:135px !important; }
      {% else %}
      .aside {top:80px !important; }
      {% endif %}
      .border-lg-end {border-right:1px solid #EAEAEA !important}
      .border-lg-start {border-left:1px solid #EAEAEA !important}
      .border-lg-0 {border:none !important}
      .input-lg-posthead {border-radius: 0px 12px 12px 0px !important;}
      .input-lg-prehead {border-radius: 12px 0px 0px 12px !important;}
    }
  
  @media (max-width: 992px) {
    .desktop-search {display:none !important}
    .aside {width:100% !important;}
  } 
    

  {% comment %} Modal {% endcomment %}

  @media (min-width: 576px) {
    .modal-dialog {max-width: 570px; margin: 1.75rem auto;}
  }


  {% comment %} Blog {% endcomment %}
  .blog-content {
    font-size:18px;
  }

  .blog-content figcaption p {
    font-family: Helvetica, sans-serif !important;
    font-size:13.5px !important;
  }
  .blog-content p {
    font-size:18px !important;
    margin-bottom: 10px;
    margin-top:20px;
  }
  .blog-content li {
    font-size:18px !important;
    margin-bottom: 10px;
    margin-top:10px;
  }
  .blog-content h2 {
    margin-top:45px;
    border-left: 3px solid;
    padding-left: 10px;
    margin-bottom: 16px;
  font-size:23.5px}

  .blog-content h3 {
    font-size:21px;
    margin-top:30px;
  }
  .message-box h3 {
    margin-top: 0 !important;
  }

  .blog-content strong {
    font-weight: 900;
  }

  .blog-content .message-box {
    background: #eee;
    padding:25px 20px;
    border-radius:4px;
  }

  .sticky-top li {
    font-size:18px !important;
  }

  {% comment %} Landing {% endcomment %}  

  .hero {
      background:#FFF5EF;
      background-size: 400% 400%;
  }

  .new-hero {
    {% comment %} repeat this background https://nyc3.digitaloceanspaces.com/sankafile/static/content-files/9d9f174f-a693-4469-9749-660a4ccdcaa5.png {% endcomment %}
    background-image: url('https://nyc3.digitaloceanspaces.com/sankafile/static/content-files/9d9f174f-a693-4469-9749-660a4ccdcaa5.png');
    background-position: center;
    background-repeat: repeat;
    background-size: 100% 100%;
  }

  {% comment %} Haegwan to work on later {% endcomment %}

  {% comment %} .btn-primary {
    background-color: #1b0f0b !important;
    border-color: #1b0f0b !important;
  }

  .btn-primary:hover {
    background-color: #827464 !important;
    border-color: #827464 !important;
  } {% endcomment %}
  
  .loop {
      {% comment %} !!! NUMBER OF ICONS !!!  {% endcomment %}
      width: calc(75px * 40);
      animation: marquee 35s linear infinite;

    }

    @keyframes marquee {
      0% {
        transform: translateX(0);
      }
      100% {
        transform: translateX(calc(-75px * 20)); 
        {% comment %} !!! HALF OF WIDTH !!!  {% endcomment %}
      }
    }  




    .accordion-button:not(.collapsed) {
      color:#030303 !important;
      font-weight:bolder !important;
      background-color: #FFF4EF !important;
    }
  



    {% if LANGUAGE_CODE == 'ja'%}
    .fs-3qx {font-size: calc(1.25rem + 1.8vw) !important;}
    {% endif %}




    {% comment %} Fonts {% endcomment %}
    .fs-1 {font-size:26px}
    {% comment %} emoji picker {% endcomment %}
    .emojionearea .emojionearea-picker .emojionearea-search > input {
      border-radius: 4px !important;
      border: 1px solid #ccc !important;
  
    }

    {% comment %} HIDE CALENDAR POP OVER {% endcomment %}
    .fc-popover {
        display: none !important;
       }



    {% comment %} Anime {% endcomment %}
    .anime {
      background-color: #222;
      color: #fff;
      font-weight: 400;
      font-size: 1.5vw;
      display: flex;
      align-items: center;
      justify-content: right;
      text-align: center;
      text-transform: uppercase;
      font-family: "Khula", sans-serif;
      height: 400px;
      box-sizing: border-box;
      letter-spacing: 21px;
      margin: 0;
      line-height: 1.2;
      padding-left:4px;
    }
    @media (max-width: 992px) {
      .anime {
          height: 300px
      }
  }
  .anime span {
      display: inline-block;
      text-align:center;
    }


    .strong {
      font-weight: 800;
      background-color: #FFF4EF;
    }

    .strong-red{
      font-weight: 800;
      text-decoration: red wavy underline;
      -webkit-text-decoration : red wavy underline !important;
      -webkit-text-decoration-line : underline !important;
      -webkit-text-decoration-color : red !important;
      -webkit-text-decoration-style: wavy !important;

    }
    .strong-blue{
      font-weight: 800;
      text-decoration: #472CF5 wavy underline;
      -webkit-text-decoration : #472CF5 wavy underline !important;
      -webkit-text-decoration-line : underline !important;
      -webkit-text-decoration-color : #472CF5 !important;
      -webkit-text-decoration-style: wavy !important;

    }
    .highlight-blue {
      color: #fff;
      position: relative;
      z-index: 1;

    }
    .highlight-blue:after {
      content: " ";
      background-color: #1b0f0b;
      height:100%;
      top: 00%;
      left:0;
      position: absolute;
      width: 100%;
      z-index: -1;
    }

    .iframe-container{
        position: relative;
        width: 100%;
        padding-bottom: 56.25%; 
        height: 0;
        }
    .iframe-container iframe{
    position: absolute;
    top:0;
    left: 0;
    width: 100%;
    height: 100%;
    }  

    .workflow-drawer p {

    }
    .workflow-drawer img {
        width:100% !important;
        margin-bottom:20px;
    }  

    #workflow-desc-preview img {
      max-width: 100%;
    }

    .fw-bold {
      font-weight: 500 !important;
    }

    .fw-bolder {
      font-weight: 700 !important;
    }

    .fw-boldest {
      font-weight: 900 !important;
    }


    #workflow-form img, #workflow-wizard img {
      width:100%
    }
    .ck-balloon-panel, .ck-balloon-panel_position_border-side_right {display:none !important}

    .cursor-grab {
      cursor: grab;
    }
    @keyframes NewItemFading {
      0% {
          background-color: rgba(var(--bs-success-rgb));
      }
      100% {
          background-color: #FFFFFF;
      }
  }
  .new-item-fading {
      animation-duration: 3s; /* same as transition duration */
      animation-timing-function: linear; /* kind of same as transition timing */
      animation-delay: 0ms; /* same as transition delay */
      animation-iteration-count: 1; /* set to 2 to make it run twice, or Infinite to run forever!*/
      animation-direction: normal; /* can be set to "alternate" to run animation, then run it backwards.*/
      animation-fill-mode: forwards; /* can be used to retain keyframe styling after animation, with "forwards" */
      animation-play-state: running; /* can be set dynamically to pause mid animation*/
      background-color: rgba(var(--bs-success-rgb));
      animation-name:NewItemFading;
  }

  @keyframes fadeOut {
      0% { opacity: 1; }
      50% { opacity: 6; }
      100% { opacity: 0; }
  }

  .fadingOut {
      opacity: 0;
      animation: fadeOut 2s
  }
  .svg-icon-4x svg {
    width: 4rem !important;
    height: 4rem !important;
  }

  .svg-icon-3x svg {
    width: 3rem !important;
    height: 3rem !important;
  }
  .svg-icon-2x svg {
    width: 2.5rem !important;
    height: 2.5rem !important;
  }

  .svg-icon-2 svg {
    width: 2rem !important;
    height: 2rem !important;
  }

  .app-box:hover {background: #F8F6F2}
  .app-box p {color: black !important;}

  .input-prehead {
    border-radius: 12px 0px 0px 12px !important;
  }
  .input-posthead {
    border-radius: 0px 12px 12px 0px !important;
  }
  .input-rounded {
    border-radius: 12px!important;
  }

  .middle-select-rounded-0 .form-select {
    border-radius: 0px !important;
  }
  .middle-form-rounded-0 .form-select {
    border-radius: 0px !important;
  }
  .tagify-input-rounded-0 .tagify {
    border-radius: 0px !important;
  }

  .kanban-board-header {
    padding-bottom : 0px !important;
  }
  .width-10 {
    width: 14.2% !important;
  }


  @media (min-width: 992px) {
    .width-lg-20 {
      width: 20% !important;
    }
  }

  @media only screen and (max-width: 1220px) and (min-width: 992px)  {
    .width-10 {
      width: 25% !important;
    }

  }

  @media only screen and (max-width: 992px) and (min-width: 600px)  {
    .width-10 {
      width: 25% !important;
    }

  }
  @media only screen and (max-width: 600px)  {
    .width-20 {
      width: 20% !important;
    }
    .width-10 {
      width: 50% !important;
    }


  }
  
  .blurry-text {
    color: transparent;
    text-shadow: 0 0 5px rgba(0,0,0,0.5);
    user-select: none !important;
    pointer-events: auto !important;
  }

  .blurry {
    -webkit-filter: blur(5px);
    -moz-filter: blur(5px);
    -o-filter: blur(5px);
    -ms-filter: blur(5px);
    filter: blur(5px);
    user-select: none !important;
    pointer-events: auto !important;
  }


  {% comment %} Kanban Design {% endcomment %}
  .kanban-board-header,
  .kanban-drag {padding: 5px 10px !important;}

  .kanban-item {
    padding: 5px 10px !important;
    margin-bottom: 8px !important;
  }


  .form-control, .form-select, .input-group-text, .border-dark {
    border: 1px solid #A4A4A4 !important;
  }

  .border-0 {
    border: 0px !important;
  }


  .panel-metric-filter-group .select2-container {
    width: auto !important;
  }

  .kanban-container .kanban-board {
    float: none;
    flex-shrink: 0;
    margin-bottom: 1.25rem;
    margin-right: 0.25rem !important;
    background-color: #FAFAFA !important;
    border-radius: 0.95rem;
}

.kanban-container .kanban-board:last-child {
    margin-right: 0 !important;
}

.kanban-container .kanban-board .kanban-board-header {
    border-top-left-radius: 0.95rem;
    border-top-right-radius: 0.95rem;
}

.kanban-container .kanban-board .kanban-board-header .kanban-title-board {
    font-size: 1.2rem;
    font-weight: 500;
    color: #211F1C;
}

.kanban-container .kanban-board .kanban-board-header.white {
    background-color: #ffffff;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-board-header.white .kanban-title-board {
    color: #8E887A;
}

.kanban-container .kanban-board .kanban-board-header.light-white {
    background-color: rgba(255, 255, 255, 0.1);
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-board-header.light-white .kanban-title-board {
    color: #ffffff;
}

.kanban-container .kanban-board .kanban-board-header.light {
    background-color: #FAFAFA !important;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-board-header.light .kanban-title-board {
    color: #8E887A;
}

.kanban-container .kanban-board .kanban-board-header.light-light {
    background-color: rgba(248, 246, 242, 0.1);
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-board-header.light-light .kanban-title-board {
    color: #FAFAFA !important;
}

.kanban-container .kanban-board .kanban-board-header.primary {
    background-color: #472CF5;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-board-header.primary .kanban-title-board {
    color: #FFFFFF;
}

.kanban-container .kanban-board .kanban-board-header.light-primary {
    background-color: rgba(79, 201, 218, 0.1);
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-board-header.light-primary .kanban-title-board {
    color: #472CF5;
}

.kanban-container .kanban-board .kanban-board-header.secondary {
    background-color: #DAD3C3;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-board-header.secondary .kanban-title-board {
    color: #403D38;
}

.kanban-container .kanban-board .kanban-board-header.light-secondary {
    background-color: rgba(218, 211, 195, 0.1);
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-board-header.light-secondary .kanban-title-board {
    color: #DAD3C3;
}

.kanban-container .kanban-board .kanban-board-header.success {
    background-color: #B8D935;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-board-header.success .kanban-title-board {
    color: #FFFFFF;
}

.kanban-container .kanban-board .kanban-board-header.light-success {
    background-color: rgba(184, 217, 53, 0.1);
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-board-header.light-success .kanban-title-board {
    color: #B8D935;
}

.kanban-container .kanban-board .kanban-board-header.info {
    background-color: #4F55DA;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-board-header.info .kanban-title-board {
    color: #FFFFFF;
}

.kanban-container .kanban-board .kanban-board-header.light-info {
    background-color: rgba(79, 85, 218, 0.1);
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-board-header.light-info .kanban-title-board {
    color: #4F55DA;
}

.kanban-container .kanban-board .kanban-board-header.warning {
    background-color: #E8C444;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-board-header.warning .kanban-title-board {
    color: #FFFFFF;
}

.kanban-container .kanban-board .kanban-board-header.light-warning {
    background-color: rgba(232, 196, 68, 0.1);
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-board-header.light-warning .kanban-title-board {
    color: #E8C444;
}

.kanban-container .kanban-board .kanban-board-header.danger {
    background-color: #F06445;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-board-header.danger .kanban-title-board {
    color: #FFFFFF;
}

.kanban-container .kanban-board .kanban-board-header.light-danger {
    background-color: rgba(240, 100, 69, 0.1);
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-board-header.light-danger .kanban-title-board {
    color: #F06445;
}

.kanban-container .kanban-board .kanban-board-header.dark {
    background-color: #211F1C;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-board-header.dark .kanban-title-board {
    color: #ffffff;
}

.kanban-container .kanban-board .kanban-board-header.light-dark {
    background-color: rgba(33, 31, 28, 0.1);
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-board-header.light-dark .kanban-title-board {
    color: #211F1C;
}

.kanban-container .kanban-board .kanban-drag .kanban-item {
    border-radius: 0.95rem;
    box-shadow: 0px 0px 13px 0px rgba(0, 0, 0, 0.05);
    background: #ffffff;
}

.kanban-container .kanban-board .kanban-drag .kanban-item[data-class=white] {
    background-color: #ffffff;
    color: #8E887A;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-drag .kanban-item[data-class=light-white] {
    background-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-drag .kanban-item[data-class=light] {
    background-color: #FAFAFA !important;
    color: #8E887A;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-drag .kanban-item[data-class=light-light] {
    background-color: rgba(248, 246, 242, 0.1);
    color: #FAFAFA !important;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-drag .kanban-item[data-class=primary] {
    background-color: #472CF5;
    color: #FFFFFF;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-drag .kanban-item[data-class=light-primary] {
    background-color: rgba(79, 201, 218, 0.1);
    color: #472CF5;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-drag .kanban-item[data-class=secondary] {
    background-color: #DAD3C3;
    color: #403D38;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-drag .kanban-item[data-class=light-secondary] {
    background-color: rgba(218, 211, 195, 0.1);
    color: #DAD3C3;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-drag .kanban-item[data-class=success] {
    background-color: #B8D935;
    color: #FFFFFF;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-drag .kanban-item[data-class=light-success] {
    background-color: rgba(184, 217, 53, 0.1);
    color: #B8D935;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-drag .kanban-item[data-class=info] {
    background-color: #4F55DA;
    color: #FFFFFF;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-drag .kanban-item[data-class=light-info] {
    background-color: rgba(79, 85, 218, 0.1);
    color: #4F55DA;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-drag .kanban-item[data-class=warning] {
    background-color: #E8C444;
    color: #FFFFFF;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-drag .kanban-item[data-class=light-warning] {
    background-color: rgba(232, 196, 68, 0.1);
    color: #E8C444;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-drag .kanban-item[data-class=danger] {
    background-color: #F06445;
    color: #FFFFFF;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-drag .kanban-item[data-class=light-danger] {
    background-color: rgba(240, 100, 69, 0.1);
    color: #F06445;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-drag .kanban-item[data-class=dark] {
    background-color: #211F1C;
    color: #ffffff;
    box-shadow: none;
}

.kanban-container .kanban-board .kanban-drag .kanban-item[data-class=light-dark] {
    background-color: rgba(33, 31, 28, 0.1);
    color: #211F1C;
    box-shadow: none;
}

.kanban-fixed-height .kanban-container .kanban-board .kanban-drag {
    position: relative;
    overflow-y: auto;
}


.dataTables_length,
.dataTables_filter,
.dataTables_info,
.dataTables_paginate {
  display: none !important;
}

  .table tr:last-child, .table th:last-child, .table td:last-child {
      padding-right: 30px !important;
  }

.dataTables_wrapper.no-footer .dataTables_scrollBody {
  border-bottom: none !important;
}

{% comment %} for menu overflow {% endcomment %}
.vis-timeline {
    z-index: -1 !important;
}


</style>
  
<link rel="stylesheet" href="/static/styles/component.css">