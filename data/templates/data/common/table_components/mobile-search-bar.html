{% comment %}
Reusable mobile search bar component
Parameters:
- object_type: The type of object (e.g., 'orders', 'items')
- menu_key: The menu key for the module
- advance_search_filter: Advanced search filter data
- search_q: Search query
- view_id: View ID
- LANGUAGE_CODE: Current language code
- advance_search: Advanced search object
{% endcomment %}

{% load i18n %}
{% load custom_tags %}

<div class="{% if search_q %}max-md:tw-flex {% else %}max-md:tw-hidden {% endif %}tw-hidden tw-items-center tw-py-2 border-bottom border-bottom-1 tw-transition-all tw-duration-300" id="search-bar">
    <form id="filter-form-search" method="get" class="w-100">
        <div class="d-flex mb-0 position-relative align-items-center" style="height: 26px;">
            <span class="svg-icon svg-icon-3 search-icon-view">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                    <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                </svg>
            </span>
            <input id="base-search-input" type="text" name="q" class="form-control bg-white ps-12 pe-16"
            value="{% if advance_search_filter %}{% for k, v in advance_search_filter.items %}{% if object_type == 'orders' %}{% if k|search_custom_field_object_orders:request %}{% with channel_column=k|search_custom_field_object_orders:request %}{{channel_column.name}}{% endwith %}{% elif k|split:'|'|length > 1 %}{% with column_display=k|split:'|'|search_custom_field_object_order_customer:request %}{{column_display.name}}{% endwith %}{% else %}{% with column_display=k|display_column_orders:request %}{{column_display}}{% endwith %}{% endif %}{% elif object_type == 'items' %}{% if k|search_custom_field_object_items:request %}{% with channel_column=k|search_custom_field_object_items:request %}{{channel_column.name}}{% endwith %}{% else %}{% with column_display=k|display_column_items:request %}{% if k == 'tax' %}{{column_display}} (%) {% else %}{{column_display}}{% endif %}{% endwith %}{% endif %}{% endif %} {% translate_lang v.key|display_filter_rule:LANGUAGE_CODE LANGUAGE_CODE %} {{ v.value }}{% if not forloop.last %}, {% endif %}{% endfor %}{% elif search_q %}{{ search_q }}{% else %}{% endif %}"             
            placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
            onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
            >
            {% if request.GET.status == 'archived' %}
            <input type="hidden" value="archived" name="status">
            {% endif %}
            <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn me-7"
                hx-get="{% url 'advance_search_drawer' %}"
                hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True"}'
                hx-indicator=".loading-drawer-spinner"
                hx-target="#filter-drawer-content"
                hx-trigger="click"
                hx-swap="innerHTML"
            >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path fill="black" d="M19.43 12.98c.04-.32.07-.65.07-.98s-.03-.66-.07-.98l2.11-1.65a.5.5 0 0 0 .12-.63l-2-3.46a.5.5 0 0 0-.6-.22l-2.49 1a7.03 7.03 0 0 0-1.7-.98l-.38-2.65A.5.5 0 0 0 14 2h-4a.5.5 0 0 0-.5.45l-.38 2.65c-.63.25-1.21.57-1.74.98l-2.49-1a.5.5 0 0 0-.6.22l-2 3.46a.5.5 0 0 0 .12.63L4.57 11c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65a.5.5 0 0 0-.12.63l2 3.46a.5.5 0 0 0 .6.22l2.49-1c.53.41 1.11.73 1.74.98l.38 2.65c.03.24.24.45.5.45h4c.26 0 .47-.21.5-.45l.38-2.65c.63-.25 1.21-.57 1.74-.98l2.49 1a.5.5 0 0 0 .6-.22l2-3.46a.5.5 0 0 0-.12-.63l-2.11-1.65zM12 15.5A3.5 3.5 0 1 1 12 8a3.5 3.5 0 0 1 0 7.5z"/>
                </svg>
            </span>
            <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn me-7"
                hx-get="{% url 'advance_search_drawer' %}"
                hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True", "view_id":"{{view_id}}"}'
                hx-indicator=".loading-drawer-spinner"
                hx-target="#filter-drawer-content"
                hx-trigger="click"
                hx-swap="innerHTML"
            >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path fill="black" d="M19.43 12.98c.04-.32.07-.65.07-.98s-.03-.66-.07-.98l2.11-1.65a.5.5 0 0 0 .12-.63l-2-3.46a.5.5 0 0 0-.6-.22l-2.49 1a7.03 7.03 0 0 0-1.7-.98l-.38-2.65A.5.5 0 0 0 14 2h-4a.5.5 0 0 0-.5.45l-.38 2.65c-.63.25-1.21.57-1.74.98l-2.49-1a.5.5 0 0 0-.6.22l-2 3.46a.5.5 0 0 0 .12.63L4.57 11c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65a.5.5 0 0 0-.12.63l2 3.46a.5.5 0 0 0 .6.22l2.49-1c.53.41 1.11.73 1.74.98l.38 2.65c.03.24.24.45.5.45h4c.26 0 .47-.21.5-.45l.38-2.65c.63-.25 1.21-.57 1.74-.98l2.49 1a.5.5 0 0 0 .6-.22l2-3.46a.5.5 0 0 0-.12-.63l-2.11-1.65zM12 15.5A3.5 3.5 0 1 1 12 8a3.5 3.5 0 0 1 0 7.5z"/>
                </svg>
            </span>
            <span class="svg-icon {% if advance_search.is_active %}svg-icon-primary{%else%}svg-icon-muted{% endif %} svg-icon-3 filter-icon filter-drawer-btn"
                hx-get="{% url 'advance_search_drawer' %}"
                hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "view_id":"{{view_id}}"}'
                hx-indicator=".loading-drawer-spinner"
                hx-target="#filter-drawer-content"
                hx-trigger="click"
                hx-swap="innerHTML"
            >
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 7H3C2.4 7 2 6.6 2 6V4C2 3.4 2.4 3 3 3H21C21.6 3 22 3.4 22 4V6C22 6.6 21.6 7 21 7Z" fill="currentColor"/>
                    <path opacity="0.3" d="M21 14H3C2.4 14 2 13.6 2 13V11C2 10.4 2.4 10 3 10H21C21.6 10 22 10.4 22 11V13C22 13.6 21.6 14 21 14ZM22 20V18C22 17.4 21.6 17 21 17H3C2.4 17 2 17.4 2 18V20C2 20.6 2.4 21 3 21H21C21.6 21 22 20.6 22 20Z" fill="currentColor"/>
                </svg>
            </span>
            <input type="hidden" value="{{view_id}}" name="view_id">
        </div>
    </form>
</div>
