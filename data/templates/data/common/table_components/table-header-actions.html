{% comment %}
Reusable table header actions component
Parameters:
- object_type: The type of object (e.g., 'orders', 'items')
- menu_key: The menu key for the module
- permission: Permission object
- view_filter: View filter object
- property_sets: Property sets for dropdown
- LANGUAGE_CODE: Current language code
- open_drawer: Open drawer parameter
- create_drawer_url: URL name for create drawer
{% endcomment %}

{% load i18n %}
{% load custom_tags %}

<div class="w-50 d-flex justify-content-end tw-mr-5" id="view-container">
    {% if permission|check_permission:'edit' %}
        <div class="{% include "data/utility/table-button.html" %}">
            <button id='view-sync-{{object_type}}-action-drawer' type="button" class="max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 btn py-1 rounded-1 tw-bg-gray-200 hover:tw-bg-gray-300 justify-content-center px-1 object-action-drawer-button"
                hx-get="{% url 'shopturbo_load_drawer' %}" 
                hx-trigger="click"
                onclick="fillAction{{object_type|title}}Ids(this),check_permission_action(event, 'edit')"
                hx-target="#object-action-drawer-content"
                hx-indicator=".loading-drawer-spinner,.view-form"
                >
                <span class="svg-icon svg-icon-4">
                    <svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8.49968 1.33301H4.66264C4.54298 1.33301 4.48316 1.33301 4.43034 1.35123C4.38363 1.36734 4.34109 1.39363 4.30579 1.4282C4.26587 1.4673 4.23912 1.52081 4.18561 1.62783L1.38561 7.22783C1.25782 7.4834 1.19393 7.61119 1.20927 7.71506C1.22268 7.80576 1.27285 7.88694 1.34798 7.93949C1.43403 7.99967 1.5769 7.99967 1.86264 7.99967H5.99968L3.99968 14.6663L12.1284 6.23655C12.4027 5.95214 12.5398 5.80994 12.5478 5.68826C12.5548 5.58265 12.5112 5.48 12.4303 5.4117C12.3371 5.33301 12.1396 5.33301 11.7445 5.33301H6.99968L8.49968 1.33301Z" stroke="#3B3B3F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    アクション
                    {% else %}
                    Action
                    {% endif %}
                </span>
            </button>
        </div>
    {% endif %}

    <div class="{% include "data/utility/header-action-button.html" %}">
        <button class="btn tw-font-[500] tw-rounded-l-lg tw-rounded-r-none tw-border-0 tw-bg-gray-200 hover:tw-bg-gray-300 max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 py-1 justify-content-center px-1 create-view-settings-button" type="button"
            hx-get="{% url 'shopturbo_load_drawer' %}" 
            hx-trigger="click"
            onclick="fill{{object_type|title}}ExportIds(this)"
            hx-target="#manage-contacts-view-settings-drawer"
            hx-indicator=".loading-drawer-spinner,.view-form"
            >
            <span class="svg-icon svg-icon-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                    <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                    <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                </svg>
            </span>
            <span class="">
                {% if LANGUAGE_CODE == 'ja'%}
                エクスポート
                {% else %}
                Export
                {% endif %}
            </span>
        </button>
        {% if permission|check_permission:'edit' %}
            <button id='view-sync-{{object_type}}' type="button" class="{% include "data/utility/import-button.html" %}"
                hx-get="{% url 'shopturbo_load_drawer' %}" 
                hx-trigger="click"
                onclick="fill{{object_type|title}}Ids(this)"
                hx-target="#manage-contacts-view-settings-drawer"
                hx-indicator=".loading-drawer-spinner,.view-form"
                >
                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-download" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                        <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708z"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    インポート
                    {% else %}
                    Import
                    {% endif %}
                </span>
            </button>
        {% endif %}
    </div>

    {% if permission|check_permission:'edit' %}
    <div class="btn-group tw-h-[32px]">
        <button class="tw-w-[100px] align-items-center d-flex btn btn-primary btn-md shopturbo-create-wizard-button view_form_trigger py-1" type="button"
            hx-get="{% url create_drawer_url %}" 
            hx-vals = '{"drawer_type":"{{object_type}}", "view_id": "{{view_filter.view.id}}", "module": "{{menu_key}}", "object_type": "{{object_type}}", "page": "{{page}}", "set_id": "{{set_id}}"}'
            hx-target="#shopturbo-create-drawer-content"
            hx-indicator=".loading-drawer-spinner,#shopturbo-create-drawer-content"
            style="border-radius: 0.475rem 0 0 0.475rem;"
            >
            <span class="svg-icon svg-icon-4">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8.00065 3.33301V12.6663M3.33398 7.99967H12.6673" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </span>
    
            <span class="fs-7 ps-1 fw-bolder w-85px">
                {% if LANGUAGE_CODE == 'ja'%}
                新規
                {% else %}
                New
                {% endif %}
            </span>
        </button> 
        <button type="button" 
            class="tw-w-[30px] align-items-center d-flex btn btn-primary btn-md dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split" 
            data-bs-toggle="dropdown" 
            aria-expanded="false"
            style="border-radius: 0 0.475rem 0.475rem 0;border-left: 1px solid #dee2e6;"
        >
            <span class="svg-icon svg-icon-4">
                <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                    <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                </svg>
            </span>
        </button>
        <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
            {% comment %} Item-specific dropdown options {% endcomment %}
            {% if object_type == 'items' %}
            <li>
                <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden shopturbo-manage-wizard-button" type="button"
                    hx-get="{% url create_drawer_url %}"
                    hx-vals = '{"drawer_type":"barcode", "view_id": "{{view_id}}", "section": "items-query-barcode", "module": "{{menu_key}}"}'
                    hx-target="#shopturbo-drawer-content"
                    hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"
                    style="border-radius: 0.475rem 0 0 0.475rem;"
                    >
                    {% if LANGUAGE_CODE == 'ja'%}バーコード/QRコードで検索{% else %}Bar/QR Code Search{% endif %}
                </button>
            </li>
            <li>
                <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden shopturbo-create-wizard-button" type="button"
                    hx-get="{% url create_drawer_url %}"
                    hx-vals = '{"drawer_type":"upload_photos", "view_id": "{{view_id}}", "module": "{{menu_key}}"}'
                    hx-target="#shopturbo-create-drawer-content"
                    hx-indicator=".loading-drawer-spinner,#shopturbo-create-drawer-content"
                    style="border-radius: 0.475rem 0 0 0.475rem;"
                    >
                {% if LANGUAGE_CODE == 'ja'%}
                    画像をアップロード
                {% else %}
                    Upload Photos
                {% endif %}
                </button>
            </li>
            {% endif %}
            {% for set in property_sets %}
            <li>
                <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden shopturbo-create-wizard-button" type="button"
                    hx-get="{% url create_drawer_url %}"
                    hx-vals = '{"drawer_type":"{{object_type}}", "view_id": "{{view_id}}", "set_id": "{{set.id}}", "module": "{{menu_key}}"}'
                    hx-target="#shopturbo-create-drawer-content"
                    hx-indicator=".loading-drawer-spinner,#shopturbo-create-drawer-content"
                    style="border-radius: 0.475rem 0 0 0.475rem;"
                    >
                    {% if set.name %}
                        {{ set.name}}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default{% endif %}
                    {% endif %}
                </button>
            </li>
            {% endfor %}
        </ul>
    </div>
    {% endif %}
</div>
