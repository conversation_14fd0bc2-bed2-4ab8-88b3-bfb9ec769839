{% comment %}
Reusable bulk actions header component
Parameters:
- object_type: The type of object (e.g., 'orders', 'items')
- menu_key: The menu key for the module
- permission: Permission object
- LANGUAGE_CODE: Current language code
- view_filter: View filter object
- request: Request object for status checking
{% endcomment %}

{% load i18n %}
{% load custom_tags %}

<div id="view-header-container" class="tw-hidden w-100">
    <div class="justify-content-between align-items-center flex-row d-flex">
        <div class="w-100">
            {% include 'data/common/select-all-in-view-button.html' %} 
            
            {% if permission|check_permission:'edit' %}
            <button class="py-1 rounded-1 btn btn-sm btn-light-primary fw-bold mt-2 mb-1" onclick="bulk_check_toggle(),check_permission_action(event, 'edit', 'edit_bulk_modal')">
                <span>
                    {% if LANGUAGE_CODE == 'ja'%}
                    編集
                    {% else %}
                    Edit
                    {% endif %}
                </span>
            </button>
            
            <button class="py-1 rounded-1 btn btn-sm btn-light-warning fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'edit')" name="bulk_duplicate" type="submit" form="{{object_type}}-form">
                {% if LANGUAGE_CODE == 'ja'%}
                複製
                {% else %}
                Duplicate
                {% endif %}
            </button>
            {% endif %}
            {% if permission|check_permission:'archive' %}
            {% if request.GET.status == 'archived' %}
            {# Show only Activate button on archived page #}
            <button class="py-1 rounded-1 btn btn-sm btn-light-success fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'archive', 'manage_restore_bulk')">
                {% if LANGUAGE_CODE == 'ja'%}
                有効化
                {% else %}
                Activate
                {% endif %}
            </button>
            {# Show Delete button instead of Archive on archived page #}
            {% if object_type == 'items' %}
            <button class="btn btn-sm btn-light-danger py-1 rounded-1 fw-bold mt-2 mb-1 me-2" onclick="check_permission_action(event, 'archive', 'manage_permanent_delete_bulk')">
                {% if LANGUAGE_CODE == 'ja'%}
                削除
                {% else %}
                Delete
                {% endif %}
            </button>
            {% endif %}
            {% else %}
            {# Show only Archive button on normal page (hide Activate) #}
            <button class="btn btn-sm btn-light-danger py-1 rounded-1 fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'archive', 'manage_delete_bulk')">
                {% if LANGUAGE_CODE == 'ja'%}
                アーカイブ
                {% else %}
                Archive 
                {% endif %}
            </button>
            {% endif %}
            {% endif %}
            <script>
                document.body.addEventListener("hideTaskBulkActionBtn", function(evt){
                    document.getElementById('view-header-container').classList.add('d-none')
                    document.getElementById('{{object_type}}-view-contianer').classList.remove('d-none')
                })
            </script>
            <script>
                function getSelected{{object_type|title}}() {
                    var selected{{object_type|title}} = [];
                    {% if object_type == 'items' %}
                    // Use cross-page selection if available
                    if (window.selectionManager && window.selectionManager.getSelectedCount() > 0) {
                        selected{{object_type|title}} = window.selectionManager.getSelectedIds();
                    } else {
                        // Fallback to current page checkboxes
                        var classNameElements = document.getElementsByClassName("{{object_type|slice:':4'}}-selection");
                        if (classNameElements){
                            classNameElements.forEach(function(classNameElement) {
                                if (classNameElement.checked) {
                                    selected{{object_type|title}}.push(classNameElement.value);
                                }
                            });
                        }
                    }
                    {% else %}
                    var classNameElements = document.getElementsByClassName("{{object_type|slice:':4'}}-selection");
                    if (classNameElements){
                        classNameElements.forEach(function(classNameElement) {
                            if (classNameElement.checked) {
                                selected{{object_type|title}}.push(classNameElement.value);
                            }
                        });  
                    }
                    {% endif %}
                    return selected{{object_type|title}};
                }
            </script>
        </div>
        <div class="d-flex">
            {% if permission|check_permission:'edit' %}
                <button id='view-sync-{{object_type}}' type="button" class="{% include "data/utility/gray-header-button.html" %} create-view-settings-button"
                    hx-get="{% url 'shopturbo_load_drawer' %}" 
                    hx-trigger="click"
                    onclick="fillAction{{object_type|title}}Ids(this),check_permission_action(event, 'edit')"
                    hx-target="#manage-contacts-view-settings-drawer"
                    hx-indicator=".loading-drawer-spinner,.view-form"
                    >
                    <span class="svg-icon svg-icon-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-magic" viewBox="0 0 16 16">
                            <path d="M9.5 2.672a.5.5 0 1 0 1 0V.843a.5.5 0 0 0-1 0zm4.5.035A.5.5 0 0 0 13.293 2L12 3.293a.5.5 0 1 0 .707.707zM7.293 4A.5.5 0 1 0 8 3.293L6.707 2A.5.5 0 0 0 6 2.707zm-.621 2.5a.5.5 0 1 0 0-1H4.843a.5.5 0 1 0 0 1zm8.485 0a.5.5 0 1 0 0-1h-1.829a.5.5 0 0 0 0 1zM13.293 10A.5.5 0 1 0 14 9.293L12.707 8a.5.5 0 1 0-.707.707zM9.5 11.157a.5.5 0 0 0 1 0V9.328a.5.5 0 0 0-1 0zm1.854-5.097a.5.5 0 0 0 0-.706l-.708-.708a.5.5 0 0 0-.707 0L8.646 5.94a.5.5 0 0 0 0 .707l.708.708a.5.5 0 0 0 .707 0l1.293-1.293Zm-3 3a.5.5 0 0 0 0-.706l-.708-.708a.5.5 0 0 0-.707 0L.646 13.94a.5.5 0 0 0 0 .707l.708.708a.5.5 0 0 0 .707 0z"/>
                        </svg>
                    </span>
                    <span class="">
                        {% if LANGUAGE_CODE == 'ja'%}
                        アクション
                        {% else %}
                        Action
                        {% endif %}
                    </span>
                </button>
                <div class="{% include "data/utility/table-button.html" %}">
                    <button id='view-sync-{{object_type}}' type="button" class="w-150px align-items-center d-flex justify-content-center svg-icon-gray-600 btn btn-sm py-1 rounded-1 bg-gray-100 create-view-settings-button"
                        style="width: 130px"
                        hx-get="{% url 'shopturbo_load_drawer' %}" 
                        hx-trigger="click"
                        onclick="fill{{object_type|title}}ExportIds(this)"
                        hx-target="#manage-contacts-view-settings-drawer"
                        hx-indicator=".loading-drawer-spinner,.view-form"
                        >
                        <span class="svg-icon svg-icon-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                                <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                                <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                            </svg>
                        </span>
                        <span class="">
                            {% if LANGUAGE_CODE == 'ja'%}
                            エクスポート
                            {% else %}
                            Export
                            {% endif %}
                        </span>
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
    {% include 'data/common/select-all-in-view-record-msg.html' %} 
</div>
