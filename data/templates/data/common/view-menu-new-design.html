{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}

<div class="w-50 d-flex justify-content-end tw-mr-5" id="view-container">
    {% if permission|check_permission:'edit' %}
        <div class="{% include "data/utility/table-button.html" %}">
            <button id='view-sync-items-action-drawer' type="button" class="max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 btn py-1 rounded-1 tw-bg-gray-200 hover:tw-bg-gray-300 justify-content-center px-1 create-view-settings-button"
                hx-trigger="click"
                onclick="fillActionIds(this),check_permission_action(event, 'edit')"
                {% with url_target=object_type|slice:":-1"|add:"_drawer" %}
                    {% if object_type == 'journal' %}
                        hx-get="{% host_url 'journal_new_drawer' host 'app' %}"  
                    {% else %} 
                        hx-get="{% url url_target %}" 
                    {% endif %}
                {% endwith %}
                hx-target="#manage-contacts-view-settings-drawer"
                hx-indicator=".loading-drawer-spinner,.view-form"
                >
                <span class="svg-icon svg-icon-4">
                    <svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8.49968 1.33301H4.66264C4.54298 1.33301 4.48316 1.33301 4.43034 1.35123C4.38363 1.36734 4.34109 1.39363 4.30579 1.4282C4.26587 1.4673 4.23912 1.52081 4.18561 1.62783L1.38561 7.22783C1.25782 7.4834 1.19393 7.61119 1.20927 7.71506C1.22268 7.80576 1.27285 7.88694 1.34798 7.93949C1.43403 7.99967 1.5769 7.99967 1.86264 7.99967H5.99968L3.99968 14.6663L12.1284 6.23655C12.4027 5.95214 12.5398 5.80994 12.5478 5.68826C12.5548 5.58265 12.5112 5.48 12.4303 5.4117C12.3371 5.33301 12.1396 5.33301 11.7445 5.33301H6.99968L8.49968 1.33301Z" stroke="#3B3B3F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    アクション
                    {% else %}
                    Action
                    {% endif %}
                </span>
            </button>
        </div>

        <script>
            {% if open_drawer == 'action_drawer_history' %}
                $(document).ready(function() {
                    setTimeout(function() {
                        document.getElementById('view-sync-items-action-drawer').click()
                    }, 1000)
                })
            {% endif %}
            function fillActionIds(elm) {
                // Now set the hx-vals attribute with the updated account IDs
                {% with drawer_type=object_type|slice:":-1" %}
                    elm.setAttribute('hx-vals', 'js:{"drawer_type":"{{drawer_type}}","section":"action_history", "action_tab": "action", "selected_ids": getSelected() {% if open_drawer %},"open_drawer":"{{open_drawer}}"{% endif %}}'); 
                {% endwith %}
            }
        </script>
    {% endif %}
    {% if permission|check_permission:'edit' %}

    <div class="{% include "data/utility/header-action-button.html" %}">

        <div class="{% include "data/utility/table-button.html" %}  {% if source_integration == 'hubspot' %} d-none {% endif %}">
            <button type="button" class="btn tw-font-[500] tw-rounded-l-lg tw-rounded-r-none tw-border-0 tw-bg-gray-200 hover:tw-bg-gray-300 max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 py-1 justify-content-center px-1 create-view-settings-button"
                hx-vals='{"object_type": "{{object_type}}", "view_id":"{{current_view.id}}", "download_view":true, "import_export_type":"export"}'
                hx-get="{% url 'commerce_view_setting' %}" 
                hx-target="#manage-contacts-view-settings-drawer"
                hx-indicator=".loading-drawer-spinner,.view-form"
                >
                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                        <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    エクスポート
                    {% else %}
                    Export
                    {% endif %}
                </span>
            </button>
        </div>
        <div class="{% include "data/utility/table-button.html" %} {% if source_integration == 'hubspot' %} d-none {% endif %}">
            <button class="{% include "data/utility/import-button.html" %} {% if property_sets %} rounded-end-0 {% endif %}"  
                    hx-indicator=".loading-drawer-spinner,.view-form"
                    hx-target="#manage-contacts-view-settings-drawer" 
                    hx-vals='{"object_type": "{{object_type}}", "view_id":"{{current_view.id}}", "download_view":true, "import_export_type":"import"}'
                    hx-get="{% url 'commerce_view_setting' %}" 
                    type="button">
                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-download" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                        <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708z"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    インポート
                    {% else %}
                    Import
                    {% endif %}
                </span>
            </button>
            <button  class="max-md:tw-block tw-hidden tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer create-view-settings-button hover-tooltip"
                type="button"
                hx-vals='{"object_type": "{{object_type}}", "view_id":"{{current_view.id}}", "download_view":true}'
                hx-get="{% url 'commerce_view_setting' %}" 
                hx-indicator=".loading-drawer-spinner,.view-form"
                hx-target="#manage-contacts-view-settings-drawer">
                <span class="search-wrapper-tooltip hover-tooltip-text">
                    {% if LANGUAGE_CODE == 'ja' %}ダウンロード{% else %}Download{% endif %}
                </span>
                <span class="tw-flex svg-icon svg-icon-3">
                    <svg xmlns="http://www.w3.-listorg/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M21 22H3C2.4 22 2 21.6 2 21C2 20.4 2.4 20 3 20H21C21.6 20 22 20.4 22 21C22 21.6 21.6 22 21 22ZM13 13.4V3C13 2.4 12.6 2 12 2C11.4 2 11 2.4 11 3V13.4H13Z" fill="black"/>
                        <path opacity="0.3" d="M7 13.4H17L12.7 17.7C12.3 18.1 11.7 18.1 11.3 17.7L7 13.4Z" fill="black"/>
                    </svg>
                </span>
            </button>
        </div>
    </div>
    {% endif %}

    {% if permission|check_permission:'edit' %}
    <div class="ms-2" data-bs-toggle="tooltip" data-bs-placement="right" data-bs-delay-show="1000" data-kt-initialized="1">
        <div class="btn-group tw-h-[32px]">
            <button class=" w-100px align-items-center d-flex btn btn-primary btn-md py-1 rounded-1 create_new_wizard_button view_form_trigger {% if property_sets %} rounded-end-0 {% endif %}"
                id="{{object_type}}-create-record"  
                hx-vals='{"drawer_type":"{{object_type|to_drawer_type}}","module":"{{menu_key}}","set_id": "{{view.form.id|to_str}}"}'
                {% if object_type == 'journal' %}
                    hx-get="{% host_url 'journal_new_drawer' host 'app' %}"  
                {% else %} 
                    hx-get="{% url object_type|to_drawer_type|add:'_drawer' %}" 
                {% endif %}
                hx-indicator=".loading-drawer-spinner,.create_new_form"
                hx-target="#create-new-drawer-content"
                hx-on::before-request="document.getElementById('manage-full-drawer-content').innerHTML = '';document.getElementById('create-new-drawer-content').innerHTML = '';"
                type="button">
                <span class="svg-icon svg-icon-4">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8.00065 3.33301V12.6663M3.33398 7.99967H12.6673" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </span>
                <span class="fs-7 ps-1 fw-bolder w-85px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    新規
                    {% else %}
                    New
                    {% endif %}
                </span>
            </button>
            {% if property_sets %}
            <button type="button" 
                class="tw-w-[30px] align-items-center d-flex btn btn-primary btn-md dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split" 
                data-bs-toggle="dropdown" 
                aria-expanded="false"
                style="border-radius: 0 0.475rem 0.475rem 0;border-left: 1px solid #dee2e6;"
            >
                <span class="svg-icon svg-icon-4">
                    <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                        <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                    </svg>
                </span>
            </button>
            {% endif %}
            {% if object_type == "slips"  %}
                {% if property_sets %}
                <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                    {% for set in property_sets %}
                    <li>
                        <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden create_new_wizard_button" id="{{object_type}}_set_create_record" type="button"
                            hx-get="{% url 'slip_drawer' %}" 
                            hx-vals = '{"set_id": "{{set.id}}" , "module":"{{menu_key}}"}'
                            hx-indicator=".loading-drawer-spinner,.create_new_form"
                            hx-target="#create-new-drawer-content"
                            hx-on="htmx:beforeSend: 
                                document.getElementById('manage-full-drawer-content').innerHTML = '';
                " 
                            style="border-radius: 0.475rem 0 0 0.475rem;"
                            >
                            {% if set.name %}
                                {{set.name}}
                            {% else %}
                                    {% if LANGUAGE_CODE == 'ja' %}売上伝票{% else %}Sales Slips{% endif %}
                            {% endif %}
                        </button>
                    </li>
                    {% endfor %}
                </ul>  
                {% endif %}
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>