{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}



<div class="w-50 p-10 py-8 position-sticky top-0 z-index-11 bg-white tw-overflow-x-auto tw-whitespace-nowrap d-flex justify-content-between align-items-center" style="background-color: #FAFAFA !important; z-index: 10;" 
    {% if object_type == 'task'%}
    style="z-index: 10 !important;"
    {% else %}
    style="z-index: 1 !important;"
    {% endif %}
    >
    <div class="d-flex tw-whitespace-nowrap" style="scrollbar-width: thin; overflow-x: none; white-space: nowrap; overflow-y: none;">
        {% for module in modules %}
            {% if module.slug == menu_key %}
                {% for module_object in module.object_values|split:"," %}
                    {% if workspace|is_show_module:module_object %}
                        {% if workspace|is_manage_object:module_object %}
                            {% if object_type == 'task' and module_object == 'task' %}
                                <div class="d-flex tw-items-center border-bottom border-primary border-bottom-2 me-4">
                                    <div class="tw-mr-1 fw-bolder fs-3 mt-0 text-dark tw-text-ellipsis tw-overflow-hidden">
                                        <span>
                                            {% if project.title %}
                                                {{ project.title }}
                                            {% else %}
                                                {% if LANGUAGE_CODE == 'ja' %}
                                                    プロジェクト
                                                {% else %}
                                                    Project
                                                {% endif %}
                                            {% endif %}
                                        </span>
                                    </div>
                            
                                    {% comment %} Setting Button {% endcomment %}
                                    {% if permission|check_permission:'edit' %}
                                    <button type="button" class="tw-bg-transparent tw-border-0 align-items-center d-flex tw-pl-0 tw-pt-1 view-wizard-button"
                                        {% if object_type == 'task'%}
                                            hx-vals='{"p_id": "{{project.id}}", "source": "taskflow", "module_slug" : "{{module.slug}}", "module_object": "{{module_object}}"}'
                                        {% elif object_type == 'workspace_setting'%}
                                            hx-vals='{"p_id": "{{project.id}}", "source": "workspace_setting", "module_slug" : "{{module.slug}}", "module_object": "{{module_object}}"}'
                                        {% endif %}
                                        hx-get="{% url 'projects_settings' %}" 
                                        hx-target="#view-drawer"
                                        >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                            <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                        </svg>
                            
                                    </button>
                                    {% endif %}
                            
                                    {% comment %} Dropdown Button {% endcomment %}
                                    <button type="button" 
                                        class="tw-w-[30px] tw-bg-transparent tw-border-0 align-items-center d-flex dropdown-toggle py-1 tw-pl-0 dropdown-toggle-split" 
                                        data-bs-toggle="dropdown" 
                                        aria-expanded="false"
                                        style="height: 26px;"
                                    >
                                        <span class="svg-icon svg-icon-4">
                                            <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                                                <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                                            </svg>
                                        </span>
                                    </button>
                                    <ul class="dropdown-menu tw-fixed tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                                        {% for project_item in projects %}
                                            {% if project_item.title %}
                                                <li><a 
                                                    class="dropdown-item tw-text-ellipsis tw-overflow-hidden" 
                                                    {% if object_type == 'task'%}
                                                    href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?p_id={{project_item.id}}"
                                                    {% elif object_type == 'workspace_setting'%}
                                                    href="{% host_url 'workspace_setting' host 'app' %}?setting_type=task&p_id={{project_item.id}}"
                                                    {% endif %}
                                                    >{{project_item.title}}
                                                </a></li>
                                            {% else %}
                                                <li><a 
                                                    class="dropdown-item tw-text-ellipsis tw-overflow-hidden" 
                                                    {% if object_type == 'task'%}
                                                    href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}"
                                                    {% elif object_type == 'workspace_setting'%}
                                                    href="{% host_url 'workspace_setting' host 'app' %}?setting_type=task"
                                                    {% endif %}
                                                    >{% if LANGUAGE_CODE == 'ja' %}プロジェクト{% else %}Project{% endif %}
                                                </a></li>
                                            {% endif %}
                                        {% endfor %}
                                        {% if permission|check_permission:'edit' %}
                                        <div class="border my-3">
                                        </div>
                                        <li>
                                        <button id="channel_create_button" class="btn py-1 px-3 view-wizard-button"
                                            hx-vals='{"source": "taskflow", "module_slug" : "{{module.slug}}", "module_object": "{{module_object}}"}'
                                            hx-get="{% url 'projects_settings' %}" 
                                            hx-target="#view-drawer">
                                            {% if LANGUAGE_CODE == 'ja'%}
                                                新規プロジェクト
                                            {% else %}
                                                New Project
                                            {% endif %}
                                        </button>
                            
                                        </li>
                                        {% endif %}
                                    </ul>
                                </div>
                            {% else %}
                                <div class="me-4 d-none">
                                    <a href="{% host_url 'load_object_page' module.slug module_object|object_type_slug:request host 'app' %}" class="fs-3 {% if object_type == module_object %} fw-bolder text-dark border-bottom border-bottom-2 border-primary {% else %} fw-bold text-gray-500 border-bottom border-bottom-2 {% endif %}">
                                        {{module_object|object_group_type:request}}
                                    </a>
                                </div>
                                {% if object_type == module_object %}
                                    <div class="tw-text-title-header-object tw-leading-[150%] tw-text-[#8F8E93]">
                                        {{module_object|object_group_type:request}}
                                    </div>
                                {% endif %}
                            {% endif %}
                        {% endif %}
                        {% if module_object|is_uuid %}
                            <div class="me-4 d-none">
                                <a href="{% host_url 'load_object_page' module.slug module_object|object_type_slug:request host 'app' %}" class="fs-3 {% if object_type == module_object|object_type_slug:request %} fw-bolder text-dark border-bottom border-bottom-2 border-primary {% else %} fw-bold text-gray-500 border-bottom border-bottom-2 {% endif %}">
                                    {{module_object|object_group_type:request}}
                                </a>
                            </div>
                            {% if object_type == module_object|object_type_slug:request %}
                                <div class="tw-text-title-header-object tw-leading-[150%] tw-text-[#8F8E93]">
                                    {{module_object|object_group_type:request}}
                                </div>
                            {% endif %}
                        {% endif %}
                    {% endif %}
                {% endfor %}
            {% endif %}
        {% endfor %}
    </div>

</div>
