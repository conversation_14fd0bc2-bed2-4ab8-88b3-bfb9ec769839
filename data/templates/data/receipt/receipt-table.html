{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}

<style>
    .receipt-item:hover .receipt-selection{
        border-width: 1px !important;
    }

    thead.position-sticky {
        position: sticky;
        top: 0;
        background: white;
        z-index: 10;
    }
</style>
<div class="receipt_table table-responsive" style="max-height: 75vh;">
    <table class="{% include "data/utility/table.html" %} receipts-table">
        <thead class="{% include "data/utility/table-header.html" %} position-sticky">
            <tr class="align-middle">
                <th class="min-w-40px"></th>
                {% for column in view_filter.column|safe|string_list_to_list %}
                    <th {% if column == 'checkbox' %}
                            class="w-10px"
                        {% elif column ==  'id_rcp' %}
                            class="{% include "data/utility/column-id.html" %}"
                        {% endif %}
                    >
                        {% with args=column|add:'|'|add:object_type %} 
                            {% if column|split:'|'|length > 1 and '_platform|' not in column %}
                                {% with channel_column=column|split:'|'|search_custom_field_object_order_customer:request %}
                                    {{channel_column.name}}
                                {% endwith %}
                            {% else %}
                                {% with column_display=args|get_column_display:request %}
                                    {{column_display.name}}
                                {% endwith %}
                            {% endif %}
                        {% endwith %}
                    </th>
                    {% if column ==  'id_rcp' %}
                    <th class="" style="width: 20px;">
                    </th>
                    {% endif %}
                {% endfor %}
            </tr>
        </thead>

        <tbody>
        {% for receipt in receipts %}
            {% include "data/common/dynamic_table/dynamic-datatable-row.html" with row_detail_url="receipt_row_detail" item=receipt %}
        {% endfor %}
        </tbody>
    </table>
</div>

{% if isopen and isopen != '' %}
    <div
        hx-get="{% host_url 'receipt_edit' isopen host 'app' %}"
        hx-target="#manage-full-drawer-content"
        hx-vals = '{"set_id": "{{set_id}}","module":"{{module}}","view_id":"{{view_id}}"}'
        hx-on:htmx:before-send="document.getElementById('manage-full-drawer-content').innerHTML = '';document.getElementById('create-new-drawer-content').innerHTML = '';"
        hx-trigger="click"
        hx-indicator=".loading-drawer-spinner,.billings-form"
        class="text-center mb-0 text-dark text-hover-primary fw-bolder manage_full_wizard_button cursor-pointer receipt_open">
    </div>
    <script>
        $(document).ready(function() {
            setTimeout(function() {
                document.querySelector('.receipt_open').click();
            }, 500);
        });
    </script>
{% endif %}

{% include "data/common/paginator/paginator.html" with redirect_url=pagination_url %}

{% include "data/common/dynamic_table/dynamic-datatable-js.html" with rows=receipts table_name="receipts" %}

{% if receipt_id %}
<a
    hx-get="{% host_url 'receipt_edit' receipt_id host 'app' %}"
    hx-target="#manage-full-drawer-content"
    hx-trigger="click"
    hx-indicator=".loading-drawer-spinner,.billings-form"
    class="receipt-open d-none text-center mb-0 text-dark text-hover-primary fw-bolder manage_full_wizard_button cursor-pointer receipt_{{receipt_id}}">
</a>
<script>
    $(document).ready(function() {
        setTimeout(function() {
            document.getElementsByClassName('receipt-open')[0].click();
        }, 0);
    });
</script>
{% endif %}