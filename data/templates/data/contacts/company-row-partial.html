{% load tz %}
{% load custom_tags %}
{% load i18n %}
{% load hosts %}
{% load humanize %}
{% get_current_language as LANGUAGE_CODE %}


{% for row_type in companies_columns %}
    {% if "checkbox" == row_type %}
    <td class="{% include "data/utility/column-checkbox.html" %}">
        <input id="company-selection-{{company.id}}" class="form-check-input cursor-pointer company-selection check_input" type="checkbox" name="checkbox" value="{{company.id}}" data-owner="{{company.owner.user.id}}" onclick="checking_checkbox(this, event)"/>
    </td>

    {% elif "image_file" == row_type %}
    <td class="text-nowrap">
            <a class="text-dark cursor-pointer customer-manage-wizard"
                hx-get="{% host_url 'load_explore_company' company.id host 'app' %}"
                hx-target="#customer-manage-drawer"
                hx-trigger="click"
                >  
            
                {% if company.image_url %}
                <div class="symbol symbol-30px">
                    <img alt="Pic" src="{{ company.image_url }}" style="object-fit:cover !important; aspect-ratio: 9 / 9;"/>
                </div>
                {% elif company.image_file %}
                <div class="symbol symbol-30px">
                    <img alt="Pic" src="{{ company.image_file.url }}" style="object-fit:cover !important; aspect-ratio: 9 / 9;"/>
                </div>
                {% else %}
                <span class="svg-icon svg-icon-muted svg-icon-2x">
                    <svg class="h-30px w-30px" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path opacity="0.3" d="M20 15H4C2.9 15 2 14.1 2 13V7C2 6.4 2.4 6 3 6H21C21.6 6 22 6.4 22 7V13C22 14.1 21.1 15 20 15ZM13 12H11C10.5 12 10 12.4 10 13V16C10 16.5 10.4 17 11 17H13C13.6 17 14 16.6 14 16V13C14 12.4 13.6 12 13 12Z" fill="currentColor"/>
                        <path d="M14 6V5H10V6H8V5C8 3.9 8.9 3 10 3H14C15.1 3 16 3.9 16 5V6H14ZM20 15H14V16C14 16.6 13.5 17 13 17H11C10.5 17 10 16.6 10 16V15H4C3.6 15 3.3 14.9 3 14.7V18C3 19.1 3.9 20 5 20H19C20.1 20 21 19.1 21 18V14.7C20.7 14.9 20.4 15 20 15Z" fill="currentColor"/>
                    </svg>
                </span>    
                {% endif %}
            </a> 
    </td>

    {% elif "company_id" == row_type %}
    <td class="{% include "data/utility/column-id.html" %}" style="border-right: 1px solid !important; border-right-color: rgb(234, 234, 234) !important;">
        {% if company.company_id %}
        <div class="d-flex align-items-center">
            <span class="d-none shopturbo-manage-wizard-button company_{{company.id}}_activity"
                hx-get="{% host_url 'load_explore_company' company.id host 'app' %}"
                hx-vals='{"tab":"activity"}'
                hx-target="#customer-manage-drawer"
                hx-trigger="click"
                hx-on::before-request="document.getElementById('customer-manage-drawer').innerHTML = '';"
                >
            </span>
            <a  class="text-dark text-hover-primary cursor-pointer cursor-pointer customer-manage-wizard view_form_trigger{{company.id}} company_{{company.id}}" 
                hx-get="{% host_url 'load_explore_company' company.id host 'app' %}"
                hx-vals='{"view_id": "{{view_filter.view.id}}"}'
                hx-target="#customer-manage-drawer"
                hx-trigger="click"
                hx-on::before-request="document.getElementById('customer-manage-drawer').innerHTML = '';"
            >
            {{company.company_id|stringformat:"04d"}}
            </a>
            {% if property_sets %}
                <div class="dropdown">
                    <button type="button" class="btn btn-primary-outline text-dark text-hover-primary px-1 py-0 dropdown-toggle" data-bs-toggle="dropdown">
                        <span class="d-inline-flex align-items-center justify-content-center" style="width: 20px; height: 20px;">
                            <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                                <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                            </svg>
                        </span>
                    </button>
                    <ul class="dropdown-menu">
                        {% for set in property_sets %}
                        <li>
                            <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden customer-manage-wizard" type="button"
                                hx-get="{% host_url 'load_explore_company' company.id host 'app' %}"
                                hx-vals = '{"set_id": "{{set.id}}","view_id":"{{view_id}}"}'
                                hx-target="#customer-manage-drawer"
                                style="border-radius: 0.475rem 0 0 0.475rem;"
                                >
                                {% if set.name %}
                                    {{ set.name}}
                                {% else %}
                                    {% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default{% endif %} 
                                {% endif %}
                            </button>
                        </li>
                        {% endfor %}
                    </ul>
                </div>  
                {% endif %}
            </div>
        {% endif %}
    </td>
    <td class="" style="width: 20px;">
    </td>

    {% elif "owner" == row_type %}
    <td class="fw-bold">
        {% if company.owner and company.owner.user %}
            {% with company.owner.user as user %}
            {% if user.verification.profile_photo %}
            <img class="w-20px rounded-circle me-2" src="{{user.verification.profile_photo.url}}" style="object-fit: cover !important;aspect-ratio: 16 / 16;" />
            {% else %}
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.28548 15.0861C7.34369 13.1814 9.35142 12 11.5304 12H12.4696C14.6486 12 16.6563 13.1814 17.7145 15.0861L19.3493 18.0287C20.0899 19.3618 19.1259 21 17.601 21H6.39903C4.87406 21 3.91012 19.3618 4.65071 18.0287L6.28548 15.0861Z" fill="currentColor"/>
                    <rect opacity="0.3" x="8" y="3" width="8" height="8" rx="4" fill="currentColor"/>
                </svg>    
            {% endif %}
            <span >
                {{user.first_name}}
            </span>
            {% endwith %}
        {% endif %}
    </td>
    {% elif "name" == row_type %}
    <td class="fw-bold">
            <a class="text-dark cursor-pointer customer-manage-wizard company_{{company.id}}"
                hx-get="{% host_url 'load_explore_company' company.id host 'app' %}"
                hx-target="#customer-manage-drawer"
                hx-trigger="click"
                >
                {{company.name}}
            </a>
    </td>

    {% elif "descriptions" == row_type %}
    <td class="text-nowrap">
            {% if company.descriptions %}
                {% if company.descriptions|length > 120 %}
                    {{company.descriptions|safe|truncatewords:5|truncatechars:50}}
                {% else %}
                    {{company.descriptions|safe|truncatewords:10|truncatechars:50}}
                {% endif %}
            {% endif %}
    </td>

    {% elif "lists" == row_type %}
    <td class="text-nowrap">
            {% for list in company.companylist.all %}
            <div class="rounded-2 p-2 bg-gray-200 mb-2" style="">
                {{list.name}}
            </div>
            {% endfor %}
    </td>

    {% elif "phone_number" == row_type %}
    <td class="text-nowrap">
            {% if company.phone_number %}
                {{company.phone_number}}
            {% else %}
                <div>----</div>
            {% endif %}
    </td>

    {% elif "address" == row_type %}
    <td class="text-nowrap">
            {% if company.address %}
                {{company.address}}
            {% endif %}

    </td>

    {% elif "url" == row_type %}
    <td class="text-nowrap">
            {% if company.url %}
                {{company.url}}
            {% endif %}

    </td>
        

    {% elif "email" == row_type %}
        <td class="text-nowrap">
            {% if company.email %}
                {{company.email}}
            {% else %}
            {% endif %}
    </td>

    {% elif "status" == row_type %}
    <td class="fw-bold text-nowrap">
        
        {% translate_lang company.get_status_display LANGUAGE_CODE %}
        
    </td>

    {% elif " - company id" in row_type|lower %}
    <td class="fw-bold text-nowrap">
        {% with company_platforms=company|search_company_channel_objects:request %}
            {% if company_platforms %}
                {% for company_platform in company_platforms %}
                    <div>
                        {{company.id|company_platform_name:row_type}}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </td>

    {% elif "| child" in row_type %}
    <td class="fw-bolder text-nowrap">
        {% with child_property_companies=company|checking_property_child %}
            {% for child_property_company in child_property_companies %}
                <div class="fw-bold">
                    <a class="{% include "data/utility/table-link.html" %} customer-manage-wizard company_{{child_property_company.id}}" 
                        hx-get="{% host_url 'load_explore_company' child_property_company.id host 'app' %}" 
                        hx-target="#customer-manage-drawer"
                        hx-trigger="click"
                    >   
                        {{child_property_company.company_id|stringformat:"04d"}}-{{child_property_company.name}}
                    </a>
                </div>
            {% endfor %}
        {% endwith %}
    </td>

    {% elif "updated_at" == row_type %}
    <td class="text-nowrap">
            {% date_format company.updated_at 1 %}

    </td>

    {% elif "created_at" == row_type %}
    <td class="text-nowrap">
            {% date_format company.created_at 1 %}

    </td>
    {% elif row_type|is_uuid %}
        <td class="fw-bold">
            <div class="text-start">
                    {% with item_id=company.id|stringify %}
                        {% with args=row_type|add:'|'|add:object_type|add:'|'|add:item_id %} 
                            {% with column_display=args|get_column_display:request %}
                                {% if column_display.type == 'formula' %}
                                    {% include "data/common/custom_field/formula/row-partial.html" with obj_id=company.id CustomFieldName_id=column_display.id %}                            
                                {% else %}
                                    {% include "data/common/custom_field/row-partial-list.html" with CustomFieldName=column_display obj_id=company.id value=column_display.value %}
                                {% endif %}
                            {% endwith %}
                        {% endwith %}   
                    {% endwith%}
            </div>
        </td>
    {% else %}
        <td class="fw-bold">
            <div class="text-start">
                    {% if company|get_attr:row_type %}
                        {{company|get_attr:row_type}}
                    {% endif %}
            </div>
        </td>
    
    {% endif %}
{% endfor %}
