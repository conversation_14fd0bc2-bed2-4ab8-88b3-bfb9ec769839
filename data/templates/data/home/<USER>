{% extends 'base.html' %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% load tz %}
{% block content %}

<style>
    html {
        scroll-padding-top: 80px;
      }
      
    textarea {
        resize: none;
    }

    .htmx-indicator, #workflow-spinner{
        display:none !important;
    }
    .htmx-request .htmx-indicator, #workflow-spinner.htmx-request{
        display:inline-block !important;
    }
    .htmx-request.htmx-indicator, #workflow-spinner.htmx-request{
        display:inline-block !important;
    }

    @keyframes fade {
        0% { opacity: 0; }  
        50% { opacity: 1; }
        100% { opacity: 0; }
    }

    .onboarding-content img {max-width: 100%;}

    .app-menu-box {
        position: relative
    }

    .dropdown-menu-container {
        inset: 5px auto auto 80px !important;
    }
    .page-menu.aside-menu {
        position: absolute;
        top: 0px;
        margin-left: 70px
    }
    .hide {
        display: none;
    }

</style>

{% if menu_key == 'home' %}

<div class="d-flex flex-column flex-lg-row">
	<div class="w-100">

        <div id='notifications'></div>
            
            <div class="mb-5 border-0">
                <div class="p-10 h-80px mb-0 d-flex justify-content-between align-items-center" style="background-color: #FAFAFA !important;">
                    <div class="d-flex tw-items-center tw-max-w-[40%] me-4 ">
                        <div class="tw-mr-1 fw-bolder tw-text-[#8F8E93] tw-text-title-header-object mt-0 tw-text-ellipsis tw-overflow-hidden">
                            {% if LANGUAGE_CODE == 'ja'%}
                            ホーム
                            {% else %}
                            Home
                            {% endif %}
                        </div>
                    </div>
                    <button class="align-items-center d-flex btn btn-primary py-1 rounded-1 h-30px edit-widgets-button" type="button"
                        hx-get="{% host_url 'widgets' host 'app' %}"
                        hx-target="#edit-widgets-drawer"
                        hx-trigger="click" >
                        <span class="svg-icon svg-icon-light-primary svg-icon-4">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path opacity="0.3" d="M22.1 11.5V12.6C22.1 13.2 21.7 13.6 21.2 13.7L19.9 13.9C19.7 14.7 19.4 15.5 18.9 16.2L19.7 17.2999C20 17.6999 20 18.3999 19.6 18.7999L18.8 19.6C18.4 20 17.8 20 17.3 19.7L16.2 18.9C15.5 19.3 14.7 19.7 13.9 19.9L13.7 21.2C13.6 21.7 13.1 22.1 12.6 22.1H11.5C10.9 22.1 10.5 21.7 10.4 21.2L10.2 19.9C9.4 19.7 8.6 19.4 7.9 18.9L6.8 19.7C6.4 20 5.7 20 5.3 19.6L4.5 18.7999C4.1 18.3999 4.1 17.7999 4.4 17.2999L5.2 16.2C4.8 15.5 4.4 14.7 4.2 13.9L2.9 13.7C2.4 13.6 2 13.1 2 12.6V11.5C2 10.9 2.4 10.5 2.9 10.4L4.2 10.2C4.4 9.39995 4.7 8.60002 5.2 7.90002L4.4 6.79993C4.1 6.39993 4.1 5.69993 4.5 5.29993L5.3 4.5C5.7 4.1 6.3 4.10002 6.8 4.40002L7.9 5.19995C8.6 4.79995 9.4 4.39995 10.2 4.19995L10.4 2.90002C10.5 2.40002 11 2 11.5 2H12.6C13.2 2 13.6 2.40002 13.7 2.90002L13.9 4.19995C14.7 4.39995 15.5 4.69995 16.2 5.19995L17.3 4.40002C17.7 4.10002 18.4 4.1 18.8 4.5L19.6 5.29993C20 5.69993 20 6.29993 19.7 6.79993L18.9 7.90002C19.3 8.60002 19.7 9.39995 19.9 10.2L21.2 10.4C21.7 10.5 22.1 11 22.1 11.5ZM12.1 8.59998C10.2 8.59998 8.6 10.2 8.6 12.1C8.6 14 10.2 15.6 12.1 15.6C14 15.6 15.6 14 15.6 12.1C15.6 10.2 14 8.59998 12.1 8.59998Z" fill="currentColor"/>
                                <path d="M17.1 12.1C17.1 14.9 14.9 17.1 12.1 17.1C9.30001 17.1 7.10001 14.9 7.10001 12.1C7.10001 9.29998 9.30001 7.09998 12.1 7.09998C14.9 7.09998 17.1 9.29998 17.1 12.1ZM12.1 10.1C11 10.1 10.1 11 10.1 12.1C10.1 13.2 11 14.1 12.1 14.1C13.2 14.1 14.1 13.2 14.1 12.1C14.1 11 13.2 10.1 12.1 10.1Z" fill="currentColor"/>
                            </svg>
                        </span>

                        <span class="fs-7 ps-1 fw-bolder">
                            {% if LANGUAGE_CODE == 'ja'%}
                            ウィジェット
                            {% else %}
                            Widgets
                            {% endif %}
                        </span>
                    </button>
                </div>

                <div id="widgets-outter" class="my-5">
                    <div id="widgets-card"> 
                        {% for widget in widgets %}                      
                            {% if widget.special_type == 'onboarding' %}
                                {% include 'data/widget/onboarding-widget.html' %}
                            {% elif widget.special_type == 'demo-bot' %}
                                {% include 'data/widget/demo-bot-widget.html' %}
                            {% elif 'reports' in widget.special_type %}
                                {% include 'data/widget/widget-card.html' %}
                            {% elif widget.special_type != all_apps or widget.special_type != recommend_resources %}
                                <div hx-get='{% host_url "solution_widgets" widget.special_type host "app" %}' hx-swap="outerHTML" hx-target="this"
                                    hx-trigger="load">
                                    <style>
                                        .solution-indicator{{forloop.counter}}{
                                            display:none;
                                        }
                                        .htmx-request .solution-indicator{{forloop.counter}}{
                                            display:inline-block;
                                        }
                                        .htmx-request.solution-indicator{{forloop.counter}}{
                                            display:inline-block;
                                        }
                                    </style>
                                    <span class="spinner-border spinner-border-sm text-secondary solution-indicator{{forloop.counter}}" style="position:relative; right:-6px" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </span>
                                </div>
                            {% endif %}
                        {% endfor %}    
                    </div>
                </div>  
            </div>

            {% for widget in widgets %}

            {% endfor %}


    </div>               
</div>

{% elif menu_key == 'search' %}

    <style>
        .tab-pane {display: none;}
    </style>
    <div class="d-flex flex-column flex-lg-row">

        <div class="mb-5 px-10 mt-8 w-100">
            <h1 class="mt-0 mb-5">
                {% if LANGUAGE_CODE == 'ja'%}
                検索結果:{{q}}
                {% else %}
                Search Results:{{q}}
                {% endif %}
            </h1>
        </div>
    </div>

    <div class="px-lg-10 px-5 w-100">
        {% if workflows %}

        <div class="mb-10">
            <div class="py-5 mb-5 align-items-center py-0 d-flex justify-content-between border-bottom">
                <h3 class="my-0">
                    {% if LANGUAGE_CODE == 'ja'%}ワークフロー{% else %}Workflows{% endif %}
                </h3>
            </div>
            {% if permissions.workflow == 'hide' %}
                <h5 class="text-center text-muted fw-boldest" style="letter-spacing:0.25px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    このページはアクセス権がありません。
                    {% else %}
                    You don't have permission to access this page.
                    {% endif %}
                </h5>
            {% else %}
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <tbody>
                    {% for workflow in workflows %}
                    
                    <tr>
                        <td class="min-w-250px">
                            <div>
                                {{workflow.title}}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}
        {% if task %}

        <div class="mb-10">
            <div class="py-5 mb-5 align-items-center py-0 d-flex justify-content-between border-bottom">
                <h3 class="my-0">
                    {% if LANGUAGE_CODE == 'ja'%}タスク{% else %}Tasks{% endif %}
                </h3>
            </div>
            {% if permissions.task == 'hide' %}
                <h5 class="text-center text-muted fw-boldest" style="letter-spacing:0.25px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    このページはアクセス権がありません。
                    {% else %}
                    You don't have permission to access this page.
                    {% endif %}
                </h5>
            {% else %}
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <tbody>
                    {% for task_ in task %}
                    
                    <tr>
                        <td class="min-w-250px">
                            <div>
                                {{task_.title}}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}
        
        {% if company %}
        <div class="mb-10">
            <div class="py-5 mb-5 align-items-center py-0 d-flex justify-content-between border-bottom">
                <h3 class="my-0">
                    {% if LANGUAGE_CODE == 'ja'%}企業{% else %}Company{% endif %}
                </h3>
            </div>
            {% if permissions.company == 'hide' %}
                <h5 class="text-center text-muted fw-boldest" style="letter-spacing:0.25px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    このページはアクセス権がありません。
                    {% else %}
                    You don't have permission to access this page.
                    {% endif %}
                </h5>
            {% else %}
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <tbody>
                    {% for co in company %}
                    <tr>
                        <td class="min-w-250px">
                            <div>
                                <a class="cursor-pointer create-view-button-drawer company_{{co.id}}" 
                                    id="company__{{co.id}}"
                                    hx-get="{% host_url 'load_explore_company' co.id host 'app' %}"
                                    hx-vals='{"view_id": "{{view_filter.view.id}}"}'
                                    hx-target="#view-drawer-target"
                                    hx-trigger="click"
                                >
                                {{co.name}}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}

        {% if contacts %}
        <div class="mb-10">
            <div class="py-5 mb-5 align-items-center py-0 d-flex justify-content-between border-bottom">
                <h3 class="my-0">
                    {% if LANGUAGE_CODE == 'ja'%}連絡先{% else %}Contacts{% endif %}
                </h3>
            </div>
            {% if permissions.contacts == 'hide' %}
                <h5 class="text-center text-muted fw-boldest" style="letter-spacing:0.25px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    このページはアクセス権がありません。
                    {% else %}
                    You don't have permission to access this page.
                    {% endif %}
                </h5>
            {% else %}
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <tbody>
                    {% for contact in contacts %}
                    <tr>
                        <td class="min-w-250px">
                            <div>
                                <a class="cursor-pointer create-view-button-drawer contact_{{contact.id}}" 
                                    id="contacts__{{contact.id}}"
                                    hx-get="{% host_url 'load_explore_profile' contact.id host 'app' %}"
                                    hx-vals='{"view_id": "{{view_filter.view.id}}"}'
                                    hx-target="#view-drawer-target"
                                    hx-trigger="click"
                                >
                                {{contact|display_contact_name:LANGUAGE_CODE}}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}

        {% if commerce_orders %}
        <div class="mb-10">
            <div class="py-5 mb-5 align-items-center py-0 d-flex justify-content-between border-bottom">
                <h3 class="my-0">
                    {% if LANGUAGE_CODE == 'ja'%}注文{% else %}Orders{% endif %}
                </h3>
            </div>
            {% if permissions.commerce_orders == 'hide' %}
                <h5 class="text-center text-muted fw-boldest" style="letter-spacing:0.25px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    このページはアクセス権がありません。
                    {% else %}
                    You don't have permission to access this page.
                    {% endif %}
                </h5>
            {% else %}
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <tbody>
                    {% for order in commerce_orders %}
                    <tr>
                        <td class="min-w-250px">
                            <div>
                                <a href="{% host_url 'load_object_page' map_module_slug_by_object_value.orders 'orders' host 'app' %}?id={{order.id}}&target=commerce_orders" >
                                    #{{order.order_id}} - {{order.created_at}}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}

        {% if commerce_items %}
        <div class="mb-10">
            <div class="py-5 mb-5 align-items-center py-0 d-flex justify-content-between border-bottom">
                <h3 class="my-0">
                    {% if LANGUAGE_CODE == 'ja'%}商品{% else %}Items{% endif %}
                </h3>
            </div>
            {% if permissions.commerce_items == 'hide' %}
                <h5 class="text-center text-muted fw-boldest" style="letter-spacing:0.25px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    このページはアクセス権がありません。
                    {% else %}
                    You don't have permission to access this page.
                    {% endif %}
                </h5>
            {% else %}
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <tbody>
                    {% for item in commerce_items %}
                    <tr>
                        <td class="min-w-250px">
                            <div>
                                <a href="{% host_url 'load_object_page' map_module_slug_by_object_value.items 'items' host 'app' %}?item_id={{item.id}}&target=commerce_items" >
                                    {% get_object_display item 'commerce_items' %}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}

        {% if commerce_inventory %}
        <div class="mb-10">
            <div class="py-5 mb-5 align-items-center py-0 d-flex justify-content-between border-bottom">
                <h3 class="my-0">
                    {% if LANGUAGE_CODE == 'ja'%}在庫{% else %}Inventory{% endif %}
                </h3>
            </div>
            {% if permissions.commerce_inventory == 'hide' %}
                <h5 class="text-center text-muted fw-boldest" style="letter-spacing:0.25px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    このページはアクセス権がありません。
                    {% else %}
                    You don't have permission to access this page.
                    {% endif %}
                </h5>
            {% else %}
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <tbody>
                    {% for item in commerce_inventory %}
                    <tr>
                        <td class="min-w-250px">
                            <div>
                                <a
                                    hx-get="{% url 'shopturbo_load_drawer' %}" 
                                    hx-vals = '{"tab":"activity", "drawer_type":"inventory-manage", "inventory_id":"{{item.id}}", "module":"{{menu_key}}" }'
                                    hx-target="#view-drawer-target"
                                    hx-trigger="click"
                                    class="cursor-pointer create-view-button-drawer"
                                >
                                    {% get_object_display item 'commerce_inventory' %}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}
        
        {% if commerce_inventory_transaction %}
        <div class="mb-10">
            <div class="py-5 mb-5 align-items-center py-0 d-flex justify-content-between border-bottom">
                <h3 class="my-0">
                    {% if LANGUAGE_CODE == 'ja'%}入出庫{% else %}Inventory Transaction{% endif %}
                </h3>
            </div>
            {% if permissions.commerce_inventory_transaction == 'hide' %}
                <h5 class="text-center text-muted fw-boldest" style="letter-spacing:0.25px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    このページはアクセス権がありません。
                    {% else %}
                    You don't have permission to access this page.
                    {% endif %}
                </h5>
            {% else %}
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <tbody>
                    {% for item in commerce_inventory_transaction %}
                    <tr>
                        <td class="min-w-250px">
                            <div>
                                <a
                                    hx-get="{% url 'shopturbo_load_drawer' %}" 
                                    hx-vals = '{"drawer_type":"inventory-transaction-manage", "transaction_id":"{{item.id}}", "module":"{{menu_key}}" }'
                                    hx-target="#view-drawer-target"
                                    hx-trigger="click"
                                    class="cursor-pointer create-view-button-drawer"
                                >
                                    {% get_object_display item 'commerce_inventory_transaction' %}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}

        {% if commerce_inventory_warehouse %}
        <div class="mb-10">
            <div class="py-5 mb-5 align-items-center py-0 d-flex justify-content-between border-bottom">
                <h3 class="my-0">
                    {% if LANGUAGE_CODE == 'ja'%}ロケーション{% else %}Location{% endif %}
                </h3>
            </div>
            {% if permissions.commerce_inventory_warehouse == 'hide' %}
                <h5 class="text-center text-muted fw-boldest" style="letter-spacing:0.25px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    このページはアクセス権がありません。
                    {% else %}
                    You don't have permission to access this page.
                    {% endif %}
                </h5>
            {% else %}
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <tbody>
                    {% for item in commerce_inventory_warehouse %}
                    <tr>
                        <td class="min-w-250px">
                            <div>
                                <a
                                    hx-get="{% url 'shopturbo_load_drawer' %}" 
                                    hx-vals = '{"drawer_type":"inventory-warehouse-manage", "warehouse_id":"{{item.id}}", "module": "{{menu_key}}" }'
                                    hx-target="#view-drawer-target"
                                    hx-trigger="click"
                                    class="cursor-pointer create-view-button-drawer"
                                >
                                    {% get_object_display item 'commerce_inventory_warehouse' %}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}
        
        {% if purchaseorder %}
        <div class="mb-10">
            <div class="py-5 mb-5 align-items-center py-0 d-flex justify-content-between border-bottom">
                <h3 class="my-0">
                    {% if LANGUAGE_CODE == 'ja'%}発注{% else %}Purchase Orders{% endif %}
                </h3>
            </div>
            {% if permissions.purchaseorder == 'hide' %}
                <h5 class="text-center text-muted fw-boldest" style="letter-spacing:0.25px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    このページはアクセス権がありません。
                    {% else %}
                    You don't have permission to access this page.
                    {% endif %}
                </h5>
            {% else %}
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <tbody>
                    {% for item in purchaseorder %}
                    <tr>
                        <td class="min-w-250px">
                            <div>
                                <a
                                    hx-get="{% host_url 'purchase_manage' item.id host 'app' %}?module={{menu_key}}"
                                    hx-target="#view-drawer-target"
                                    hx-trigger="click"
                                    class="cursor-pointer create-view-button-drawer"
                                >
                                    {% get_object_display item 'purchaseorder' %}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}

        {% if billing %}
        <div class="mb-10">
            <div class="py-5 mb-5 align-items-center py-0 d-flex justify-content-between border-bottom">
                <h3 class="my-0">
                    {% if LANGUAGE_CODE == 'ja'%}支払請求{% else %}Bills{% endif %}
                </h3>
            </div>
            {% if permissions.billing == 'hide' %}
                <h5 class="text-center text-muted fw-boldest" style="letter-spacing:0.25px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    このページはアクセス権がありません。
                    {% else %}
                    You don't have permission to access this page.
                    {% endif %}
                </h5>
            {% else %}
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <tbody>
                    {% for item in billing %}
                    <tr>
                        <td class="min-w-250px">
                            <div>
                                <a
                                    hx-get="{% host_url 'bill_manage' item.id host 'app' %}"
                                    hx-target="#view-drawer-target"
                                    hx-trigger="click"
                                    class="cursor-pointer create-view-button-drawer"
                                >
                                    {% get_object_display item 'billing' %}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}
        
        {% if spendpocket %}
        <div class="mb-10">
            <div class="py-5 mb-5 align-items-center py-0 d-flex justify-content-between border-bottom">
                <h3 class="my-0">
                    {% if LANGUAGE_CODE == 'ja'%}経費{% else %}Expenses{% endif %}
                </h3>
            </div>
            {% if permissions.spendpocket == 'hide' %}
                <h5 class="text-center text-muted fw-boldest" style="letter-spacing:0.25px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    このページはアクセス権がありません。
                    {% else %}
                    You don't have permission to access this page.
                    {% endif %}
                </h5>
            {% else %}
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <tbody>
                    {% for item in spendpocket %}
                    <tr>
                        <td class="min-w-250px">
                            <div>
                                <a
                                    hx-get="{% host_url 'expense_drawer' item.id host 'app' %}"
                                    hx-target="#view-drawer-target"
                                    hx-trigger="click"
                                    class="cursor-pointer create-view-button-drawer"
                                >
                                    {% get_object_display item 'spendpocket' %}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}

        {% if commerce_subscription %}
        <div class="mb-10">
            <div class="py-5 mb-5 align-items-center py-0 d-flex justify-content-between border-bottom">
                <h3 class="my-0">
                    {% if LANGUAGE_CODE == 'ja'%}サブスクリプション{% else %}Subscriptions{% endif %}
                </h3>
            </div>
            {% if permissions.commerce_subscription == 'hide' %}
                <h5 class="text-center text-muted fw-boldest" style="letter-spacing:0.25px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    このページはアクセス権がありません。
                    {% else %}
                    You don't have permission to access this page.
                    {% endif %}
                </h5>
            {% else %}
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <tbody>
                    {% for subscription in commerce_subscription %}
                    <tr>
                        <td class="min-w-250px">
                            <div>
                                <a href="{% host_url 'load_object_page' map_module_slug_by_object_value.subscriptions 'subscriptions' host 'app' %}?id={{subscription.id}}&target=commerce_subscription" >
                                    #{{subscription.subscriptions_id}} - {{subscription.created_at}}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}

        {% if reports %}
        <div class="mb-10">
            <div class="py-5 mb-5 align-items-center py-0 d-flex justify-content-between border-bottom">
                <h3 class="my-0">
                    {% if LANGUAGE_CODE == 'ja'%}レポート{% else %}Report{% endif %}
                </h3>
            </div>
            {% if permissions.panels == 'hide' %}
                <h5 class="text-center text-muted fw-boldest" style="letter-spacing:0.25px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    このページはアクセス権がありません。
                    {% else %}
                    You don't have permission to access this page.
                    {% endif %}
                </h5>
            {% else %}
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <tbody>
                    {% for report in reports %}
                    <tr>
                        <td class="min-w-250px">
                            <div>
                                <a href="{% host_url 'report_view' report.id host 'app' %}" >
                                    {{report.name}} - {{report.created_at}}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}

        {% if panels %}
        <div class="mb-10">
            <div class="py-5 mb-5 align-items-center py-0 d-flex justify-content-between border-bottom">
                <h3 class="my-0">
                    {% if LANGUAGE_CODE == 'ja'%}ダッシュボード{% else %}Dashboard{% endif %}
                </h3>
            </div>
            {% if permissions.panels == 'hide' %}
                <h5 class="text-center text-muted fw-boldest" style="letter-spacing:0.25px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    このページはアクセス権がありません。
                    {% else %}
                    You don't have permission to access this page.
                    {% endif %}
                </h5>
            {% else %}
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <tbody>
                    {% for panel in panels %}
                    <tr>
                        <td class="min-w-250px">
                            <div>
                                <a href="{% host_url 'panel_view' panel.id host 'app' %}" >
                                    {{panel.name}} - {{panel.created_at}}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}

        {% if estimates %}
        <div class="mb-10">
            <div class="py-5 mb-5 align-items-center py-0 d-flex justify-content-between border-bottom">
                <h3 class="my-0">
                    {% if LANGUAGE_CODE == 'ja'%}見積{% else %}Estimate{% endif %}
                </h3>
            </div>
            {% if permissions.estimates == 'hide' %}
                <h5 class="text-center text-muted fw-boldest" style="letter-spacing:0.25px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    このページはアクセス権がありません。
                    {% else %}
                    You don't have permission to access this page.
                    {% endif %}
                </h5>
            {% else %}
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <tbody>
                    {% for estimate in estimates %}
                    <tr>
                        <td class="min-w-250px">
                            <div>
                                <a 
                                    hx-get="{% host_url 'estimate_edit' estimate.id host 'app' %}"  
                                    hx-target="#view-drawer-target"
                                    hx-trigger="click"
                                    class="cursor-pointer create-view-button-drawer"
                                >
                                    #{{estimate.id_est|stringformat:"04d"}} - {{estimate.created_at}}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}

        {% if invoices %}
        <div class="mb-10">
            <div class="py-5 mb-5 align-items-center py-0 d-flex justify-content-between border-bottom">
                <h3 class="my-0">
                    {% if LANGUAGE_CODE == 'ja'%}請求{% else %}Invoice{% endif %}
                </h3>
            </div>
            {% if permissions.invoices == 'hide' %}
                <h5 class="text-center text-muted fw-boldest" style="letter-spacing:0.25px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    このページはアクセス権がありません。
                    {% else %}
                    You don't have permission to access this page.
                    {% endif %}
                </h5>
            {% else %}
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <tbody>
                    {% for invoice in invoices %}
                    <tr>
                        <td class="min-w-250px">
                            <div>
                                <a 
                                    hx-get="{% host_url 'invoice_edit' invoice.id host 'app' %}"  
                                    hx-target="#view-drawer-target"
                                    hx-trigger="click"
                                    class="cursor-pointer create-view-button-drawer"
                                >
                                    #{{invoice.id_inv|stringformat:"04d"}} - {{invoice.created_at}}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}

        {% if conversation %}
        <div class="mb-10">
            <div class="py-5 mb-5 align-items-center py-0 d-flex justify-content-between border-bottom">
                <h3 class="my-0">
                    {% if LANGUAGE_CODE == 'ja'%}メッセージ{% else %}Message{% endif %}
                </h3>
            </div>
            {% if permissions.conversation == 'hide' %}
                <h5 class="text-center text-muted fw-boldest" style="letter-spacing:0.25px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    このページはアクセス権がありません。
                    {% else %}
                    You don't have permission to access this page.
                    {% endif %}
                </h5>
            {% else %}
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <tbody>
                    {% for msg in conversation %}
                    <tr>
                        <td class="min-w-250px">
                            <div>
                                <a 
                                    {% if messages|length == 1 %}
                                        {% with message=messages|first %}
                                            {% if message.status == 'draft' %}
                                                hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                                                hx-vals='{"drawer_type":"manage-message", "message_id" : "{{message.id}}"}'
                                            {% endif %}
                                        {% endwith %}
                                    {% endif %}
                                    hx-get="{% host_url 'message_detail' msg.id host 'app' %}"  
                                    hx-target="#view-drawer-target"
                                    hx-trigger="click"
                                    class="cursor-pointer create-view-button-drawer"
                                >
                                    {% if msg.contacts.all %}
                                    {% for contact in msg.contacts.all %}
                                            {{contact.name}} 
                                        {% endfor %}
                                    {% else %}
                                        {{msg.channel.name}}
                                    {% endif %}

                                    <div class="fs-6 text-gray-700">{{msg|latest_message_body|truncatechars:50}}</div>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}

        {% if customer_case %}
        <div class="mb-10">
            <div class="py-5 mb-5 align-items-center py-0 d-flex justify-content-between border-bottom">
                <h3 class="my-0">
                    {% if LANGUAGE_CODE == 'ja'%}案件{% else %}Cases{% endif %}
                </h3>
            </div>
            {% if permissions.customer_case == 'hide' %}
                <h5 class="text-center text-muted fw-boldest" style="letter-spacing:0.25px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    このページはアクセス権がありません。
                    {% else %}
                    You don't have permission to access this page.
                    {% endif %}
                </h5>
            {% else %}
            <table class="table table-row-bordered table-row-dashed gy-4 align-middle">
                <tbody>
                    {% for case in customer_case %}
                    <tr>
                        <td class="min-w-250px">
                            <div>
                                <a 
                                    hx-get="{% host_url 'new_customerlinkapp_drawer' host 'app' %}"
                                    hx-vals = '{"drawer_type":"manage-deal", "deal_id":"{{case.id}}" ,"module":"{{menu_key}}"}'  
                                    hx-target="#view-drawer-target"
                                    hx-trigger="click"
                                    class="cursor-pointer create-view-button-drawer"
                                >
                                    {% get_object_display case 'customer_case' %}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
        {% endif %}


        {% if not has_any_results %}
        <h3 class="my-0">
            {% if LANGUAGE_CODE == 'ja'%}該当する結果がありません。{% else %}No result found.{% endif %}
        </h3>

        {% endif %}
    </div>


    {% if target and id %}
        <script>
            $(document).ready(function() {                // Wait 500 ms waiting all component loaded
                setTimeout(function() {
                    var element = document.getElementById("{{target}}__{{id}}");
                    element.scrollIntoView({
                        behavior: "smooth",
                        block: "start"
                    })
                    element.click();
                }, 500)
            })
        </script>
    {% endif %}
{% endif %}

<div id="edit-widgets-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".edit-widgets-button"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}"
    >
    <div id="edit-widgets-drawer" class="w-100"></div>
</div>

<div class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".create-view-button-drawer"
    data-kt-drawer-width="{'default':'90%','lg':'100%'}"
    >
    <div id="view-drawer-target" class="w-100"></div>
</div>

{% endblock %}


{% block js %}
<script>
    {% comment %} Gleap.openChecklists(); {% endcomment %}

    {% comment %} {% if onboarding_start %}
        Gleap.startChecklist("67e05bce1e1087bd9caa426e");
    {% endif %}
     {% endcomment %}
    
    function download(content, mimeType, filename){
        const a = document.createElement('a') // Create "a" element
        const blob = new Blob([content], {type: mimeType}) // Create a blob (file-like object)
        const url = URL.createObjectURL(blob) // Create an object URL from blob
        a.setAttribute('href', url) // Set "a" element link
        a.setAttribute('download', filename) // Set download filename
        a.click() // Start downloading
    }
    document.body.addEventListener('htmx:beforeSwap', function(evt) {
        a = evt.detail.xhr
        if(evt.detail.xhr.status == 200){
            if (evt.detail.xhr.getAllResponseHeaders().includes('content-disposition')) {
                if (evt.detail.xhr.getResponseHeader('content-disposition').includes('attachment;')){
                    evt.detail.shouldSwap = false;
                    var filename = evt.detail.xhr.getResponseHeader('content-disposition').split('"')[1]
                    var mime = evt.detail.xhr.getResponseHeader('content-type')
                    download(evt.detail.xhr.responseText.replace(/(\r\n|\n|\r)/gm,'\n'), mime, filename)
                    window.location.reload()
                }
            }
        }
    });
    document.body.addEventListener('htmx:afterSwap', function(evt) {
      a = evt.detail.xhr
      if (evt.detail.xhr.status == 200 && evt.detail.pathInfo.responsePath == '/home/<USER>/sorter') {
        var element = document.querySelector('#apps-load-trigger');
        if(element) {
            element.click();
        } else {
            console.log('Element not found');
        }
        var element = document.querySelector('#pin-apps-load-trigger');
        if(element) {
            element.click();
        } else {
            console.log('Element not found');
        }

        
      }
    });
    var sortable = null;
    var listElement = document.getElementById("AppSortableMainList_{{mobile}}");

    $("#customize-button").click(function(event){
      event.preventDefault();
      $(this).css("cssText", "display: none !important;");
      $("#done-button").css("display", "");
      $("#AppSortableMainList_{{mobile}} div.aside-menu").removeClass('hide');
      $("#AppSortableMainList_{{mobile}} div.aside-menu").addClass('show');


      if (!sortable){
        sortable = new Sortable(listElement, {
            animation: 150,
            ghostClass: 'blue-background-class'
        });
      }
    });

    $("#done-button").click(function(event){
      event.preventDefault();
      $(this).css("cssText", "display: none !important;");
      $("#customize-button").css("display", "");
      $("#AppSortableMainList_{{mobile}} div.aside-menu").removeClass('show');
      $("#AppSortableMainList_{{mobile}} div.aside-menu").addClass('hide');
      if (sortable != null && sortable){
        sortable.destroy();
        sortable = null
      }
    });

    $('.app-menu-box').on('dragstart', function () {
      $('#apps-load-trigger').click()
    });


</script>
    
{% endblock js %}
    