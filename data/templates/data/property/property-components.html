{% load tz %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}


<div class="{% include 'data/utility/form-label.html' %} cursor-pointer">
    
    <div class="d-none" 
        {% if object_type == 'commerce_items' %}
            hx-get="{% host_url 'shopturbo_load_drawer' host 'app' %}"
            hx-vals='js:{"drawer_type":"items","section":"items-components","item_id":"{{object_id}}","property_id":"{{property_id}}"}'
        {% endif %}

        hx-target="#add-items-components"
        hx-swap="beforeend"
        hx-trigger="load"
        ></div>
        
    <a id="htmx-trigger-button-add-items-components" class="mt-2 fs-7"
        {% if object_type == 'commerce_items' %}
            hx-get="{% host_url 'shopturbo_load_drawer' host 'app' %}"
            hx-vals='js:{"drawer_type":"items","section":"items-components","function_type":"add-item","item_id":"{{object_id}}","get_item_choosen":get_item_choosen(),"property_id":"{{property_id}}"}'
            onclick="console.log('HTMX click - commerce_items condition met');"
        {% else %}
            onclick="console.log('HTMX click - commerce_items condition NOT met: object_type={{object_type}}');"
        {% endif %}
        hx-target="#add-items-components"
        hx-swap="beforeend"
        hx-trigger="click"
    >
        <span class="svg-icon svg-icon-primary svg-icon-2x">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path opacity="0.3" d="M20 22H4C3.4 22 3 21.6 3 21V2H21V21C21 21.6 20.6 22 20 22Z" fill="currentColor"/>
                <path d="M12 14C9.2 14 7 11.8 7 9V5C7 4.4 7.4 4 8 4C8.6 4 9 4.4 9 5V9C9 10.7 10.3 12 12 12C13.7 12 15 10.7 15 9V5C15 4.4 15.4 4 16 4C16.6 4 17 4.4 17 5V9C17 11.8 14.8 14 12 14Z" fill="currentColor"/>
            </svg>
        </span>
        {% if LANGUAGE_CODE == 'ja'%}
        商品を構成プロパティとして追加
        {% else %}
        Add Items as Component Property
        {% endif %}
    </a>
</div>

<div id="add-items-components" class="w-100 mb-5"></div>

<script>
    // Check if get_item_choosen exists
    if (typeof get_item_choosen !== 'function') {
        console.error('get_item_choosen function is not defined');
        // Define a simple version to prevent errors
        function get_item_choosen() {
            console.log('Fallback get_item_choosen called');
            return [];
        }
    }
    
    // Add listener to log HTMX events
    document.addEventListener('htmx:beforeRequest', function(evt) {
        console.log('HTMX request starting:', evt.detail);
    });
    
    document.addEventListener('htmx:afterRequest', function(evt) {
        console.log('Response:', evt.detail.xhr.responseText);
    });
    
    document.addEventListener('htmx:responseError', function(evt) {
        console.error('HTMX response error:', evt.detail);
    });
    
    function delete_item_component(elm){
        elm.parentElement.parentElement.remove()
    }
</script>

{% if object_id %}
    {% if object_type == 'purchaseitem' %}
        <input hidden name='components|quantity|{{object_id}}|{{property_id}}'>
        <input hidden name='components|iShopTurboItemsValueCustomFieldd|{{object_id}}|{{property_id}}'>
    {% else %}
        <input hidden name='components|value|{{object_id}}|{{property_id}}'>
        <input hidden name='components-quantity'>
    {% endif %}
{% else %}
    <input hidden name='components|value|{{object_id}}|{{property_id}}'>
    <input hidden name='components-quantity'>
{% endif %}