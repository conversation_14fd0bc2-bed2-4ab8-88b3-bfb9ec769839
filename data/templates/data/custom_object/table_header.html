{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}

{% for object_column in custom_object_columns %}
    <th {% if object_column == 'checkbox'%}
        class="{% include 'data/utility/column-checkbox.html' %}"
        {% elif object_column == 'row_id' %}
        class="{% include 'data/utility/column-id.html' %}" {% if source_integration %} style="min-width: 120px;"{% endif %}
        {% elif object_column == 'dropdown' %}
        class="min-w-40px w-40px"
        {% else %}
            {% if source_integration %}
            style="min-width: 120px;"
            {% else %}
            class=""
            {% endif %}
        {% endif %}>
        {% if object_column != 'checkbox' and object_column != 'dropdown' %}
            {% if object_column|search_custom_object_property:request %}
                {% with channel_column=object_column|search_custom_object_property:request %}
                    {% if channel_column.type == 'image' or channel_column.type == 'image_group' %}
                        <div style="min-width: 200px; max-width: 210px;">
                            {{channel_column.name}}
                        </div>
                    {% else %}
                        {{channel_column.name}}
                    {% endif %}
                {% endwith %}
            {% elif "related_custom_object" in object_column %}
                {% if object_column|get_related_custom_object_name %}
                    {{object_column|get_related_custom_object_name}}
                {% endif %}
            {% else %}
                {% with column_value=object_column|display_column_custom_objects:request %}
                    {{column_value}}
                {% endwith %}
            {% endif %}
        {% endif %}
    </th>
    {% if object_column == 'row_id' %}
    <th class="" style="width: 20px;">
    </th>
    {% endif %}
{% endfor %}