{% load tz %}
{% load custom_tags %}
{% load i18n %}
{% load hosts %}
{% load humanize %}
{% load formatting_tags %}
{% get_current_language as LANGUAGE_CODE %}

<td class="{% include "data/utility/column-checkbox.html" %}">
    <input id="{{object.id}}" class="form-check-input cursor-pointer check_input" type="checkbox" name="items" data-owner="{{object.owner.user.id}}" value="{{object.id}}" onclick="checking_checkbox(this, event)"/>
</td>

{% for column in columns|safe|string_list_to_list %}
    {% if "id_po" == column %}
    <td class="{% include "data/utility/column-id.html" %}" style="border-right: 1px solid !important; border-right-color: rgb(234, 234, 234) !important; position:static !important;">
        
        <div class="d-flex align-items-center">
            <a hx-indicator=".loading-drawer-spinner,.procurement-full-form" 
                hx-get="{% host_url 'purchase_manage' object.id host 'app' %}?view_id={{view_id}}&page={{page}}&module={{menu_key}}&set_id={{set_id}}"  
                hx-target="#manage-full-drawer-content"
                hx-on::before-request="document.getElementById('manage-full-drawer-content').innerHTML = '';"
                class="manage-full-wizard-button text-center mb-0 text-dark text-hover-primary fw-bolder cursor-pointer view_form_trigger{{object.id}}">
                
                {{object.id_po|stringformat:"04d"}}
            </a>
            {% if property_sets %}
            <div class="dropdown">
                <button type="button" class="btn btn-primary-outline text-dark text-hover-primary px-1 py-0 dropdown-toggle" data-bs-toggle="dropdown">
                    <span class="d-inline-flex align-items-center justify-content-center" style="width: 20px; height: 20px;">
                        <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                            <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                        </svg>
                    </span>
                </button>
                <ul class="dropdown-menu">
                    {% for set in property_sets %}
                    <li>
                        <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden manage-full-wizard-button" type="button"
                            hx-get="{% host_url 'purchase_manage' object.id host 'app' %}?view_id={{view_id}}" 
                            hx-vals='{"set_id": "{{set.id}}"}' 
                            hx-target="#manage-full-drawer-content"
                            hx-indicator=".loading-drawer-spinner,.procurement-full-form" 
                            style="border-radius: 0.475rem 0 0 0.475rem;"
                            >
                            {% if set.name %}
                                {{ set.name}}
                            {% else %}
                                {% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default{% endif %} 
                            {% endif %}
                        </button>
                    </li>
                    {% endfor %}
                </ul>
            </div>  
            {% endif %}
        </div>
    </td>
    <td class="" style="width: 20px;">
    </td>

    {% elif "name" == column %}
            <td class="fw-bold" class="">
                <div class="fw-bold py-2">
                    {{object.name}}
                </div>
            </td>     
    {% elif "owner" == column %}
        <td class="fw-bold">
            {% if object.owner and object.owner.user %}
                {% with object.owner.user as user %}
                {% if user.verification.profile_photo %}
                <img class="w-20px rounded-circle me-2" src="{{user.verification.profile_photo.url}}" style="object-fit: cover !important;aspect-ratio: 16 / 16;" />
                {% else %}
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6.28548 15.0861C7.34369 13.1814 9.35142 12 11.5304 12H12.4696C14.6486 12 16.6563 13.1814 17.7145 15.0861L19.3493 18.0287C20.0899 19.3618 19.1259 21 17.601 21H6.39903C4.87406 21 3.91012 19.3618 4.65071 18.0287L6.28548 15.0861Z" fill="currentColor"/>
                        <rect opacity="0.3" x="8" y="3" width="8" height="8" rx="4" fill="currentColor"/>
                    </svg>    
                {% endif %}
                <span >
                    {{user.first_name}}
                </span>
                {% endwith %}
            {% endif %}
        </td>
    {% elif "status" == column %}
            <td class="fw-bold">
                <div class="text-start">
                    {% if 'status'|get_custom_property_object:object %}
                        {% with value_map_label='status'|get_custom_property_object:object|get_attr:'value'|string_list_to_list %}
                            {% for value, label in value_map_label.items %}
                                {% if object.status == value %}
                                    {{label}}
                                {% endif %}
                            {% endfor %}
                        {% endwith %}
                    {% else %}
                        {% with status=object|get_default_status_display:column %}
                            {% for key, value in  status.items %}
                                {% if object.status ==  key %}
                                    {% if LANGUAGE_CODE == 'ja' %}
                                        {{value.ja}}
                                    {% else %}
                                        {{value.en}}
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                        {% endwith %}
                    {% endif %}
                </div>
            </td>


    {% elif "tax_rate" == column %}
            <td class="fw-bold">
                <div class="text-start">
                    {% if object.tax_rate %}
                    {{object.tax_rate}}%
                    {% else %}
                    0.0%
                    {% endif %}
                </div>
            </td>
    {% elif "total_price" == column %}
    <td class="fw-bold">
            <div class="text-start">
                {{object.currency|convert_currency_to_symbol}}
                {% if object.total_price and object.total_price != 'None'  %}
                    {{object.total_price|use_thousand_separator_string_with_currency:object.currency}}
                {% else %}
                    0
                {% endif %}
            </div>

    </td>


    {% elif "total_price_without_tax" == column %}
        <td class="fw-bold">
                <div class="text-start">
                    {% if object.total_price_without_tax and object.total_price_without_tax != 'None' %}
                        {{object.currency|convert_currency_to_symbol}}
                        {{object.total_price_without_tax|use_thousand_separator_string_with_currency:object.currency}}
                    {% endif %}
                </div>
        </td>

    {% elif "date" in column or '_at' in column%}
    <td class="fw-bold">
            <div class="text-start">
                {% if object|get_attr:column %}
                    {% date_format object|get_attr:column 1 %}
                {% endif %}
            </div>
    </td>

    {% elif "supplier" in column %}
        {% include 'data/partials/partial-customer-handler.html' %}    

    {% elif "usage_status" == column %}
    <td class="fw-bold">
        <div class="text-start">
                {% translate_lang object.get_usage_status_display LANGUAGE_CODE %}
        </div>
    </td>
    
    {% elif "source_item" == column %}
        <td class="fw-bold">
                {% if item_ %}
                    <div class="text-start">
                        {% if item_.item %}
                            <a class="{% include "data/utility/table-link.html" %} manage-full-wizard-button item_{{item_.item.id}}" 
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-vals = '{"drawer_type":"item-manage", "item_id":"{{item_.item.id}}", "view_id":"" }'
                                hx-target="#manage-full-drawer-content"
                                hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"  
                                hx-trigger="click"
                            >
                                {% get_object_display item_.item 'commerce_items' %}
                            </a>
                        {% else %}
                            {{item_.item_name}}
                        {% endif %}
                    </div>
                {% else %}
                    {% for item_ in object.purchase_order_object.all %}
                        <div class="text-start">
                            {% if item_.item %}
                                <a class="{% include "data/utility/table-link.html" %} manage-full-wizard-button item_{{item_.item.id}}" 
                                    hx-get="{% url 'shopturbo_load_drawer' %}" 
                                    hx-vals = '{"drawer_type":"item-manage", "item_id":"{{item_.item.id}}", "view_id":"" }'
                                    hx-target="#manage-full-drawer-content"
                                    hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"  
                                    hx-trigger="click"
                                >
                                    {% get_object_display item_.item 'commerce_items' %}
                                </a>
                            {% else %}
                                {{item_.item_name}}
                            {% endif %}
                        </div>
                    {% endfor %}
                {% endif %}
        </td>

    {% elif "line_item" == column %}
        <td class="fw-bold">
                {% for item_ in object.purchase_order_object.all %}
                    <div class="text-start">
                        {% if item_.item %}
                            <a class="{% include "data/utility/table-link.html" %} manage-full-wizard-button item_{{item_.item.id}}" 
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-vals = '{"drawer_type":"item-manage", "item_id":"{{item_.item.id}}", "view_id":"" }'
                                hx-target="#manage-full-drawer-content"
                                hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"  
                                hx-trigger="click"
                            >
                                {% get_object_display item_.item 'commerce_items' %}
                            </a>
                        {% else %}
                            {{item_.item_name}}
                        {% endif %}
                        x {{item_.amount_item|as_int}}
                    </div>
                {% endfor %}
        </td>

    {% elif "line_item_name" == column %}
    <td class="fw-bold">
        <div class="text-start">
                {% if item_ %}
                    {% if item_.item %}
                        <a class="{% include "data/utility/table-link.html" %} manage-full-wizard-button item_{{item_.item.id}}" 
                            hx-get="{% url 'shopturbo_load_drawer' %}" 
                            hx-vals = '{"drawer_type":"item-manage", "item_id":"{{item_.item.id}}", "view_id":"" }'
                            hx-target="#manage-full-drawer-content"
                            hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"  
                            hx-trigger="click"
                        >
                            {% get_object_display item_.item 'commerce_items' %}
                        </a>
                    {% else %}
                        {{item_.item_name|resolve_item_name}}
                    {% endif %}
                {% else %}
                    {% for item_ in object.purchase_order_object.all %}
                        {% if item_.item %}
                            <a class="{% include "data/utility/table-link.html" %} manage-full-wizard-button item_{{item_.item.id}}" 
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-vals = '{"drawer_type":"item-manage", "item_id":"{{item_.item.id}}", "view_id":"" }'
                                hx-target="#manage-full-drawer-content"
                                hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"  
                                hx-trigger="click"
                            >
                                {% get_object_display item_.item 'commerce_items' %}
                            </a>
                        {% else %}
                            {{item_.item_name|resolve_item_name}}
                        {% endif %}
                    </br>
                    {% endfor %}
                {% endif %}
        </div>
    </td>

    {% elif "line_item_quantity" == column %}
    <td class="fw-bold">
        <div class="text-start">
                {% if item_ %}
                    {{item_.amount_item|as_int}}
                {% else %}
                    {% for item_ in object.purchase_order_object.all %}
                    <div>
                        {{item_.amount_item|as_int}}
                    </div>
                    {% endfor %}
                {% endif %}
        </div>
    </td>
    {% elif "line_item_name_quantity" == column %}
    <td class="fw-bold">
        <div class="text-start">
                {% if item_ %}
                    {{item_.amount_item|as_int}}
                {% else %}
                    {% for item_ in object.purchase_order_object.all %}
                    <div>
                        {{item_.item_name|resolve_item_name}} x {{item_.amount_item|as_int}}
                    </div>
                    {% endfor %}
                {% endif %}
        </div>
    </td>

    {% elif "line_item_price" == column %}
    <td class="fw-bold">
        <div class="text-start">
                {% if item_ %}
                    {% if item_.purchase_item %}
                        <a hx-indicator=".loading-drawer-spinner,.expenses-form" 
                            id="payment_wizard_button" 
                            hx-get="{% host_url 'purchase_item_manage' item_.purchase_object.id host 'app' %}?from={{page_group_type}}&from_view_id={{view_id}}"  
                            hx-target="#expenses_form"
                            hx-on="htmx:beforeSend: 
                                document.getElementById('expenses_form_lg').innerHTML = '';
                                "
                        
                            class="text-center mb-0 text-dark text-hover-primary cursor-pointer">
                            
                            {{object.currency|convert_currency_to_symbol}}
                            {% if item_.total_price and item_.total_price != 'None'  %}
                                {{item_.total_price|use_thousand_separator_string_with_currency:object.currency}}
                            {% else %}
                                0
                            {% endif %}
                        </a>
                    {% else %}
                        {{object.currency|convert_currency_to_symbol}}
                        {% if item_.total_price and item_.total_price != 'None'  %}
                            {{item_.total_price|use_thousand_separator_string_with_currency:object.currency}}
                        {% else %}
                            0
                        {% endif %}
                    {% endif %}
                {% else %}
                    {% for item_ in object.purchase_order_object.all %}
                        {{object.currency|convert_currency_to_symbol}}
                        {% if item_.total_price and item_.total_price != 'None'  %}
                            {{item_.total_price|use_thousand_separator_string_with_currency:object.currency}}
                        {% else %}
                            0
                        {% endif %}
                    </br>
                    {% endfor %}
                {% endif %}
        </div>
    </td>

    {% elif "line_item_price_without_tax" == column %}
    <td class="fw-bold">
        <div class="text-start">
                {% if item_ %}
                    {% if item_.purchase_item %}
                        <a hx-indicator=".loading-drawer-spinner,.expenses-form" 
                            id="payment_wizard_button" 
                            hx-get="{% host_url 'purchase_item_manage' item_.purchase_object.id host 'app' %}?from={{page_group_type}}&from_view_id={{view_id}}"  
                            hx-target="#expenses_form"
                            hx-on="htmx:beforeSend: 
                                document.getElementById('expenses_form_lg').innerHTML = '';
                                "
                        
                            class="text-center mb-0 text-dark text-hover-primary cursor-pointer">
                            
                            {{object.currency|convert_currency_to_symbol}}
                            {% if item_.total_price_without_tax and item_.total_price_without_tax != 'None'  %}
                                {{item_.total_price_without_tax|use_thousand_separator_string_with_currency:object.currency}}
                            {% else %}
                                0
                            {% endif %}
                        </a>
                    {% else %}
                        {{object.currency|convert_currency_to_symbol}}
                        {% if item_.total_price_without_tax and item_.total_price_without_tax != 'None'  %}
                            {{item_.total_price_without_tax|use_thousand_separator_string_with_currency:object.currency}}
                        {% else %}
                            0
                        {% endif %}
                    {% endif %}
                {% else %}
                    {% for item_ in object.purchase_order_object.all %}
                        {{object.currency|convert_currency_to_symbol}}
                        {% if item_.total_price_without_tax and item_.total_price_without_tax != 'None'  %}
                            {{item_.total_price_without_tax|use_thousand_separator_string_with_currency:object.currency}}
                        {% else %}
                            0
                        {% endif %}
                    </br>
                    {% endfor %}
                {% endif %}
        </div>
    </td>

    {% elif "line_item_status" == column %}
    
    <td class="fw-bold">
        <div class="text-start">
                {% if item_ %}
                    {% if item_.item_status %}
                        {% if choice_status %}
                            {% with options=choice_status|string_list_to_list%}
                                {% for option in options %}
                                    {% if item_.item_status == option.value %}
                                        {{option.label}}
                                    {% endif %}
                                {% endfor %}
                            {% endwith %}
                        {% endif %}
                    {% else %}
                        -
                    {% endif %}
                    </br>
                {% else %}
                    {% for item_ in object.purchase_order_object.all %}
                        {% if choice_status %}
                            {% if item_.item_status %}
                                {% with options=choice_status|string_list_to_list%}
                                    {% for option in options %}
                                        {% if item_.item_status == option.value %}
                                            {{option.label}}
                                        {% endif %}
                                    {% endfor %}
                                {% endwith %}
                            {% else %}
                                -
                            {% endif %}
                        {% else %}
                            -
                        {% endif %}
                        </br>
                    {% endfor %}
                {% endif %}
        </div>
    </td>
    {% elif column == "line_item_tax" %}
        <td class="fw-bold">
            <div class="text-start">
                    {% if item_ %}
                        {% if item_.tax_rate %}
                            {{item_.tax_rate}}%
                        {% else %}
                            0
                        {% endif %}  
                    {% else %}
                        {% for item_ in object.purchase_order_object.all %}
                            {% if item_.tax_rate %}
                                {{item_.tax_rate}}%
                            {% else %}
                                0
                            {% endif %}  
                        </br>
                        {% endfor %}
                    {% endif %}
            </div>
        </td>

    {% elif column|is_uuid %}
    <td class="fw-bold">
        <div class="text-start">
                {% for custom_field_value in object.purchase_order_field_relations.all %}
                    {% with CustomFieldName=custom_field_value.field_name %}
                        {% if CustomFieldName.id|stringify == column %}
                            {% if CustomFieldName.type == 'choice' %}
                                {% if custom_field_value.value %}
                                    {% for choice in CustomFieldName.choice_value|string_list_to_list %}
                                        {% if ';' in custom_field_value.value and CustomFieldName.multiple_select %}
                                            {% for val in custom_field_value.value|split:";" %}
                                                {% if choice.value == val %}
                                                    {% if CustomFieldName.show_badge %}
                                                    <div class="fw-bold">
                                                        <span class="d-inline-flex align-items-center">
                                                            <span class="me-2" style="background-color: {{ choice.color|default:'#000000' }}; width: 16px; height: 16px; border-radius: 50%; display: inline-block; border: 1px solid #ccc;"></span>
                                                            {{ choice.label }}
                                                        </span>
                                                    </div>
                                                    {% else %}
                                                    <div class="fw-bold">
                                                        {{ choice.label }}
                                                    </div>
                                                    {% endif %}
                                                {% endif %}
                                            {% endfor %}
                                        {% else %}
                                            {% if choice.value == custom_field_value.value %}
                                                <div class="fw-bold">
                                                    {% if CustomFieldName.show_badge %}
                                                    <span class="d-inline-flex align-items-center">
                                                        <span class="me-2" style="background-color: {{ choice.color|default:'#000000' }}; width: 16px; height: 16px; border-radius: 50%; display: inline-block; border: 1px solid #ccc;"></span>
                                                        {{ choice.label }}
                                                    </span>
                                                    {% else %}
                                                    {{ choice.label }}
                                                    {% endif %}
                                                </div>
                                            {% endif %}
                                        {% endif %}
                                    {% endfor %}
                                {% endif %}
                            {% else %}
                                {% with item_id=object.id|stringify %}
                                    {% with args=column|add:'|'|add:object_type|add:'|'|add:item_id %} 
                                        {% with column_display=args|get_column_display:request %}
                                            {% if column_display.type == 'formula' %}
                                                {% include "data/common/custom_field/formula/row-partial.html" with obj_id=object.id CustomFieldName_id=column_display.id object_type='purchaseorder' %}                            
                                            {% else %}
                                                {% include "data/common/custom_field/row-partial-list.html" with CustomFieldName=column_display obj_id=object.id value=column_display.value object_type=object_type %}
                                            {% endif %}
                                        {% endwith %}
                                    {% endwith %}   
                                {% endwith%}
                            {% endif %}
                        {% endif %}
                    {% endwith %}
                {% endfor %}
        </div>
    </td>

    {% elif column == 'inventory_transactions' %}
        <td class="fw-bold">
            {% for transaction in object.inventory_transactions.all %}
                <div>
                    <a id="profile_wizard_button" class="mb-0 text-center cursor-pointer text-dark text-hover-primary fw-bolder manage-full-wizard-button transaction_{{transaction.id}}" 
                        hx-get="{% url 'shopturbo_load_drawer' %}" 
                        hx-vals = '{"drawer_type":"inventory-transaction-manage", "transaction_id":"{{transaction.id}}", "module":"{{menu_key}}" }'
                        hx-target="#manage-full-drawer-content"
                        hx-indicator=".loading-drawer-spinner"  
                        hx-trigger="click"
                    >
                        #{{ transaction.transaction_id|stringformat:"04d" }}
                    </a>
                </div>
            {% endfor %}
        </td>

    {% else %}
    <td class="fw-bold">
        <div class="text-start">
                {% if object|get_attr:column %}
                    {{object|get_attr:column}}
                {% endif %}
        </div>
    </td>

    {% endif %}

{% endfor %}

