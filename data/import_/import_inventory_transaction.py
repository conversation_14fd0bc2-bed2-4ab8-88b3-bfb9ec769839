import uuid
import pytz

from dateutil import parser
from django.utils import timezone
from data.constants.properties_constant import (
    TYPE_OBJECT_INVENTORY_TRANSACTION,
    TYPE_OBJECT_INVENTORY,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_PURCHASE_ORDER,
)
from utils.list_action_trigger_event import trigger_workflow_by_inventory_less_than
from utils.inventory import (
    recalculate_transactions_after,
    update_inventory_stock_price,
    re_calculate_inventory_stock,
)
#association label
from data.association import AssociationLabel,AssociationLabelObject

from data.models import (
    User,
    TransferHistory,
    ShopTurboInventory,
    ShopTurboItems,
    InventoryWarehouse,
    InventoryTransaction,
    InventoryTransactionNameCustomField,
    InventoryTransactionValueCustomField,
    UserManagement,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsValueCustomField,
    ShopTurboInventoryNameCustomField,
    ShopTurboInventoryValueCustomField,
)
from utils.bgjobs.runner import trigger_bg_job
from utils.utility import is_valid_uuid
from utils.workflow import get_workflows_by_first_action_trigger
from utils.logger import logger
from data.models import ShopTurboInventory,ShopTurboOrders,PurchaseOrders


def _find_item_by_custom_property(workspace, property_name, property_value, lang="en"):
    """
    Find item by custom property value.

    Args:
        workspace: The workspace to search in
        property_name: Name of the custom property (e.g., 'SKU')
        property_value: Value to search for
        lang: Language for error messages

    Returns:
        ShopTurboItems instance or None

    Raises:
        Exception: If multiple items found or other errors
    """
    try:
        # Find the custom field definition by name
        if is_valid_uuid(property_name):
            custom_field = ShopTurboItemsNameCustomField.objects.filter(
                workspace=workspace, id=property_name
            ).first()
        else:
            custom_field = ShopTurboItemsNameCustomField.objects.filter(
                workspace=workspace, name=property_name
            ).first()

        if not custom_field:
            if lang == "ja":
                raise Exception(
                    f"カスタムプロパティ '{property_name}' が見つかりません。"
                )
            else:
                raise Exception(f"Custom property '{property_name}' not found.")

        # Find items with this custom property value (optimized query)
        custom_values = ShopTurboItemsValueCustomField.objects.filter(
            field_name=custom_field,
            value__iexact=str(property_value).strip(),
            items__workspace=workspace,
            items__status="active",
        ).select_related("items", "field_name")

        if not custom_values.exists():
            if lang == "ja":
                raise Exception(
                    f"カスタムプロパティ '{property_name}' の値 '{property_value}' を持つ商品が見つかりません。"
                )
            else:
                raise Exception(
                    f"No item found with {property_name} = '{property_value}'."
                )

        if custom_values.count() > 1:
            if lang == "ja":
                raise Exception(
                    f"カスタムプロパティ '{property_name}' の値 '{property_value}' を持つ商品が複数見つかりました。一意の値を使用してください。"
                )
            else:
                raise Exception(
                    f"Multiple items found with {property_name} = '{property_value}'. Please use a unique value."
                )

        return custom_values.first().items

    except Exception as e:
        raise e


def _find_inventory_by_custom_property(
    workspace, property_name, property_value, lang="en"
):
    """
    Find inventory by custom property value.

    Args:
        workspace: The workspace to search in
        property_name: Name of the custom property (e.g., 'SKU')
        property_value: Value to search for
        lang: Language for error messages

    Returns:
        ShopTurboInventory instance or None

    Raises:
        Exception: If multiple items found or other errors
    """
    try:
        # Find the custom field definition by name
        if is_valid_uuid(property_name):
            custom_field = ShopTurboInventoryNameCustomField.objects.filter(
                workspace=workspace, id=property_name
            ).first()
        else:
            custom_field = ShopTurboInventoryNameCustomField.objects.filter(
                workspace=workspace, name=property_name
            ).first()

        if not custom_field:
            if lang == "ja":
                raise Exception(
                    f"カスタムプロパティ '{property_name}' が見つかりません。"
                )
            else:
                raise Exception(f"Custom property '{property_name}' not found.")

        # Find items with this custom property value (optimized query)
        custom_values = ShopTurboInventoryValueCustomField.objects.filter(
            field_name=custom_field,
            value__iexact=str(property_value).strip(),
            inventory__workspace=workspace,
            inventory__status="active",
        ).select_related("inventory", "field_name")

        if not custom_values.exists():
            if lang == "ja":
                raise Exception(
                    f"{property_name} = '{property_value}' の在庫が見つかりません。"
                )
            else:
                raise Exception(
                    f"No inventory found with {property_name} = '{property_value}'."
                )

        if custom_values.count() > 1:
            if lang == "ja":
                raise Exception(
                    f"{property_name} = '{property_value}' の在庫が複数見つかりました。一意の値を使用してください。"
                )
            else:
                raise Exception(
                    f"Multiple inventory found with {property_name} = '{property_value}'. Please use a unique value."
                )

        return custom_values.first().inventory

    except Exception as e:
        raise e


def _get_oldest_inventory_for_item(workspace, item, lang="en"):
    """
    Get the oldest inventory record for an item using FIFO logic.

    Args:
        workspace: The workspace to search in
        item: The ShopTurboItems instance
        lang: Language for error messages

    Returns:
        ShopTurboInventory instance

    Raises:
        Exception: If no inventory found
    """
    try:
        # Find all active inventory records for this item, ordered by creation date (FIFO)
        # Optimized query with select_related for better performance
        inventory = (
            ShopTurboInventory.objects.filter(
                item=item, workspace=workspace, status="active"
            )
            .select_related("workspace")
            .prefetch_related("item")
            .order_by("created_at")
            .first()
        )

        if not inventory:
            if lang == "ja":
                raise Exception(
                    f"商品 '{item.name}' (ID: {item.item_id}) の在庫が見つかりません。この商品の在庫を先に作成してください。"
                )
            else:
                raise Exception(
                    f"No inventory found for item '{item.name}' (ID: {item.item_id}). Please create inventory for this item first."
                )

        return inventory

    except Exception as e:
        raise e


def _enhanced_item_lookup(workspace, item_identifier, lang="en"):
    """
    Enhanced item lookup supporting multiple methods:
    1. Numeric item_id
    2. Item name
    3. Custom properties (detected by format like "property_name:value")

    Args:
        workspace: The workspace to search in
        item_identifier: The identifier string from CSV
        lang: Language for error messages

    Returns:
        ShopTurboItems instance

    Raises:
        Exception: If item not found or multiple matches
    """
    item_identifier = str(item_identifier).strip()

    # Check if it's a custom property lookup (format: "property_name:value")
    if ":" in item_identifier:
        property_name, property_value = item_identifier.split(":", 1)
        property_name = property_name.strip()
        property_value = property_value.strip()

        if property_name and property_value:
            return _find_item_by_custom_property(
                workspace, property_name, property_value, lang
            )

    # Try numeric item_id first
    if item_identifier.isdigit():
        try:
            return ShopTurboItems.objects.get(
                item_id=int(item_identifier), workspace=workspace, status="active"
            )
        except ShopTurboItems.DoesNotExist:
            pass

    # Try item name
    try:
        return ShopTurboItems.objects.get(
            name=item_identifier, workspace=workspace, status="active"
        )
    except ShopTurboItems.DoesNotExist:
        pass
    except ShopTurboItems.MultipleObjectsReturned:
        if lang == "ja":
            raise Exception(
                f"商品名 '{item_identifier}' を持つ商品が複数見つかりました。一意の識別子を使用してください。"
            )
        else:
            raise Exception(
                f"Multiple items found with name '{item_identifier}'. Please use a unique identifier."
            )

    # If we get here, no item was found
    if lang == "ja":
        raise Exception(f"商品コード '{item_identifier}' の商品が見つかりません。")
    else:
        raise Exception(f"Item with identifier '{item_identifier}' does not exist.")


def _enhanced_inventory_lookup(workspace, inventory_identifier, lang="en"):
    """
    Enhanced inventory lookup supporting multiple methods:
    1. Numeric inventory_id
    2. Custom properties (detected by format like "property_name:value")

    Args:
        workspace: The workspace to search in
        inventory_identifier: The identifier string from CSV
        lang: Language for error messages

    Returns:
        ShopTurboInventory instance

    Raises:
        Exception: If inventory not found or multiple matches
    """
    SPECIAL_KEYWORD = ["inventory_id"]
    inventory_identifier = str(inventory_identifier).strip()
    # Check if it's a custom property lookup (format: "property_name:value")
    if ":" in inventory_identifier:
        property_name, property_value = inventory_identifier.split(":", 1)
        property_name = property_name.strip()
        property_value = property_value.strip()
        if property_name and property_value and (property_name not in SPECIAL_KEYWORD):
            return _find_inventory_by_custom_property(
                workspace, property_name, property_value, lang
            )
        
        if property_name in SPECIAL_KEYWORD:inventory_identifier = property_value

    # Try numeric inventory_id first
    if inventory_identifier.isdigit():
        try:
            return ShopTurboInventory.objects.get(
                inventory_id=int(inventory_identifier),
                workspace=workspace,
                status="active",
            )
        except ShopTurboInventory.DoesNotExist:
            pass

    # If we get here, no inventory was found
    if lang == "ja":
        raise Exception(f"識別子 {inventory_identifier} を持つ在庫は存在しません。")
    else:
        raise Exception(
            f"Inventory with identifier '{inventory_identifier}' does not exist."
        )


def write_inventory_transaction(
    workspace, user: User, lang, data_dictionary={}, task_history_id=None
):
    inventory_transaction = None
    try:
        transfer_history = None
        if task_history_id:
            try:
                transfer_history = TransferHistory.objects.get(id=task_history_id)
            except:
                pass

        inventory = None
        is_adjust_inventory = False

        # Check if we have either inventory_id or item_id with valid values
        has_inventory_id = (
            "inventory_id" in data_dictionary["default_property"]
            and data_dictionary["default_property"]["inventory_id"] is not None
            and str(data_dictionary["default_property"]["inventory_id"]).strip()
            not in ["", "nan", "none", "null"]
        )

        has_item_id = (
            "item_id" in data_dictionary["default_property"]
            and data_dictionary["default_property"]["item_id"] is not None
            and str(data_dictionary["default_property"]["item_id"]).strip()
            not in ["", "nan", "none", "null"]
        )

        if not has_inventory_id and not has_item_id:
            if lang == "ja":
                raise Exception(
                    "在庫IDまたは商品コードが必要です。空の値は使用できません。"
                )
            else:
                raise Exception(
                    "Either inventory_id or item_id is required. Empty values are not allowed."
                )

        # Handle inventory lookup by inventory_id
        if has_inventory_id:
            inventory_identifier = data_dictionary["default_property"]["inventory_id"]
            if (
                "property_object_relation_key_field" in data_dictionary
                and "inventory_id"
                in data_dictionary["property_object_relation_key_field"]
            ):
                custom_prop_id = data_dictionary["property_object_relation_key_field"][
                    "inventory_id"
                ]
                inventory_identifier = f"{custom_prop_id}:{inventory_identifier}"
            try:
                # Use enhanced inventory lookup that supports custom properties and multiple methods
                inventory = _enhanced_inventory_lookup(
                    workspace, inventory_identifier, lang
                )

                data_dictionary["default_property"]["inventory"] = inventory
                data_dictionary["default_property"].pop("inventory_id")
            except Exception as e:
                # Re-raise the exception with the original error message
                logger.error(str(e))
                raise Exception(str(e))

        # Handle inventory lookup by item_id (item code) - Enhanced with custom property support
        elif has_item_id:
            item_identifier = data_dictionary["default_property"]["item_id"]
            if (
                "property_object_relation_key_field" in data_dictionary
                and "item_id" in data_dictionary["property_object_relation_key_field"]
            ):
                custom_prop_id = data_dictionary["property_object_relation_key_field"][
                    "item_id"
                ]
                item_identifier = f"{custom_prop_id}:{item_identifier}"
            try:
                # Use enhanced item lookup that supports custom properties and multiple methods
                item = _enhanced_item_lookup(workspace, item_identifier, lang)

                # Use FIFO logic to get the oldest inventory record for this item
                inventory = _get_oldest_inventory_for_item(workspace, item, lang)

                data_dictionary["default_property"]["inventory"] = inventory
                data_dictionary["default_property"].pop("item_id")

            except Exception as e:
                # Re-raise the exception with the original error message
                logger.error(str(e))
                raise Exception(str(e))

        # Handle created_at date parsing
        if "created_at" in data_dictionary["default_property"]:
            try:
                date_str = str(
                    data_dictionary["default_property"]["created_at"]
                ).strip()
                if date_str and date_str.lower() not in ["", "nan", "none"]:
                    # Handle various date formats including YYYY/MM/DD
                    if "/" in date_str and len(date_str.split("/")) == 3:
                        # Add default time if only date is provided
                        if " " not in date_str:
                            date_str += " 00:00:00"

                    date_obj = parser.parse(date_str)
                    if date_obj and workspace.timezone:
                        date_obj = timezone.make_aware(
                            date_obj, pytz.timezone(workspace.timezone)
                        )
                    data_dictionary["default_property"]["created_at"] = date_obj
            except (ValueError, TypeError):
                if lang == "ja":
                    raise Exception(
                        f"作成日の形式が正しくありません: {data_dictionary['default_property']['created_at']}"
                    )
                else:
                    raise Exception(
                        f"Invalid created_at date format: {data_dictionary['default_property']['created_at']}"
                    )

        # Handle transaction_date parsing
        if "transaction_date" in data_dictionary["default_property"]:
            try:
                date_str = str(
                    data_dictionary["default_property"]["transaction_date"]
                ).strip()
                if date_str and date_str.lower() not in ["", "nan", "none"]:
                    # Handle various date formats including YYYY/MM/DD
                    if "/" in date_str and len(date_str.split("/")) == 3:
                        # Add default time if only date is provided
                        if " " not in date_str:
                            date_str += " 00:00:00"

                    date_obj = parser.parse(date_str)
                    if date_obj and workspace.timezone:
                        date_obj = timezone.make_aware(
                            date_obj, pytz.timezone(workspace.timezone)
                        )
                    data_dictionary["default_property"]["transaction_date"] = date_obj

                    # Check if transaction date is before inventory registration date
                    if (
                        inventory
                        and hasattr(inventory, "date")
                        and inventory.date
                        and date_obj < inventory.date
                    ):
                        if lang == "ja":
                            raise Exception(
                                f"入出庫日 {date_obj.strftime('%Y-%m-%d')} が在庫登録日 {inventory.date.strftime('%Y-%m-%d')} より前です。在庫登録日以降の日付を使用してください。"
                            )
                        else:
                            raise Exception(
                                f"Transaction date {date_obj.strftime('%Y-%m-%d')} is earlier than inventory registration date {inventory.date.strftime('%Y-%m-%d')}. Please use a date on or after the inventory registration date."
                            )
                else:
                    # If no transaction_date provided, use current time
                    data_dictionary["default_property"]["transaction_date"] = (
                        timezone.now()
                    )
            except (ValueError, TypeError):
                if lang == "ja":
                    raise Exception(
                        f"入出庫日時の形式が正しくありません: {data_dictionary['default_property']['transaction_date']}"
                    )
                else:
                    raise Exception(
                        f"Invalid transaction_date format: {data_dictionary['default_property']['transaction_date']}"
                    )

        # Handle amount validation and conversion
        if "amount" in data_dictionary["default_property"]:
            try:
                amount_str = str(data_dictionary["default_property"]["amount"]).strip()
                if amount_str and amount_str.lower() not in ["", "nan", "none"]:
                    # Remove commas and convert to integer
                    amount_str = amount_str.replace(",", "")
                    amount_value = int(
                        float(amount_str)
                    )  # Handle both int and float strings
                    data_dictionary["default_property"]["amount"] = amount_value
                else:
                    if lang == "ja":
                        raise Exception("入出庫数が必要です。")
                    else:
                        raise Exception("Amount is required.")
            except (ValueError, TypeError):
                if lang == "ja":
                    raise Exception(
                        f"入出庫数の形式が正しくありません: {data_dictionary['default_property']['amount']}"
                    )
                else:
                    raise Exception(
                        f"Invalid amount format: {data_dictionary['default_property']['amount']}"
                    )
        else:
            if lang == "ja":
                raise Exception("入出庫数が必要です。")
            else:
                raise Exception("Amount is required.")

        # Handle transaction_type validation
        if "transaction_type" in data_dictionary["default_property"]:
            transaction_type = (
                str(data_dictionary["default_property"]["transaction_type"])
                .strip()
                .lower()
            )
            if transaction_type in ["in", "out", "adjust"]:
                data_dictionary["default_property"]["transaction_type"] = (
                    transaction_type
                )
                if transaction_type == "adjust":
                    data_dictionary["default_property"].pop("transaction_type")
                    is_adjust_inventory = True
            else:
                if lang == "ja":
                    raise Exception(
                        f"無効な入出庫タイプです: {data_dictionary['default_property']['transaction_type']} (有効な値: in, out, adjust)"
                    )
                else:
                    raise Exception(
                        f"Invalid transaction type: {data_dictionary['default_property']['transaction_type']} (valid values: in, out, adjust)"
                    )
        else:
            if lang == "ja":
                raise Exception("入出庫タイプが必要です。")
            else:
                raise Exception("Transaction type is required.")

        # Handle location ID validation if provided
        if "id_iw" in data_dictionary["default_property"]:
            try:
                location_id = data_dictionary["default_property"]["id_iw"]
                if location_id and str(location_id).strip():
                    # Validate that the location exists
                    InventoryWarehouse.objects.get(
                        id_iw=location_id, workspace=workspace
                    )
                    # Note: The location is validated but not directly used in InventoryTransaction model
                # Remove id_iw from default_property as it's not a field on InventoryTransaction
                data_dictionary["default_property"].pop("id_iw")
            except InventoryWarehouse.DoesNotExist:
                if lang == "ja":
                    raise Exception(f"ロケーションID {location_id} が見つかりません。")
                else:
                    raise Exception(f"Location ID {location_id} does not exist.")
            except (ValueError, TypeError):
                if lang == "ja":
                    raise Exception(f"無効なロケーションID形式: {location_id}")
                else:
                    raise Exception(f"Invalid location ID format: {location_id}")

        # add user
        data_dictionary["default_property"]["user"] = user

        # # Process Base Objects
        if data_dictionary["how_to_import"] == "update":
            inventory_transaction = None
            if is_valid_uuid(data_dictionary["key_field"]):
                order_property = InventoryTransactionNameCustomField.objects.get(
                    id=data_dictionary["key_field"], workspace=workspace
                )
                CustomFieldValue = (
                    InventoryTransactionValueCustomField.objects.filter(
                        field_name=order_property,
                        value=data_dictionary["key_property"][
                            data_dictionary["key_field"]
                        ],
                    )
                    .order_by("-created_at")
                    .first()
                )
                if CustomFieldValue:
                    inventory_transaction = CustomFieldValue.transaction
            else:
                inventory_transaction = InventoryTransaction.objects.filter(
                    **data_dictionary["key_property"], workspace=workspace
                ).first()
        elif data_dictionary["how_to_import"] == "create":
            if "transaction_id" in data_dictionary["default_property"]:
                data_dictionary["default_property"].pop("transaction_id")
            if transfer_history and transfer_history.checkpoint_details["object_id"]:
                # This process is already processed before.
                try:
                    inventory_transaction = InventoryTransaction.objects.get(
                        id=transfer_history.checkpoint_details["object_id"],
                        workspace=workspace,
                    )
                except InventoryTransaction.DoesNotExist:
                    inventory_transaction = InventoryTransaction.objects.create(
                        **data_dictionary["default_property"], workspace=workspace
                    )
                    workflows = get_workflows_by_first_action_trigger(
                        "record-created",
                        workspace,
                        TYPE_OBJECT_INVENTORY_TRANSACTION,
                        additional_conditions={
                            "input_data__transaction_type": inventory_transaction.transaction_type
                        },
                    )
                    for workflow in workflows:
                        param = {
                            "function": "trigger_workflow_when_inventory_transaction_created",
                            "job_id": str(uuid.uuid4()),
                            "workspace_id": str(workspace.id),
                            "user_id": None,
                            "args": [
                                f"--inventory_transaction_id={inventory_transaction.id}",
                                f"--lang={lang}",
                                f"--workflow_id={workflow.id}",
                            ],
                        }
                        trigger_bg_job(param)
            else:
                inventory_transaction = InventoryTransaction.objects.create(
                    **data_dictionary["default_property"], workspace=workspace
                )
                workflows = get_workflows_by_first_action_trigger(
                    "record-created",
                    workspace,
                    TYPE_OBJECT_INVENTORY_TRANSACTION,
                    additional_conditions={
                        "input_data__transaction_type": inventory_transaction.transaction_type
                    },
                )
                for workflow in workflows:
                    param = {
                        "function": "trigger_workflow_when_inventory_transaction_created",
                        "job_id": str(uuid.uuid4()),
                        "workspace_id": str(workspace.id),
                        "user_id": None,
                        "args": [
                            f"--inventory_transaction_id={inventory_transaction.id}",
                            f"--lang={lang}",
                            f"--workflow_id={workflow.id}",
                        ],
                    }
                    trigger_bg_job(param)
        elif data_dictionary["how_to_import"] == "create_and_update":
            try:
                # custom property
                inventory_transaction = None
                if is_valid_uuid(data_dictionary["key_field"]):
                    inventory_transaction_property = (
                        InventoryTransactionNameCustomField.objects.get(
                            id=data_dictionary["key_field"], workspace=workspace
                        )
                    )
                    CustomFieldValue = (
                        InventoryTransactionValueCustomField.objects.filter(
                            field_name=inventory_transaction_property,
                            value=data_dictionary["key_property"][
                                data_dictionary["key_field"]
                            ],
                        )
                        .order_by("-created_at")
                        .first()
                    )
                    if CustomFieldValue:
                        inventory_transaction = CustomFieldValue.transaction

                if not inventory_transaction:
                    if not data_dictionary["key_property"]:
                        raise Exception
                    inventory_transaction, _ = (
                        InventoryTransaction.objects.get_or_create(
                            **data_dictionary["key_property"], workspace=workspace
                        )
                    )

            except InventoryTransaction.MultipleObjectsReturned:
                if not data_dictionary["key_property"]:
                    raise Exception
                inventory_transaction = InventoryTransaction.objects.filter(
                    **data_dictionary["key_property"], workspace=workspace
                ).first()
            except:
                if "transaction_id" in data_dictionary["default_property"]:
                    data_dictionary["default_property"].pop("transaction_id")
                inventory_transaction = InventoryTransaction.objects.create(
                    **data_dictionary["default_property"], workspace=workspace
                )
                workflows = get_workflows_by_first_action_trigger(
                    "record-created",
                    workspace,
                    TYPE_OBJECT_INVENTORY_TRANSACTION,
                    additional_conditions={
                        "input_data__transaction_type": inventory_transaction.transaction_type
                    },
                )
                for workflow in workflows:
                    param = {
                        "function": "trigger_workflow_when_inventory_transaction_created",
                        "job_id": str(uuid.uuid4()),
                        "workspace_id": str(workspace.id),
                        "user_id": None,
                        "args": [
                            f"--inventory_transaction_id={inventory_transaction.id}",
                            f"--lang={lang}",
                            f"--workflow_id={workflow.id}",
                        ],
                    }
                    trigger_bg_job(param)

        if not inventory_transaction:
            return

        if transfer_history:
            transfer_history.checkpoint_details["object_id"] = str(
                inventory_transaction.id
            )
            transfer_history.save()

        # Process Adding base Value
        for default_property_key in data_dictionary["default_property"]:
            setattr(
                inventory_transaction,
                default_property_key,
                data_dictionary["default_property"][default_property_key],
            )
        setattr(inventory_transaction, "usage_status", "active")

        inventory: ShopTurboInventory = data_dictionary["default_property"]["inventory"]
        amount: str = data_dictionary["default_property"]["amount"]

        if is_adjust_inventory:
            setattr(inventory_transaction, "transaction_type", "adjust")

        inventory_transaction.save(
            log_data={"user": user, "workspace": workspace}
        )  # Log

        if is_adjust_inventory:
            transaction_amount = int(amount) - inventory.total_inventory
            inventory.total_inventory = int(amount)
            inventory.save()
            trigger_workflow_by_inventory_less_than(inventory)

            inventory_transaction.transaction_amount = amount
            inventory_transaction.amount = transaction_amount

            if not inventory_transaction.transaction_date:
                inventory_transaction.transaction_date = timezone.now()
            inventory_transaction.save()

            # inventory_stock = recalculate_transactions_after(inventory, inventory_transaction)

        else:
            recalculate_transactions_after(inventory, inventory_transaction)
            inventory.refresh_from_db()
            update_inventory_stock_price(inventory)
            inventory.refresh_from_db()
            # Recalculate Inventory Amount
            re_calculate_inventory_stock(inventory)

        # Process Custom Property
        for custom_property in data_dictionary["custom_property"]:

            print("===== custom_property: ", custom_property)

            #Checking for association label
            item_property = InventoryTransactionNameCustomField.objects.filter(
                id=custom_property, workspace=workspace
            ).first()
            if item_property:
                CustomFieldValue, _ = (
                    InventoryTransactionValueCustomField.objects.get_or_create(
                        field_name=item_property, transaction=inventory_transaction
                    )
                )

                if item_property.type == "number":
                    data = data_dictionary["custom_property"][custom_property]
                    if "," in data:
                        data = data.replace(",", "")
                    CustomFieldValue.value = data
                elif item_property.type == "user_management":
                    try:
                        data = data_dictionary["custom_property"][custom_property]
                        if data:
                            user = UserManagement.objects.get(
                                workspace=workspace, id_user=int(data)
                            )
                            CustomFieldValue.value = str(user.id)
                    except:
                        pass
                else:
                    CustomFieldValue.value = data_dictionary["custom_property"][
                        custom_property
                    ]

                CustomFieldValue.save()
            else:

                
                #Association label
                association_label = AssociationLabel.objects.filter(
                    id=custom_property, workspace=workspace
                ).first()
                if association_label:
                    try:
                        # Get the target object value from the CSV data
                        target_values = data_dictionary["custom_property"][custom_property]
                        print("=== target_values: ",target_values)
                        if not target_values or str(target_values).strip() in ["", "nan", "none", "null"]:
                            continue  # Skip empty values
                        
                        # Find the target object based on the association label's target type
                        # Use DEAFULT_ASSOCIATION_OBJECT_MEMBER for TYPE_OBJECT_INVENTORY_TRANSACTION
                        target_object = None
                        target_object_type = association_label.object_target
                        
                        target_values = target_values.split(";")
                        for target_value in target_values:
                            if target_object_type == TYPE_OBJECT_INVENTORY:
                                # Try to find inventory by ID first, then by enhanced lookup
                                if str(target_value).isdigit():
                                    target_object = ShopTurboInventory.objects.filter(
                                        inventory_id=int(target_value), workspace=workspace, status="active"
                                    ).first()
                                        
                            elif target_object_type == TYPE_OBJECT_ORDER:  # "commerce_orders"
                                
                                # Try to find order by ID first, then by order number
                                if str(target_value).isdigit():
                                    target_object = ShopTurboOrders.objects.filter(
                                        order_id=int(target_value), workspace=workspace, status="active"
                                    ).first()
                                if not target_object:
                                    target_object = ShopTurboOrders.objects.filter(
                                        order_id=str(target_value).strip(), workspace=workspace, status="active"
                                    ).first()
                                    
                            elif target_object_type == TYPE_OBJECT_PURCHASE_ORDER:  # "purchaseorder"
                                
                                # Try to find purchase order by ID first, then by purchase order number
                                if str(target_value).isdigit():
                                    target_object = PurchaseOrders.objects.filter(
                                        id_po=int(target_value), workspace=workspace, usage_status="active"
                                    ).first()
                                if not target_object:
                                    target_object = PurchaseOrders.objects.filter(
                                        id_po=str(target_value).strip(), workspace=workspace, usage_status="active"
                                    ).first()
                            
                            # Create the association if target object is found
                            if target_object:
                                AssociationLabelObject.create_association(
                                    inventory_transaction,  # source object
                                    target_object,         # target object
                                    workspace,
                                    association_label,
                                )
                                logger.info(f"Created association between inventory transaction {inventory_transaction.id} and {target_object_type} {target_object.id}")
                            else:
                                logger.warning(f"Could not find {target_object_type} with identifier: {target_value}")
                            
                    except Exception as e:
                        logger.error(f"Error creating association for label {association_label.label}: {str(e)}")
                        # Don't raise the exception, just log it and continue processing

    except Exception as e:
        # Clean up if transaction was created but failed
        if data_dictionary.get("how_to_import") == "create":
            if inventory_transaction:
                try:
                    inventory_transaction.delete()
                except:
                    pass  # Ignore deletion errors

        # Provide more detailed error messages
        error_msg = str(e)
        if not error_msg:
            if lang == "ja":
                error_msg = (
                    "入出庫トランザクションの作成中に不明なエラーが発生しました。"
                )
            else:
                error_msg = (
                    "Unknown error occurred while creating inventory transaction."
                )

        raise ValueError(error_msg)

    return inventory_transaction
