# Import necessary models for V4 batch associations

import asyncio
from datetime import datetime
from typing import Dict, List, Optional

from data.models import (
    Channel,
    CustomObject,
    CustomObjectPlatforms,
    CustomObjectPropertyRow,
    Notification,
    TransferHistory,
    User,
)
from utils.error_logger.import_export_logger import (
    ImportExportLogger as HubSpotErrorLogger,
)
from utils.hubspot.utils_hubspot import (
    refresh_hubspot_token,
)
from utils.logger import logger

# from .owner_hubspot import get_all_owners  # Using async version instead
from .batch_operations import (
    create_batch_records,
    create_bulk_properties,
    get_records_object,
    update_batch_records,
    get_all_owners_async,
    prepare_custom_object_properties,
    create_associations_bulk,
    validate_and_prepare_schema_mapping,
)
from .models import AssociationMapping


def parse_association_mappings(
    mapping_custom_fields_association: Dict[str, str],
) -> List[AssociationMapping]:
    """
    Convert association mapping dictionary to AssociationMapping objects
    """
    association_mappings = []
    for (
        hubspot_association,
        sanka_association,
    ) in mapping_custom_fields_association.items():
        parts = hubspot_association.split("|")
        if len(parts) == 4:
            association_mapping = AssociationMapping(
                sanka_association_id=sanka_association,
                hubspot_association_name=parts[0],
                hubspot_association_id=parts[1],
                hubspot_association_from_object_type_id=parts[2],
                hubspot_association_to_object_type_id=parts[3],
            )
            association_mappings.append(association_mapping)
    return association_mappings


async def export_hubspot_custom_object(
    channel_id: str,
    custom_object_id: str,
    platform_object_type_id: str,
    mapping_custom_fields: Dict[str, str] = None,
    hubspot_group_name: str = "",
    lang: str = "ja",
    user_id: str = "",
    custom_object_ids: List[str] = None,
    mapping_custom_fields_association: Dict[str, str] = None,
    task: Optional[TransferHistory] = None,
):
    # Initialize default values
    if mapping_custom_fields is None:
        mapping_custom_fields = {}
    if custom_object_ids is None:
        custom_object_ids = []
    if mapping_custom_fields_association is None:
        mapping_custom_fields_association = {}

    # Initialize error logger
    error_logger = HubSpotErrorLogger(lang=lang)

    async with error_logger.afunction_context("export_hubspot_custom_object"):
        channel = await Channel.objects.aget(pk=channel_id)
        workspace = await asyncio.to_thread(lambda: channel.workspace)
        user = await User.objects.aget(id=user_id)

        custom_object = await CustomObject.objects.filter(id=custom_object_id).afirst()

        task = await (
            TransferHistory.objects.filter(
                workspace=workspace, type=f"export_{custom_object.slug}"
            )
            .order_by("-created_at")
            .afirst()
        )

        if lang == "ja":
            await Notification.objects.acreate(
                workspace=workspace,
                user=user,
                message=f"{custom_object.name}をエクスポートしております。少々お待ちください...",
                type="success",
            )
        else:
            await Notification.objects.acreate(
                workspace=workspace,
                user=user,
                message=f"{custom_object.name} being export. Please give it a few moment...",
                type="success",
            )

        try:
            await error_logger.alog_info(
                "export_hubspot_custom_object",
                f"Starting export for custom object: {custom_object.name}",
            )

            access_token = await asyncio.to_thread(refresh_hubspot_token, channel.id)
            await error_logger.alog_info(
                "export_hubspot_custom_object", "Successfully refreshed HubSpot token"
            )

            # Validate schema and required properties early
            (
                is_valid,
                updated_mapping,
                missing_required,
                required_properties,
                property_types,
            ) = await validate_and_prepare_schema_mapping(
                access_token,
                platform_object_type_id,
                mapping_custom_fields,
                error_logger,
            )

            if not is_valid:
                error_msg = (
                    f"Schema validation failed for custom object {platform_object_type_id}. "
                    f"The following required properties are missing mappings: {missing_required}. "
                    f"You need to map Sanka fields to these HubSpot properties to run the export successfully."
                )
                await error_logger.alog_validation_error(
                    "export_hubspot_custom_object", error_msg
                )

                # Create user notification
                if lang == "ja":
                    await Notification.objects.acreate(
                        workspace=workspace,
                        user=user,
                        message=f"HubSpot {custom_object.name} エクスポートに失敗しました: 必須プロパティ {missing_required} のマッピングが不足しています",
                        type="error",
                    )
                else:
                    await Notification.objects.acreate(
                        workspace=workspace,
                        user=user,
                        message=f"HubSpot {custom_object.name} export failed: Missing required property mappings {missing_required}",
                        type="error",
                    )
                raise Exception(error_msg)

            # Use the updated mapping with auto-mapped ID fields
            mapping_custom_fields = updated_mapping

            inputs = []
            hubspotFieldType = {
                "bool": "booleancheckbox",
                "enumeration": "select",
                "date": "date",
                "datetime": "date",
                "string": "text",
                "number": "number",
            }
            list_create_properties = [
                {"name": "sanka_id", "label": "Sanka Id", "type": "string"}
            ]
            for property in list_create_properties:
                options = []
                input = {
                    "name": property["name"],
                    "label": property["label"],
                    "type": property["type"],
                    "fieldType": "html"
                    if property["name"] == f"{platform_object_type_id}_link"
                    else hubspotFieldType[property["type"]],
                    "groupName": hubspot_group_name,
                    "description": f"property {property['name']} for {platform_object_type_id}",
                    "options": options,
                    "searchable": True,
                    "displayOrder": -1,
                    "hasUniqueValue": "true" if property["name"] == "id" else "false",
                    "hidden": "false",
                    "formField": "true",
                }

                inputs.append(input)

            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
            }
            owners = await get_all_owners_async(headers)

            ok, response = await create_bulk_properties(
                access_token, platform_object_type_id, inputs, error_logger
            )

            if not ok:
                await error_logger.alog_api_error(
                    "export_hubspot_custom_object",
                    f"Failed to create bulk properties: {response}",
                )
                raise Exception(response)

            # fetch record from Hubspot's custom object
            hs_object_property_ids = []
            ok, response = await get_records_object(
                access_token, platform_object_type_id, [], error_logger
            )
            if ok:
                hs_object_property_ids.extend([data["id"] for data in response])
                await error_logger.alog_info(
                    "export_hubspot_custom_object",
                    f"Found {len(hs_object_property_ids)} existing records in HubSpot",
                )
            else:
                await error_logger.alog_warning(
                    "export_hubspot_custom_object",
                    f"Failed to fetch existing records: {response}",
                )

            inputs = []
            inputs_batch_update = []
            processed_count = 0

            # fetch record from sanka
            if len(custom_object_ids) > 0:
                custom_object_rows = CustomObjectPropertyRow.objects.filter(
                    custom_object=custom_object,
                    workspace=workspace,
                    id__in=custom_object_ids,
                    usage_status="active",
                )
            else:
                custom_object_rows = CustomObjectPropertyRow.objects.filter(
                    custom_object=custom_object,
                    workspace=workspace,
                    usage_status="active",
                )

            total_rows = await custom_object_rows.acount()
            await error_logger.alog_info(
                "export_hubspot_custom_object",
                f"Processing {total_rows} custom object rows",
            )

            # Initialize task tracking
            if task:
                task.total_number = total_rows
                task.success_number = 0
                task.failed_number = 0
                task.progress = 0
                task.status = "running"
                await task.asave()

            # Process custom objects using new modular approach
            async for row in custom_object_rows:
                try:
                    properties = await prepare_custom_object_properties(
                        row,
                        mapping_custom_fields,
                        workspace,
                        owners,
                        custom_object,
                        error_logger,
                        required_properties,
                        property_types,
                    )

                    # Check if record should be skipped (e.g., required number ID field is null/empty)
                    # This is indicated by missing required properties in the result
                    skip_record = False
                    for required_prop in required_properties:
                        if required_prop in ["id"] and required_prop not in properties:
                            skip_record = True
                            if error_logger:
                                await error_logger.alog_info(
                                    "export_hubspot_custom_object",
                                    f"Skipping record {row.id} - required property '{required_prop}' could not be populated",
                                )
                            break

                    if skip_record:
                        processed_count += 1
                        if task and total_rows > 0:
                            preparation_progress = min(
                                50, int((processed_count / total_rows) * 50)
                            )
                            task.progress = preparation_progress
                            await task.asave()
                        continue

                    row_platform = await (
                        row.custom_object_platform_relations.filter(
                            platform_object_type=platform_object_type_id
                        )
                        .order_by("-created_at")
                        .afirst()
                    )

                    platform_id = ""
                    if row_platform and row_platform.platform_id:
                        platform_id = row_platform.platform_id

                    if platform_id in hs_object_property_ids:
                        inputs_batch_update.append(
                            {"id": str(platform_id), "properties": properties}
                        )
                    else:
                        inputs.append({"properties": properties})

                except Exception as e:
                    await error_logger.alog_business_logic_error(
                        "export_hubspot_custom_object",
                        f"Failed to process custom object row {row.id}: {str(e)}",
                        e,
                    )
                    # Update failed count for processing errors
                    if task:
                        task.failed_number += 1
                        await task.asave()

                # Update progress for each processed row
                processed_count += 1
                if task and total_rows > 0:
                    # Update progress based on processing completion (up to 50% for preparation)
                    preparation_progress = min(
                        50, int((processed_count / total_rows) * 50)
                    )
                    task.progress = preparation_progress
                    await task.asave()

            successful_updated_records = []
            if inputs_batch_update:
                try:
                    ok, response = await update_batch_records(
                        access_token,
                        platform_object_type_id,
                        inputs_batch_update,
                        is_update=True,
                        error_logger=error_logger,
                    )
                    if ok:
                        print("Log update:", response)
                        successful_updated_records = response
                        await error_logger.alog_info(
                            "export_hubspot_custom_object",
                            f"Successfully updated {len(inputs_batch_update)} records",
                        )
                        # Update success count and progress for updates
                        if task:
                            task.success_number += len(successful_updated_records)
                            # Progress: 50% + (updates portion of remaining 50%)
                            update_progress = int(
                                (len(successful_updated_records) / total_rows) * 25
                            )
                            task.progress = min(75, 50 + update_progress)
                            await task.asave()
                    else:
                        await error_logger.alog_api_error(
                            "export_hubspot_custom_object",
                            f"Failed to update batch records: {response}",
                        )
                        # Update failed count for update failures
                        if task:
                            task.failed_number += len(inputs_batch_update)
                            await task.asave()
                        if lang == "ja":  # in japanese
                            await Notification.objects.acreate(
                                workspace=workspace,
                                user=user,
                                message=f"Hubspotカスタムオブジェクト {custom_object.name} アップデートに失敗しました: {response}",
                                type="error",
                            )
                        else:
                            await Notification.objects.acreate(
                                workspace=workspace,
                                user=user,
                                message=f"Hubspot custom object {custom_object.name} update failed: {response}",
                                type="error",
                            )
                        raise Exception(response)
                except Exception as e:
                    await error_logger.alog_api_error(
                        "export_hubspot_custom_object",
                        f"Failed to update batch records: {str(e)}",
                        e,
                    )
                    raise

            try:
                successful_records, failed_records = await create_batch_records(
                    access_token,
                    platform_object_type_id,
                    inputs,
                    error_logger=error_logger,
                )

                # Update task counters for created records
                if task:
                    task.success_number += len(successful_records)
                    task.failed_number += len(failed_records)
                    # Progress: previous progress + (creates portion of remaining progress)
                    create_progress = int((len(inputs) / total_rows) * 25)
                    task.progress = min(90, task.progress + create_progress)
                    await task.asave()

                if failed_records:
                    await error_logger.alog_api_error(
                        "export_hubspot_custom_object",
                        f"Failed to create {len(failed_records)} records",
                    )
                    raise Exception(response)
                await error_logger.alog_info(
                    "export_hubspot_custom_object",
                    f"Successfully created {len(successful_records)} records, failed {len(failed_records)} records",
                )
            except Exception as e:
                await error_logger.alog_api_error(
                    "export_hubspot_custom_object",
                    f"Failed to create batch records: {str(e)}",
                    e,
                )
                raise

            # Collect all successful records for bulk association processing
            all_records_data = []

            # Process created records
            for record in successful_records:
                try:
                    sanka_id = record["properties"].get("sanka_id")
                    platform_id = record.get("id")

                    row = await CustomObjectPropertyRow.objects.aget(id=sanka_id)

                    # Add to bulk processing list
                    all_records_data.append((platform_id, row, sanka_id))

                    try:
                        await CustomObjectPlatforms.objects.aupdate_or_create(
                            custom_object=custom_object,
                            object=row,
                            channel=channel,
                            platform_id=platform_id,
                            platform="hubspot",
                            platform_object_type=platform_object_type_id,
                        )
                    except Exception as e:
                        await error_logger.alog_database_error(
                            "export_hubspot_custom_object",
                            f"Failed to update CustomObjectPlatforms for record {sanka_id}: {str(e)}",
                            e,
                        )
                except Exception as e:
                    error_logger.log_business_logic_error(
                        "export_hubspot_custom_object",
                        f"Failed to process successful record {record.get('id', 'unknown')}: {str(e)}",
                        e,
                    )

            # Process updated records
            for record in successful_updated_records:
                try:
                    sanka_id = record["properties"].get("sanka_id")
                    platform_id = record.get("id")

                    row = await CustomObjectPropertyRow.objects.aget(id=sanka_id)

                    # Add to bulk processing list
                    all_records_data.append((platform_id, row, sanka_id))

                except Exception as e:
                    error_logger.log_business_logic_error(
                        "export_hubspot_custom_object",
                        f"Failed to process updated record {record.get('id', 'unknown')}: {str(e)}",
                        e,
                    )

            # Create all associations in bulk - single API call per mapping type across all records
            if all_records_data and mapping_custom_fields_association:
                association_mappings = parse_association_mappings(
                    mapping_custom_fields_association
                )
                await create_associations_bulk(
                    access_token=access_token,
                    records_data=all_records_data,
                    association_mappings=association_mappings,
                    workspace=workspace,
                    channel=channel,
                    error_logger=error_logger,
                )

            if lang == "ja":
                await Notification.objects.acreate(
                    workspace=workspace,
                    user=user,
                    message=f"{custom_object.name}が Hubspot に正常にエクスポートされました",
                    type="success",
                )
            else:
                await Notification.objects.acreate(
                    workspace=workspace,
                    user=user,
                    message=f"{custom_object.name} successfully exported to Hubspot",
                    type="success",
                )

            if task:
                task.status = "completed"
                task.progress = 100
                await task.asave()
            await error_logger.alog_info(
                "export_hubspot_custom_object", "Export completed successfully"
            )

        except Exception as e:
            await error_logger.alog_api_error(
                "export_hubspot_custom_object", f"Export failed: {str(e)}", e
            )
            if task:
                task.status = "failed"
                # Keep the current progress instead of resetting to 0
                await task.asave()
            logger.error(
                f"[ERROR] Error export hubspot custom object {custom_object.name}, {e}"
            )

            if lang == "ja":
                await Notification.objects.acreate(
                    workspace=workspace,
                    user=user,
                    message=f"Hubspot {custom_object.name} のエクスポートに失敗しました",
                    type="error",
                )
            else:
                await Notification.objects.acreate(
                    workspace=workspace,
                    user=user,
                    message=f"Hubspot {custom_object.name} export failed",
                    type="error",
                )
            raise
        finally:
            # Save error logs to CSV file if any errors occurred
            if error_logger.has_errors() and task:
                try:
                    await error_logger.asave_to_transfer_history(
                        task,
                        f"custom_object_export_errors_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    )
                    await error_logger.alog_info(
                        "export_hubspot_custom_object",
                        f"Saved {error_logger.get_error_count()} errors to CSV file",
                    )
                except Exception as e:
                    print(f"Failed to save error CSV: {str(e)}")
