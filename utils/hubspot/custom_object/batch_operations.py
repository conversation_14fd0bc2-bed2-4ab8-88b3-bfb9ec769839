"""
Async batch operations for HubSpot custom objects.
Provides modular, type-safe functions for batch processing.
"""

import ast
import asyncio
import traceback
from typing import Any, Dict, List, Optional

import aiohttp

from data.constants.properties_constant import (
    TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_CONTACT,
    TYPE_OBJECT_ORDER,
)
from data.models import (
    AssociationLabel,
    AssociationLabelObject,
    Channel,
    CompanyPlatforms,
    ContactsPlatforms,
    CustomObject,
    CustomObjectPlatforms,
    CustomObjectPropertyName,
    CustomObjectPropertyRow,
    CustomObjectPropertyValue,
    ShopTurboOrdersPlatforms,
    TransferHistory,
)
from utils.error_logger.import_export_logger import ImportExportLogger
from utils.logger import logger
from utils.utility import chunks_list

from .models import (
    AssociationMapping,
    PropertyMapping,
)
from ..utils_hubspot import get_custom_object


class BatchResponseHandler:
    """
    Centralized handler for HubSpot batch operation responses
    """

    @staticmethod
    def is_success_status(status_code: int) -> bool:
        """Check if status code indicates success (full or partial)"""
        return status_code in [200, 201, 207]

    @staticmethod
    def is_partial_success(status_code: int) -> bool:
        """Check if status code indicates partial success (multi-status)"""
        return status_code == 207

    @staticmethod
    def is_acceptable_error(error: Dict[str, Any]) -> bool:
        """
        Check if an error is acceptable (e.g., property already exists)
        """
        category = error.get("category", "")
        sub_category = error.get("subCategory", "")

        acceptable_errors = [
            "OBJECT_ALREADY_EXISTS",
            "Properties.PROPERTY_WITH_NAME_EXISTS",
        ]

        return category in acceptable_errors or sub_category in acceptable_errors

    @staticmethod
    def parse_batch_response(
        response_data: Dict[str, Any],
    ) -> tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        Parse batch response and categorize results

        Returns:
            tuple: (successful_results, failed_results, acceptable_errors)
        """
        successful_results = []
        failed_results = []
        acceptable_errors = []

        # Handle successful results
        results = response_data.get("results", [])
        if results:
            successful_results.extend(results)

        # Handle errors
        errors = response_data.get("errors", [])
        for error in errors:
            if BatchResponseHandler.is_acceptable_error(error):
                acceptable_errors.append(error)
            else:
                failed_results.append(error)

        return successful_results, failed_results, acceptable_errors


async def exponential_backoff_retry(
    async_func,
    max_retries: int = 3,
    base_delay: float = 1.0,
    error_logger: Optional[ImportExportLogger] = None,
):
    """
    Retry async function with exponential backoff for rate limiting
    """
    for attempt in range(max_retries):
        try:
            return await async_func()
        except aiohttp.ClientResponseError as e:
            if e.status == 429:  # Rate limited
                if attempt < max_retries - 1:
                    delay = base_delay * (2**attempt)
                    logger.info(
                        f"Rate limited, retrying in {delay} seconds (attempt {attempt + 1}/{max_retries})"
                    )
                    if error_logger:
                        await error_logger.alog_warning(
                            "exponential_backoff_retry",
                            f"Rate limited, retrying in {delay} seconds (attempt {attempt + 1}/{max_retries})",
                        )
                    await asyncio.sleep(delay)
                    continue
            raise
        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            if attempt < max_retries - 1:
                delay = base_delay * (2**attempt)
                logger.info(
                    f"Request failed, retrying in {delay} seconds (attempt {attempt + 1}/{max_retries}): {str(e)}"
                )
                if error_logger:
                    await error_logger.alog_warning(
                        "exponential_backoff_retry",
                        f"Request failed, retrying in {delay} seconds (attempt {attempt + 1}/{max_retries}): {str(e)}",
                    )
                await asyncio.sleep(delay)
                continue
            raise
    raise Exception(f"Max retries ({max_retries}) exceeded")


async def get_all_owners_async(headers: Dict[str, str]) -> List[Dict[str, Any]]:
    """
    Async version of get_all_owners
    """
    async with aiohttp.ClientSession() as session:
        async with session.get(
            "https://api.hubapi.com/crm/v3/owners", headers=headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                return data.get("results", [])
            else:
                logger.error(f"Failed to get owners: {response.status}")
                return []


async def create_bulk_properties(
    access_token: str,
    object_name: str,
    inputs: List[Dict[str, Any]],
    error_logger: Optional[ImportExportLogger] = None,
) -> tuple[bool, Any]:
    """
    Async version of create_bulk_properties
    """

    async def make_request():
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"https://api.hubapi.com/crm/v3/properties/{object_name}/batch/create",
                headers={
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json",
                },
                json={"inputs": inputs},
            ) as response:
                response_data = await response.json()
                return response, response_data

    try:
        if error_logger:
            await error_logger.alog_info(
                "create_bulk_properties",
                f"Creating {len(inputs)} properties for object {object_name}",
            )

        response, response_data = await exponential_backoff_retry(
            make_request, error_logger=error_logger
        )

        if BatchResponseHandler.is_success_status(response.status):
            successful_results, failed_results, acceptable_errors = (
                BatchResponseHandler.parse_batch_response(response_data)
            )

            # Log acceptable errors (properties that already exist)
            if acceptable_errors and error_logger:
                for error in acceptable_errors:
                    property_name = (
                        error.get("context", {}).get("name", ["unknown"])[0]
                        if isinstance(error.get("context", {}).get("name"), list)
                        else error.get("context", {}).get("name", "unknown")
                    )
                    await error_logger.alog_info(
                        "create_bulk_properties",
                        f"Property {property_name} already exists (acceptable)",
                    )

            # Log actual failures
            failed_properties = []
            for error in failed_results:
                property_name = (
                    error.get("context", {}).get("name", ["unknown"])[0]
                    if isinstance(error.get("context", {}).get("name"), list)
                    else error.get("context", {}).get("name", "unknown")
                )
                failed_properties.append({"property": property_name, "error": error})
                if error_logger:
                    await error_logger.alog_api_error(
                        "create_bulk_properties",
                        f"Failed to create property {property_name}: {error}",
                    )

            # Determine success based on whether there are any actual failures
            if not failed_properties:
                success_count = len(successful_results) + len(acceptable_errors)
                if error_logger:
                    if BatchResponseHandler.is_partial_success(response.status):
                        await error_logger.alog_info(
                            "create_bulk_properties",
                            f"Successfully processed {success_count}/{len(inputs)} properties (some already existed)",
                        )
                    else:
                        await error_logger.alog_info(
                            "create_bulk_properties",
                            f"Successfully created all {len(inputs)} properties",
                        )
                return (
                    True,
                    f"Properties processed successfully ({success_count}/{len(inputs)})",
                )
            else:
                error_msg = f"Some properties failed to create: {failed_properties}"
                if error_logger:
                    await error_logger.alog_api_error(
                        "create_bulk_properties", error_msg
                    )
                return False, error_msg
        else:
            error_msg = f"Failed to create properties, status code: {response.status}\n{response_data}"
            if error_logger:
                await error_logger.alog_api_error("create_bulk_properties", error_msg)
            return False, error_msg

    except Exception as e:
        error_msg = f"Error during bulk properties creation: {str(e)}"
        if error_logger:
            await error_logger.alog_api_error("create_bulk_properties", error_msg, e)
        return False, error_msg


async def get_records_object(
    access_token: str,
    object_type_id: str,
    properties: List[str],
    error_logger: Optional[ImportExportLogger] = None,
) -> tuple[bool, Any]:
    """
    Async version of get_records_object with pagination
    """
    if error_logger:
        await error_logger.alog_info(
            "get_records_object", f"Fetching records for object type {object_type_id}"
        )

    start_id = 0
    filters = [{"propertyName": "hs_object_id", "operator": "GT", "value": start_id}]

    inputs = {
        "limit": 100,
        "after": "0",
        "properties": properties,
        "filterGroups": [{"filters": filters}],
        "sorts": [{"propertyName": "hs_object_id", "direction": "ASCENDING"}],
    }

    records = []
    request_count = 0

    async with aiohttp.ClientSession() as session:
        while True:
            request_count += 1
            if error_logger and request_count > 100:  # Safety limit
                error_msg = (
                    f"Too many requests ({request_count}) while fetching records"
                )
                await error_logger.alog_warning("get_records_object", error_msg)
                break

            async def make_request():
                async with session.post(
                    f"https://api.hubapi.com/crm/v3/objects/{object_type_id}/search",
                    headers={
                        "Authorization": f"Bearer {access_token}",
                        "Content-Type": "application/json",
                    },
                    json=inputs,
                ) as response:
                    response_data = await response.json()
                    return response, response_data

            try:
                response, response_data = await exponential_backoff_retry(
                    make_request, error_logger=error_logger
                )

                if response.status == 200:
                    results = response_data.get("results", [])
                    records.extend(results)

                    if results:
                        try:
                            last_hs_object_id = results[-1]["properties"].get(
                                "hs_object_id", start_id
                            )
                            filters[0]["value"] = str(last_hs_object_id)
                            inputs["filterGroups"][0]["filters"] = filters
                        except (KeyError, IndexError) as e:
                            error_msg = f"Error processing pagination data: {str(e)}"
                            if error_logger:
                                await error_logger.alog_api_error(
                                    "get_records_object", error_msg, e
                                )
                            break
                    else:
                        break
                else:
                    error_msg = f"Error: {response.status} - {response_data}"
                    if error_logger:
                        await error_logger.alog_api_error(
                            "get_records_object", error_msg
                        )
                    return False, error_msg

            except Exception as e:
                error_msg = (
                    f"Error during records fetch (request {request_count}): {str(e)}"
                )
                if error_logger:
                    await error_logger.alog_api_error(
                        "get_records_object", error_msg, e
                    )
                return False, error_msg

    if error_logger:
        await error_logger.alog_info(
            "get_records_object",
            f"Successfully fetched {len(records)} records in {request_count} requests",
        )

    return True, records


async def create_batch_records(
    access_token: str,
    object_type: str,
    inputs: List[Dict[str, Any]],
    error_logger: Optional[ImportExportLogger] = None,
) -> tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Async version of create_batch_records
    """
    if error_logger:
        await error_logger.alog_info(
            "create_batch_records",
            f"Creating {len(inputs)} records for object {object_type}",
        )

    successful_records = []
    failed_records = []

    async with aiohttp.ClientSession() as session:
        for chunk_index, chunk in enumerate(chunks_list(inputs, 100)):
            try:
                if error_logger:
                    await error_logger.alog_info(
                        "create_batch_records",
                        f"Processing chunk {chunk_index + 1} with {len(chunk)} records",
                    )

                async def make_request():
                    async with session.post(
                        f"https://api.hubapi.com/crm/v3/objects/{object_type}/batch/create",
                        headers={
                            "Authorization": f"Bearer {access_token}",
                            "Content-Type": "application/json",
                        },
                        json={"inputs": chunk},
                    ) as response:
                        response_data = await response.json()
                        return response, response_data

                response, response_data = await exponential_backoff_retry(
                    make_request, error_logger=error_logger
                )

                if BatchResponseHandler.is_success_status(response.status):
                    successful_results, failed_results, acceptable_errors = (
                        BatchResponseHandler.parse_batch_response(response_data)
                    )

                    # Add successful results
                    if successful_results:
                        successful_records.extend(successful_results)
                        if error_logger:
                            await error_logger.alog_info(
                                "create_batch_records",
                                f"Successfully created {len(successful_results)} records in chunk {chunk_index + 1}",
                            )

                    # Handle acceptable errors (shouldn't typically occur in record creation, but handle gracefully)
                    if acceptable_errors and error_logger:
                        await error_logger.alog_info(
                            "create_batch_records",
                            f"Encountered {len(acceptable_errors)} acceptable errors in chunk {chunk_index + 1}",
                        )

                    # Handle actual failures - add only the failed records to failed_records
                    if failed_results:
                        # For failed results, we need to map back to the original input records
                        # Since we don't have direct mapping, we'll log the failures and mark the entire chunk as problematic
                        if error_logger:
                            await error_logger.alog_api_error(
                                "create_batch_records",
                                f"Failed to create {len(failed_results)} records in chunk {chunk_index + 1}: {failed_results}",
                            )
                        # Add failed records (in practice, this might need more sophisticated mapping)
                        failed_records.extend(
                            [
                                {"input_index": i, "error": err}
                                for i, err in enumerate(failed_results)
                            ]
                        )

                    # Log partial success scenario
                    if (
                        BatchResponseHandler.is_partial_success(response.status)
                        and error_logger
                    ):
                        await error_logger.alog_info(
                            "create_batch_records",
                            f"Partial success in chunk {chunk_index + 1}: {len(successful_results)} created, {len(failed_results)} failed",
                        )

                else:
                    error_msg = f"Error {response.status}: {response_data}"
                    if error_logger:
                        await error_logger.alog_api_error(
                            "create_batch_records",
                            f"Failed to create records in chunk {chunk_index + 1}: {error_msg}",
                        )
                    failed_records.extend(chunk)

            except Exception as e:
                error_msg = f"Unexpected error in chunk {chunk_index + 1}: {str(e)}"
                if error_logger:
                    await error_logger.alog_api_error(
                        "create_batch_records", error_msg, e
                    )
                logger.error(f"Error in create_batch_records: {e}")
                failed_records.extend(chunk)

    if error_logger:
        await error_logger.alog_info(
            "create_batch_records",
            f"Completed batch creation: {len(successful_records)} successful, {len(failed_records)} failed",
        )

    return successful_records, failed_records


async def update_batch_records(
    access_token: str,
    object_name: str,
    inputs: List[Dict[str, Any]],
    is_update: bool = False,
    error_logger: Optional[ImportExportLogger] = None,
) -> tuple[bool, Any]:
    """
    Async version of update_batch_records
    """
    if error_logger:
        await error_logger.alog_info(
            "update_batch_records",
            f"Updating {len(inputs)} records for object {object_name}",
        )

    url = f"https://api.hubapi.com/crm/v3/objects/{object_name}/batch/upsert"
    if is_update:
        url = f"https://api.hubapi.com/crm/v3/objects/{object_name}/batch/update"

    async def make_request():
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url,
                headers={
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json",
                },
                json={"inputs": inputs},
            ) as response:
                response_data = await response.json()
                return response, response_data

    try:
        response, response_data = await exponential_backoff_retry(
            make_request, error_logger=error_logger
        )

        if BatchResponseHandler.is_success_status(response.status):
            successful_results, failed_results, acceptable_errors = (
                BatchResponseHandler.parse_batch_response(response_data)
            )

            # Process successful results - filter by presence of "id" field
            successful_records = []
            additional_failed = []

            for result in successful_results:
                if "id" in result:
                    successful_records.append(result)
                else:
                    additional_failed.append(result)

            # Combine failed results
            all_failed_records = failed_results + additional_failed

            # Log acceptable errors
            if acceptable_errors and error_logger:
                await error_logger.alog_info(
                    "update_batch_records",
                    f"Encountered {len(acceptable_errors)} acceptable errors",
                )

            # Log results
            if error_logger:
                if BatchResponseHandler.is_partial_success(response.status):
                    await error_logger.alog_info(
                        "update_batch_records",
                        f"Partial success: {len(successful_records)} updated, {len(all_failed_records)} failed",
                    )
                else:
                    await error_logger.alog_info(
                        "update_batch_records",
                        f"Successfully updated {len(successful_records)} records",
                    )

                if all_failed_records:
                    await error_logger.alog_warning(
                        "update_batch_records",
                        f"Failed to update {len(all_failed_records)} records: {all_failed_records}",
                    )

            return True, successful_records
        else:
            error_msg = f"Failed update data to hubspot. Error {response.status},{response_data}"
            if error_logger:
                await error_logger.alog_api_error("update_batch_records", error_msg)
            return False, error_msg

    except Exception as e:
        error_msg = f"Error during batch update: {str(e)}"
        if error_logger:
            await error_logger.alog_api_error("update_batch_records", error_msg, e)
        return False, error_msg


async def prepare_custom_object_properties(
    row: CustomObjectPropertyRow,
    mapping_custom_fields: Dict[str, str],
    workspace,
    owners: List[Dict[str, Any]],
    custom_object: CustomObject,
    error_logger: Optional[ImportExportLogger] = None,
    required_properties: List[str] = None,
    property_types: Dict[str, str] = None,
) -> Dict[str, Any]:
    """
    Prepare custom object properties for HubSpot API
    """
    properties = {}

    # Always inject sanka_id for tracking (matches other export patterns)
    properties["sanka_id"] = str(row.id)

    # Auto-inject required ID properties that aren't explicitly mapped
    if required_properties and property_types:
        # Get explicitly mapped property names
        explicitly_mapped_properties = set()
        for hubspot_mapping in mapping_custom_fields.keys():
            if "|" in hubspot_mapping:
                property_name = hubspot_mapping.split("|")[0]
                explicitly_mapped_properties.add(property_name)

        # Auto-inject missing required ID properties
        for required_prop in required_properties:
            if (
                required_prop in ["id", "sanka_id"]
                and required_prop not in explicitly_mapped_properties
            ):
                prop_type = property_types.get(required_prop, "string")

                if required_prop == "id":
                    if prop_type == "number":
                        # Use row_id for number type, skip if null/empty/0
                        if row.row_id and row.row_id != 0:
                            properties["id"] = row.row_id
                        else:
                            # Skip this record if required number ID is null/empty/0
                            # This will be handled at the calling level
                            if error_logger:
                                await error_logger.alog_info(
                                    "prepare_custom_object_properties",
                                    f"Skipping record {row.id} - required 'id' field is number type but row_id is null/empty/0",
                                )
                    else:
                        # Use string ID for text type
                        properties["id"] = str(row.id)

                    if error_logger:
                        await error_logger.alog_info(
                            "prepare_custom_object_properties",
                            f"Auto-injected required property '{required_prop}' ({prop_type}) with value: {properties.get(required_prop, 'SKIPPED')}",
                        )

    try:
        for hubspot_property, sanka_property in mapping_custom_fields.items():
            try:
                if "|" not in hubspot_property:
                    continue

                property_mapping = PropertyMapping.from_hubspot_string(
                    hubspot_property, sanka_property
                )

                if sanka_property == "id":
                    properties[property_mapping.hubspot_property_name] = str(row.id)
                    continue
                elif sanka_property == "row_id":
                    # Handle row_id for number type ID fields, skip if null/empty/0
                    if row.row_id and row.row_id != 0:
                        properties[property_mapping.hubspot_property_name] = row.row_id
                    # Skip this record if row_id is null/empty/0 - will be handled at the record level
                    continue

                custom_field_name = await CustomObjectPropertyName.objects.filter(
                    workspace=workspace,
                    custom_object=custom_object,
                    name=sanka_property,
                ).afirst()

                if custom_field_name:
                    (
                        custom_value,
                        _,
                    ) = await CustomObjectPropertyValue.objects.aget_or_create(
                        field_name=custom_field_name, object=row
                    )

                    if custom_value:
                        # Handle different field types
                        if custom_value.value and custom_field_name.type == "tag":
                            tag_values = await asyncio.to_thread(
                                ast.literal_eval, custom_value.value
                            )
                            properties[property_mapping.hubspot_property_name] = (
                                ",".join([tag["value"] for tag in tag_values])
                            )
                        elif custom_value.value_number:
                            properties[property_mapping.hubspot_property_name] = (
                                custom_value.value_number
                            )
                        elif custom_value.value_time:
                            properties[property_mapping.hubspot_property_name] = (
                                custom_value.value_time.isoformat()
                            )
                        elif custom_value.value:
                            # Handle hubspot_owner_id mapping
                            if (
                                property_mapping.hubspot_property_name
                                == "hubspot_owner_id"
                            ):
                                for owner in owners:
                                    if owner["email"] == custom_value.value:
                                        properties[
                                            property_mapping.hubspot_property_name
                                        ] = int(owner["id"])
                                        break
                            else:
                                properties[property_mapping.hubspot_property_name] = (
                                    custom_value.value
                                )
                        else:
                            properties[property_mapping.hubspot_property_name] = ""

                        if properties[property_mapping.hubspot_property_name] in [
                            "True",
                            "TRUE",
                        ]:
                            properties[property_mapping.hubspot_property_name] = "true"
                        elif properties[property_mapping.hubspot_property_name] in [
                            "False",
                            "FALSE",
                        ]:
                            properties[property_mapping.hubspot_property_name] = "false"

            except Exception as e:
                if error_logger:
                    await error_logger.alog_validation_error(
                        "prepare_custom_object_properties",
                        f"Failed to map custom field {hubspot_property} for row {row.id}: {str(e)}",
                        e,
                    )
                logger.error(f"Failed to process property {hubspot_property}: {e}")

    except Exception as e:
        if error_logger:
            await error_logger.alog_business_logic_error(
                "prepare_custom_object_properties",
                f"Error preparing properties for row {row.id}: {str(e)}",
                e,
            )
        logger.error(f"Error preparing custom object properties: {e}")

    return properties


async def validate_and_prepare_schema_mapping(
    access_token: str,
    platform_object_type_id: str,
    mapping_custom_fields: Dict[str, str],
    error_logger: Optional[ImportExportLogger] = None,
) -> tuple[bool, Dict[str, str], List[str], List[str], Dict[str, str]]:
    """
    Validate that all non-ID required properties are mapped and return schema data.

    Args:
        access_token: HubSpot access token
        platform_object_type_id: HubSpot custom object type ID
        mapping_custom_fields: Current field mappings
        error_logger: Optional error logger

    Returns:
        tuple: (is_valid, updated_mapping, missing_required_properties, required_properties, property_types)
    """
    if error_logger:
        await error_logger.alog_info(
            "validate_and_prepare_schema_mapping",
            f"Validating schema for custom object {platform_object_type_id}",
        )

    try:
        # Get full schema from HubSpot
        ok, schema_data = await asyncio.to_thread(
            get_custom_object, access_token, platform_object_type_id
        )
        if not ok:
            error_msg = (
                f"Failed to fetch schema for {platform_object_type_id}: {schema_data}"
            )
            if error_logger:
                await error_logger.alog_api_error(
                    "validate_and_prepare_schema_mapping", error_msg
                )
            return False, mapping_custom_fields, []

        required_properties = schema_data.get("requiredProperties", [])
        properties = schema_data.get("properties", [])

        if error_logger:
            await error_logger.alog_info(
                "validate_and_prepare_schema_mapping",
                f"Found {len(required_properties)} required properties: {required_properties}",
            )

        # Create property name to type mapping
        property_types = {}
        for prop in properties:
            property_types[prop["name"]] = prop.get("type", "string")

        # Get currently mapped HubSpot properties
        mapped_hubspot_properties = set()
        for hubspot_mapping in mapping_custom_fields.keys():
            if "|" in hubspot_mapping:
                # Extract property name from mapping format: "property_name|type|id|group"
                property_name = hubspot_mapping.split("|")[0]
                mapped_hubspot_properties.add(property_name)

        # Check for missing required properties (excluding ID fields that will be auto-injected)
        missing_required = []
        updated_mapping = mapping_custom_fields.copy()

        for required_prop in required_properties:
            if required_prop not in mapped_hubspot_properties:
                # ID fields will be auto-injected, so don't require explicit mapping
                if required_prop not in ["id", "sanka_id"]:
                    missing_required.append(required_prop)
                elif error_logger:
                    # Log that ID field will be auto-injected
                    prop_type = property_types.get(required_prop, "string")
                    await error_logger.alog_info(
                        "validate_and_prepare_schema_mapping",
                        f"Required property '{required_prop}' ({prop_type}) will be auto-injected",
                    )

        # Check if all required properties are now mapped
        is_valid = len(missing_required) == 0

        if not is_valid and error_logger:
            await error_logger.alog_validation_error(
                "validate_and_prepare_schema_mapping",
                f"Missing required property mappings: {missing_required}. "
                f"You need to map Sanka fields to these HubSpot properties to run the export successfully.",
            )
        elif is_valid and error_logger:
            await error_logger.alog_info(
                "validate_and_prepare_schema_mapping",
                f"Schema validation passed. All {len(required_properties)} required properties are mapped.",
            )

        return (
            is_valid,
            updated_mapping,
            missing_required,
            required_properties,
            property_types,
        )

    except Exception as e:
        error_msg = f"Error during schema validation: {str(e)}"
        if error_logger:
            await error_logger.alog_api_error(
                "validate_and_prepare_schema_mapping", error_msg, e
            )
        logger.error(f"Schema validation error: {e}")
        return False, mapping_custom_fields, [], [], {}


async def get_association_types_for_objects(
    access_token: str,
    from_object_type: str,
    to_object_type: str,
    error_logger: Optional[ImportExportLogger] = None,
) -> tuple[bool, List[Dict[str, Any]]]:
    """
    Get valid association types for a specific object pair from HubSpot v4 API.
    
    Args:
        access_token: HubSpot access token
        from_object_type: Source object type ID (e.g., "2-47982367")
        to_object_type: Target object type ID (e.g., "2-48151335")
        error_logger: Optional error logger
        
    Returns:
        tuple: (success, list of association types with category and typeId)
    """
    url = f"https://api.hubapi.com/crm/v4/associations/{from_object_type}/{to_object_type}/labels"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }
    
    if error_logger:
        await error_logger.alog_info(
            "get_association_types_for_objects",
            f"Fetching association types for {from_object_type} -> {to_object_type}",
        )
    
    async def make_request():
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                response_data = await response.json()
                return response, response_data
    
    try:
        response, response_data = await exponential_backoff_retry(
            make_request, error_logger=error_logger
        )
        
        if response.status == 200:
            results = response_data.get("results", [])
            if error_logger:
                await error_logger.alog_info(
                    "get_association_types_for_objects",
                    f"Found {len(results)} valid association types for {from_object_type} -> {to_object_type}",
                )
            
            # Log available association types for debugging
            logger.info(f"Available association types for {from_object_type} -> {to_object_type}: {results}")
            
            return True, results
        else:
            error_msg = f"Failed to fetch association types: {response.status} - {response_data}"
            if error_logger:
                await error_logger.alog_api_error(
                    "get_association_types_for_objects", error_msg
                )
            logger.error(error_msg)
            return False, []
            
    except Exception as e:
        error_msg = f"Error fetching association types: {str(e)}"
        if error_logger:
            await error_logger.alog_api_error(
                "get_association_types_for_objects", error_msg, e
            )
        logger.error(error_msg)
        return False, []


async def find_valid_association_type(
    access_token: str,
    from_object_type: str,
    to_object_type: str,
    requested_type_id: int,
    error_logger: Optional[ImportExportLogger] = None,
) -> tuple[bool, Optional[Dict[str, Any]]]:
    """
    Find a valid association type for the object pair, preferring the requested type if available.
    
    Args:
        access_token: HubSpot access token
        from_object_type: Source object type ID
        to_object_type: Target object type ID
        requested_type_id: Originally requested association type ID
        error_logger: Optional error logger
        
    Returns:
        tuple: (success, association_type_info or None)
    """
    success, available_types = await get_association_types_for_objects(
        access_token, from_object_type, to_object_type, error_logger
    )
    
    if not success or not available_types:
        if error_logger:
            await error_logger.alog_api_error(
                "find_valid_association_type",
                f"No association types available for {from_object_type} -> {to_object_type}",
            )
        return False, None
    
    # First, try to find the exact requested type
    for assoc_type in available_types:
        if assoc_type.get("typeId") == requested_type_id:
            if error_logger:
                await error_logger.alog_info(
                    "find_valid_association_type",
                    f"Found exact match for requested association type {requested_type_id}",
                )
            return True, assoc_type
    
    # If exact match not found, use the first available type (usually the primary association)
    if available_types:
        fallback_type = available_types[0]
        if error_logger:
            await error_logger.alog_warning(
                "find_valid_association_type",
                f"Requested association type {requested_type_id} not found. Using fallback type {fallback_type.get('typeId')} ({fallback_type.get('label', 'Unknown')})",
            )
        logger.warning(
            f"Association type {requested_type_id} not valid for {from_object_type} -> {to_object_type}. "
            f"Using fallback: {fallback_type.get('typeId')} ({fallback_type.get('label', 'Unknown')})"
        )
        return True, fallback_type
    
    return False, None


async def update_progress(
    task: Optional[TransferHistory],
    processed: int,
    total: int,
    success: int,
    failed: int,
):
    """
    Update the progress and counters in TransferHistory model
    """
    if task:
        task.progress = min(int((processed / total) * 100), 100) if total > 0 else 0
        task.success_number = success
        task.failed_number = failed
        await task.asave()


async def create_associations_bulk(
    access_token: str,
    records_data: List[tuple],  # List of (platform_id, row, sanka_id) tuples
    association_mappings: List[AssociationMapping],
    workspace,
    channel: Channel,
    error_logger: Optional[ImportExportLogger] = None,
):
    """
    Create associations for multiple custom object records in optimized bulk operations.
    Groups associations by mapping type and makes one API call per mapping across all records.
    
    UPDATED: Now uses HubSpot v4 Associations API with proper associationCategory and 
    associationTypeId format instead of deprecated v3 API.

    Args:
        access_token: HubSpot access token
        records_data: List of tuples containing (platform_id, row, sanka_id) for each record
        association_mappings: List of association mappings to process
        workspace: Workspace instance
        channel: Channel instance
        error_logger: Optional error logger
    """
    if not records_data or not association_mappings:
        return

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }

    if error_logger:
        await error_logger.alog_info(
            "create_associations_bulk",
            f"Processing bulk associations for {len(records_data)} records with {len(association_mappings)} mapping types",
        )

    async with aiohttp.ClientSession() as session:
        # Process each association mapping type
        for mapping in association_mappings:
            logger.info(
                f"Processing bulk association mapping {mapping.sanka_association_id}"
            )

            try:
                association_label = await AssociationLabel.objects.filter(
                    id=mapping.sanka_association_id
                ).afirst()

                if not association_label:
                    continue

                if error_logger:
                    await error_logger.alog_info(
                        "create_associations_bulk",
                        f"Processing bulk association mapping {mapping.sanka_association_id} for {len(records_data)} records",
                    )

                # Validate and get correct association type for this object pair
                requested_type_id = int(mapping.hubspot_association_id)
                success, valid_association_type = await find_valid_association_type(
                    access_token,
                    mapping.hubspot_association_from_object_type_id,
                    mapping.hubspot_association_to_object_type_id,
                    requested_type_id,
                    error_logger,
                )

                if not success or not valid_association_type:
                    error_msg = f"No valid association type found for mapping {mapping.sanka_association_id} ({mapping.hubspot_association_from_object_type_id} -> {mapping.hubspot_association_to_object_type_id})"
                    if error_logger:
                        await error_logger.alog_api_error(
                            "create_associations_bulk", error_msg
                        )
                    logger.error(error_msg)
                    continue

                # Use the validated association type
                validated_type_id = valid_association_type.get("typeId")
                association_category = valid_association_type.get("category", "HUBSPOT_DEFINED")
                
                if error_logger:
                    await error_logger.alog_info(
                        "create_associations_bulk",
                        f"Using validated association type {validated_type_id} (category: {association_category}) for mapping {mapping.sanka_association_id}",
                    )

                # Collect all associations for this mapping type across all records
                all_batch_inputs = []

                for platform_id, row, sanka_id in records_data:
                    try:
                        # Get associations for this specific record and mapping
                        selected_associations = await asyncio.to_thread(
                            lambda: list(
                                AssociationLabelObject.get_for_object(
                                    row, workspace, association_label
                                ).values_list("target_object_id", flat=True)
                            )
                        )

                        # Get platform IDs for the associations
                        assoc_platform_ids = []
                        for selected_association in selected_associations:
                            platform_obj = None

                            if (
                                TYPE_OBJECT_COMPANY
                                in association_label.object_target.split(",")
                                and mapping.hubspot_association_to_object_type_id
                                == "0-2"
                            ):
                                platform_obj = await CompanyPlatforms.objects.filter(
                                    channel=channel,
                                    company__id=selected_association,
                                ).afirst()
                                if platform_obj and platform_obj.platform_id:
                                    assoc_platform_ids.append(platform_obj.platform_id)

                            elif (
                                TYPE_OBJECT_CONTACT
                                in association_label.object_target.split(",")
                                and mapping.hubspot_association_to_object_type_id
                                == "0-1"
                            ):
                                platform_obj = await ContactsPlatforms.objects.filter(
                                    channel=channel,
                                    contact__id=selected_association,
                                ).afirst()
                                if platform_obj and platform_obj.platform_id:
                                    assoc_platform_ids.append(platform_obj.platform_id)

                            elif (
                                TYPE_OBJECT_ORDER
                                in association_label.object_target.split(",")
                                and mapping.hubspot_association_to_object_type_id
                                == "0-3"
                            ):
                                platform_obj = (
                                    await ShopTurboOrdersPlatforms.objects.filter(
                                        channel=channel,
                                        order__id=selected_association,
                                    ).afirst()
                                )
                                if platform_obj and platform_obj.platform_order_id:
                                    assoc_platform_ids.append(
                                        platform_obj.platform_order_id
                                    )

                            else:
                                platform_obj = (
                                    await CustomObjectPlatforms.objects.filter(
                                        channel=channel,
                                        object__id=selected_association,
                                    ).afirst()
                                )
                                if platform_obj and platform_obj.platform_id:
                                    assoc_platform_ids.append(platform_obj.platform_id)

                        # Add associations for this record to the bulk batch using validated type
                        for assoc_platform_id in assoc_platform_ids:
                            all_batch_inputs.append(
                                {
                                    "from": {"id": str(platform_id)},
                                    "to": {"id": str(assoc_platform_id)},
                                    "type": str(validated_type_id),
                                }
                            )

                    except Exception as e:
                        if error_logger:
                            await error_logger.alog_business_logic_error(
                                "create_associations_bulk",
                                f"Failed to process associations for record {sanka_id}: {str(e)}",
                                e,
                            )
                        logger.error(
                            f"Error processing associations for record {sanka_id}: {e}"
                        )
                        continue

                # Make single bulk API call for this mapping type across all records
                if all_batch_inputs:
                    # Convert v3 format to v4 format for associations using validated association type
                    v4_batch_inputs = []
                    for batch_input in all_batch_inputs:
                        association_type_id = int(batch_input["type"])
                        
                        v4_batch_inputs.append({
                            "from": batch_input["from"],
                            "to": batch_input["to"], 
                            "types": [{
                                "associationCategory": association_category,
                                "associationTypeId": association_type_id
                            }]
                        })
                    
                    url = f"https://api.hubapi.com/crm/v4/associations/{mapping.hubspot_association_from_object_type_id}/{mapping.hubspot_association_to_object_type_id}/batch/create"
                    logger.info(f"URL: {url}")
                    data = {"inputs": v4_batch_inputs}
                    logger.info(f"Data: {data}")

                    logger.info(
                        f"Creating {len(v4_batch_inputs)} associations in single bulk call for mapping {mapping.sanka_association_id}"
                    )

                    async def make_request():
                        async with session.post(
                            url, headers=headers, json=data
                        ) as response:
                            response_data = await response.json()
                            return response, response_data

                    try:
                        response, response_data = await exponential_backoff_retry(
                            make_request, error_logger=error_logger
                        )

                        if (
                            BatchResponseHandler.is_success_status(response.status)
                            or response.status == 204
                        ):
                            # Handle v4 API response format
                            if response.status == 201 or response.status == 200:
                                # v4 API typically returns 201 for successful creation
                                successful_count = len(v4_batch_inputs)
                                if response_data and 'results' in response_data:
                                    successful_count = len(response_data.get('results', []))
                                
                                logger.info(
                                    f"Successfully bulk associated {successful_count} associations for mapping {mapping.sanka_association_id} using v4 API"
                                )
                                if error_logger:
                                    await error_logger.alog_info(
                                        "create_associations_bulk",
                                        f"Successfully created {successful_count} associations for mapping {mapping.sanka_association_id} using v4 API",
                                    )
                            elif BatchResponseHandler.is_partial_success(response.status):
                                (
                                    successful_results,
                                    failed_results,
                                    acceptable_errors,
                                ) = BatchResponseHandler.parse_batch_response(
                                    response_data
                                )

                                if error_logger:
                                    if failed_results:
                                        await error_logger.alog_api_error(
                                            "create_associations_bulk",
                                            f"Partial success bulk mapping {mapping.sanka_association_id}: {len(successful_results)} success, {len(failed_results)} failed",
                                        )
                                    if acceptable_errors:
                                        await error_logger.alog_info(
                                            "create_associations_bulk",
                                            f"Bulk mapping {mapping.sanka_association_id}: {len(acceptable_errors)} acceptable errors",
                                        )

                                logger.info(
                                    f"Partially bulk associated mapping {mapping.sanka_association_id}: {len(successful_results)} success, {len(failed_results)} failed"
                                )
                            else:
                                logger.info(
                                    f"Successfully bulk associated {len(v4_batch_inputs)} associations for mapping {mapping.sanka_association_id}"
                                )
                                if error_logger:
                                    await error_logger.alog_info(
                                        "create_associations_bulk",
                                        f"Successfully created {len(v4_batch_inputs)} associations for mapping {mapping.sanka_association_id}",
                                    )
                        else:
                            error_msg = f"Failed to bulk associate mapping {mapping.sanka_association_id}: Status {response.status}, Response: {response_data}"
                            if error_logger:
                                await error_logger.alog_api_error(
                                    "create_associations_bulk", error_msg
                                )
                            logger.error(error_msg)

                    except Exception as e:
                        error_msg = f"Error during bulk association API call for mapping {mapping.sanka_association_id}: {str(e)}"
                        if error_logger:
                            await error_logger.alog_api_error(
                                "create_associations_bulk", error_msg, e
                            )
                        logger.error(error_msg)

                else:
                    logger.info(
                        f"No associations found for mapping {mapping.sanka_association_id}"
                    )

            except Exception as e:
                tb_str = traceback.format_exc()
                error_msg = f"Failed to process bulk association mapping {mapping.sanka_association_id}: {str(e)}"
                detailed_error = f"{error_msg}\n\nFull traceback:\n{tb_str}"

                if error_logger:
                    await error_logger.alog_api_error(
                        "create_associations_bulk", detailed_error, e
                    )
                logger.error(
                    f"Exception in bulk association processing: {detailed_error}"
                )

        if error_logger:
            await error_logger.alog_info(
                "create_associations_bulk",
                f"Completed bulk association processing for {len(records_data)} records",
            )
