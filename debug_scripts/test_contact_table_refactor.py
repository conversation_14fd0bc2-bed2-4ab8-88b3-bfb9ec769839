#!/usr/bin/env python3
"""
Test script to validate the contact table refactoring changes.
This script tests that the refactored templates render correctly.
"""

import os
import sys
import django

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sanka.settings')
django.setup()

from django.template import Template, Context
from django.template.loader import get_template
from data.models import Workspace, Contact, User, Verification, UserManagement

def test_contact_table_templates():
    """Test the refactored contact table templates"""

    print("="*60)
    print(" TESTING CONTACT TABLE REFACTOR")
    print("="*60)

    # Test template loading without database data
    print(f"✓ Testing template loading and syntax validation")
    
    # Test 1: Template loading validation
    print(f"\n" + "="*40)
    print(" TEST 1: Template Loading Validation")
    print("="*40)

    templates_to_test = [
        'data/contacts/contact-row-partial.html',
        'data/utility/table-row-loading.html',
        'data/utility/table-column-header.html',
        'data/utility/table-datatables-config.html',
        'data/utility/table-container-wrapper.html'
    ]

    for template_path in templates_to_test:
        try:
            template = get_template(template_path)
            print(f"✓ {template_path} loads successfully")
        except Exception as e:
            print(f"❌ {template_path} failed to load: {e}")
            return False
    
    # Test 2: Template syntax validation
    print(f"\n" + "="*40)
    print(" TEST 2: Template Syntax Validation")
    print("="*40)

    # Test basic rendering with minimal context
    try:
        template = get_template('data/utility/table-row-loading.html')
        context = {'object_id': 123}
        result = template.render(context)
        print(f"✓ Table row loading template renders successfully")
        if "row_load-123" in result:
            print(f"✓ Loading spinner properly configured")
    except Exception as e:
        print(f"❌ Table row loading template failed: {e}")
        return False

    try:
        # Create a mock request object
        from django.test import RequestFactory
        factory = RequestFactory()
        mock_request = factory.get('/')
        mock_request.user = None

        template = get_template('data/utility/table-column-header.html')
        context = {'column': 'contact_id', 'object_type': 'contacts', 'request': mock_request}
        result = template.render(context)
        print(f"✓ Table column header template renders successfully")
        if 'column-id-size' in result:
            print(f"✓ Column sizing classes applied correctly")
    except Exception as e:
        print(f"❌ Table column header template failed: {e}")
        return False

    try:
        template = get_template('data/utility/table-datatables-config.html')
        context = {'table_class': 'contacts-table', 'LANGUAGE_CODE': 'en'}
        result = template.render(context)
        print(f"✓ DataTables config template renders successfully")
        if 'initializeDataTable' in result:
            print(f"✓ DataTables initialization function found")
        if 'fixedColumns' in result:
            print(f"✓ Fixed columns configuration found")
    except Exception as e:
        print(f"❌ DataTables config template failed: {e}")
        return False
    
    print(f"\n" + "="*60)
    print(" CONTACT TABLE REFACTOR TEST COMPLETE")
    print("="*60)
    print(f"✅ All template tests passed!")
    print(f"✅ The refactored contact table should work correctly.")
    print(f"\n📋 Summary of changes:")
    print(f"  • Contact table now uses shared utility templates")
    print(f"  • DataTables configuration matches order table patterns")
    print(f"  • Consistent styling and structure across tables")
    print(f"  • Improved maintainability and reusability")
    
    return True

if __name__ == "__main__":
    success = test_contact_table_templates()
    sys.exit(0 if success else 1)
